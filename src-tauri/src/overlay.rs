use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{command, AppHandle, Manager};

// 覆盖层窗口信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OverlayInfo {
    pub id: String,
    pub window_type: OverlayType,
    pub created_at: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum OverlayType {
    ScreenshotOverlay,
    PinnedImage,
}

// 全局覆盖层管理器
lazy_static::lazy_static! {
    static ref OVERLAY_MANAGER: Arc<Mutex<HashMap<String, OverlayInfo>>> = Arc::new(Mutex::new(HashMap::new()));
}

/// 关闭覆盖层窗口
#[command]
pub async fn close_overlay(app_handle: AppHandle, overlay_id: String) -> Result<(), String> {
    println!("[OVERLAY] Closing overlay: {}", overlay_id);

    // 获取窗口并关闭
    if let Some(window) = app_handle.get_webview_window(&overlay_id) {
        if let Err(e) = window.close() {
            return Err(format!("Failed to close overlay window: {}", e));
        }
    }

    // 从管理器中移除
    if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
        manager.remove(&overlay_id);
    }

    println!("[OVERLAY] Overlay closed successfully: {}", overlay_id);
    Ok(())
}

/// 获取所有活动的覆盖层
#[command]
pub async fn list_active_overlays() -> Result<Vec<OverlayInfo>, String> {
    match OVERLAY_MANAGER.lock() {
        Ok(manager) => {
            let overlays: Vec<OverlayInfo> = manager.values().cloned().collect();
            Ok(overlays)
        }
        Err(e) => Err(format!("Failed to access overlay manager: {}", e)),
    }
}
