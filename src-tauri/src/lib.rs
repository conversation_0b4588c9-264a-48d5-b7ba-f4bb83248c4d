mod modules;
mod overlay;
mod screenshot;

use chrono;
use tauri::Manager;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(
            // 在macOS系统下，日志文件默认保存在:
            // ~/Library/Logs/com.mecap.dev/mecap.log (开发模式)
            // ~/Library/Logs/com.mecap/mecap.log (生产模式)
            tauri_plugin_log::Builder::new()
                .targets([
                    tauri_plugin_log::Target::new(
                        tauri_plugin_log::TargetKind::LogDir {
                            file_name: Some("mecap".to_string()) // 显式指定.log后缀
                        }
                    ),
                    tauri_plugin_log::Target::new(tauri_plugin_log::TargetKind::Stdout),
                    tauri_plugin_log::Target::new(tauri_plugin_log::TargetKind::Webview),
                ])
                .level(if cfg!(debug_assertions) {
                    log::LevelFilter::Debug  // 开发模式：显示详细日志
                } else {
                    log::LevelFilter::Info   // 生产模式：只显示重要信息
                })
                .format(|out, message, record| {
                    out.finish(format_args!(
                        "[{}][{}][{}] {}",
                        chrono::Local::now().format("%Y-%m-%d %H:%M:%S%.3f"),
                        record.target(),
                        record.level(),
                        message
                    ))
                })
                .build()
        )
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(
            tauri_plugin_global_shortcut::Builder::new()
                .with_handler(|app_handle, shortcut, event| {
                    use tauri_plugin_global_shortcut::{Code, Shortcut, ShortcutState};

                    // 检查是否是ESC键
                    let esc_shortcut = Shortcut::new(None, Code::Escape);
                    if shortcut == &esc_shortcut {
                        match event.state() {
                            ShortcutState::Pressed => {
                                println!("[GLOBAL-SHORTCUT] 🎯 ESC key pressed globally - triggering exit");

                                // 异步调用退出函数
                                let app_handle_clone = app_handle.clone();
                                tauri::async_runtime::spawn(async move {
                                    if let Err(e) = modules::ux::exit_capture_completely_direct(app_handle_clone).await {
                                        println!("[GLOBAL-SHORTCUT] ❌ Failed to exit capture: {}", e);
                                    } else {
                                        println!("[GLOBAL-SHORTCUT] ✅ Successfully exited capture via global shortcut");
                                    }
                                });
                            }
                            ShortcutState::Released => {
                                // ESC键释放，不需要处理
                            }
                        }
                    }
                })
                .build()
        )
        .invoke_handler(tauri::generate_handler![
            // 基础截图功能
            screenshot::capture_screenshot,

            // 窗口检测功能
            modules::window::detect_window_under_mouse,

            // Phase 1: 新的模块化命令
            modules::capture::capture_region_new,
            modules::capture::capture_window_at_coordinates, // 🔧 BUG FIX: 基于坐标的窗口截图
            modules::capture::capture_fullscreen,
            modules::capture::get_capture_config,
            modules::window::list_windows_new,
            modules::window::refresh_window_list,
            modules::window::get_window_info_new,
            modules::window::validate_window_exists_new,
            modules::window::clear_window_cache_new,
            modules::window::detect_window_under_mouse_realtime,
            modules::window::benchmark_window_detection,
            modules::window::start_smart_window_detection,
            modules::window::stop_smart_window_detection,
            modules::window::detect_window_under_cursor,
            modules::window::get_window_details,
            modules::window::capture_selected_window,
            modules::window::get_window_detection_config,
            modules::window::update_window_detection_config,
            // 混合截图模块命令
            modules::hybrid_screenshot::start_hybrid_screenshot,
            modules::hybrid_screenshot::capture_region_from_overlay,
            modules::hybrid_screenshot::capture_region_with_tool_selection,
            modules::hybrid_screenshot::trigger_region_capture_from_toolbar,
            modules::hybrid_screenshot::close_hybrid_screenshot,
            modules::hybrid_screenshot::capture_window_by_info,
            modules::hybrid_screenshot::get_current_monitors,
            modules::hybrid_screenshot::detect_window_smart,
            // Windows平台特定命令
            #[cfg(target_os = "windows")]
            modules::hybrid_screenshot::detect_window_enhanced_windows,
            // macOS平台特定命令
            #[cfg(target_os = "macos")]
            modules::hybrid_screenshot::check_macos_permissions,
            #[cfg(target_os = "macos")]
            modules::hybrid_screenshot::request_macos_permissions,
            modules::recording::start_recording,
            modules::recording::stop_recording,
            modules::recording::pause_recording,
            modules::recording::resume_recording,
            modules::recording::get_recording_status,
            modules::recording::check_recording_capabilities,
            modules::recording::get_recommended_recording_config,
            modules::overlay::create_modern_overlay,
            modules::overlay::close_overlay_new,
            modules::overlay::list_active_overlays_new,
            modules::overlay::update_overlay_properties,
            modules::overlay::get_overlay_performance_stats,
            modules::overlay::ping_test,
            modules::overlay::create_fullscreen_overlay_manager,
            modules::overlay::close_all_overlays,
            modules::overlay::hide_all_overlays,
            modules::overlay::get_overlay_compatibility_info,
            // 🔧 LOG REFACTOR: 移除自定义前端日志命令，使用Tauri log插件
            // 混合截图功能
            modules::overlay::create_multi_monitor_screenshot_overlay,
            modules::overlay::close_all_screenshot_overlays,
            // Phase 1: P1-T4 高级覆盖层UI功能
            modules::overlay::handle_region_selection_complete,
            modules::overlay::create_window_highlight_overlay,
            modules::overlay::handle_window_selection_complete,
            modules::overlay::create_screenshot_editor_window,
            modules::overlay::create_screenshot_editor_with_file,
            modules::overlay::create_screenshot_preview_window,
            modules::overlay::create_annotation_window, // 🆕 注解窗口创建
            modules::overlay::save_annotated_screenshot, // 🆕 保存注解截图
            modules::overlay::copy_annotated_screenshot, // 🆕 复制注解截图
            modules::overlay::read_screenshot_file,
            modules::overlay::show_main_window,
            // 🆕 独立工具栏窗口管理
            modules::independent_toolbar::create_independent_toolbar_window,
            modules::independent_toolbar::create_independent_toolbar_for_region,
            modules::independent_toolbar::update_toolbar_position,
            modules::independent_toolbar::update_toolbar_position_after_drag,
            modules::independent_toolbar::request_window_focus, // 🆕 智能焦点管理
            modules::independent_toolbar::check_window_focus, // 🆕 焦点状态检查
            modules::independent_toolbar::close_toolbar_window,
            modules::independent_toolbar::close_all_active_toolbars,
            modules::independent_toolbar::close_all_toolbar_windows, // 🔧 ALIAS for frontend
            modules::independent_toolbar::get_toolbar_info,
            modules::independent_toolbar::list_active_toolbars,
            modules::independent_toolbar::find_toolbar_by_preview,
            modules::overlay::get_overlay_ui_config,
            modules::overlay::update_overlay_ui_config,
            // Phase 1: 原生菜单系统
            modules::menu::create_native_menu,
            modules::menu::get_menu_config,
            modules::menu::update_menu_config,
            // Phase 1: P1-T6 状态管理系统
            modules::state::get_app_state,
            modules::state::get_capture_state,
            modules::state::start_capture_session_with_editor,
            modules::state::get_precreated_editor_id,
            modules::state::update_capture_state,
            modules::state::get_editor_state,
            modules::state::update_editor_state,
            modules::state::reset_capture_state,
            modules::state::reset_editor_state,
            modules::state::reset_all_states,
            modules::state::start_capture_session,
            modules::state::complete_capture_session,
            modules::state::start_editor_session,
            modules::state::add_edit_action,
            modules::state::subscribe_to_state_changes,
            modules::state::unsubscribe_from_state_changes,
            // 🆕 Overlay标注模式命令
            modules::state::start_overlay_annotation_session,
            modules::state::end_overlay_annotation_session,
            modules::state::get_overlay_annotation_state,
            modules::state::update_overlay_annotation_state,
            modules::state::update_overlay_annotation_tool,
            modules::state::save_overlay_annotation_data,
            modules::overlay::calculate_overlay_toolbar_position,
            modules::overlay::start_overlay_annotation_mode,
            modules::overlay::end_overlay_annotation_mode,
            modules::overlay::update_overlay_toolbar_position,
            // 🆕 保存/导出功能
            modules::overlay::save_annotated_image,
            modules::overlay::export_annotated_image_to_clipboard,
            modules::overlay::get_save_options,
            modules::overlay::show_save_dialog,
            modules::state::get_state_stats,
            // Phase 1: P1-T7 用户体验优化
            modules::ux::get_ux_config,
            modules::ux::update_ux_config,
            modules::ux::set_interaction_mode,
            modules::ux::get_interaction_mode,
            modules::ux::send_user_feedback,
            modules::ux::handle_escape_key,
            modules::ux::exit_capture_completely_direct,
            modules::ux::complete_screenshot_capture,
            modules::ux::cycle_interaction_mode,
            modules::ux::reset_ux_state,
            modules::ux::get_ux_stats,
            modules::ux::apply_visual_theme,
            modules::ux::handle_spacebar_press,
            modules::ux::handle_mouse_drag_start,

            // 混合截图核心命令
            modules::hybrid_screenshot::start_hybrid_screenshot,
            modules::hybrid_screenshot::capture_region_from_overlay,
            modules::hybrid_screenshot::close_hybrid_screenshot,
            modules::hybrid_screenshot::get_current_monitors,
            modules::hybrid_screenshot::detect_window_smart,
            modules::hybrid_screenshot::capture_window_by_info,
            #[cfg(target_os = "windows")]
            modules::hybrid_screenshot::detect_window_enhanced_windows,
            #[cfg(target_os = "macos")]
            modules::hybrid_screenshot::check_macos_permissions,
            #[cfg(target_os = "macos")]
            modules::hybrid_screenshot::request_macos_permissions,

            // 坐标系统命令
            modules::coordinate::get_monitor_info,
            modules::coordinate::get_virtual_screen_bounds,
            modules::coordinate::get_monitor_at_point,
            modules::coordinate::validate_screen_region,

            // 编辑器命令
            modules::editor::save_edited_screenshot,
            modules::editor::copy_edited_screenshot_to_clipboard,
            modules::editor::save_canvas_image,
            modules::editor::copy_image_to_clipboard,
            modules::editor::save_annotation_metadata,
            modules::editor::get_editor_config,
            modules::editor::validate_image_data_url,
            modules::editor::send_window_selected_event,
            // 已通过modules::capture::capture_region_new引用
        ])
        .setup(|app| {
            // 🔧 LOG REFACTOR: 使用标准日志系统记录启动信息
            log::info!("[MECAP] 🚀 Starting Mecap application with unified logging system");
            log::info!("[MECAP] 📁 Logs will be saved to the system log directory");

            // 设置原生菜单
            if let Err(e) = modules::menu::setup_menu_event_handler(app.handle()) {
                log::error!("[MENU] ❌ Failed to setup menu event handler: {}", e);
            }

            // 创建默认菜单
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                // 🔧 CRITICAL FIX: 在开发模式下，避免创建重复的菜单，防止出现多个托盘图标
                #[cfg(not(debug_assertions))]
                {
                    if let Err(e) = modules::menu::create_native_menu(app_handle.clone(), None).await {
                        log::error!("[MENU] ❌ Failed to create native menu: {}", e);
                    }
                }

                #[cfg(debug_assertions)]
                {
                    log::info!("[MENU] 🔧 Skipping duplicate menu creation in development mode");
                }

                log::info!("[STARTUP] ✅ Application startup completed - overlay system will be created on demand");
            });

            // 创建系统托盘图标
            #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
            {
                use tauri::tray::TrayIconBuilder;
                use tauri::menu::{Menu, MenuItem};

                log::info!("[TRAY] 🔧 Starting system tray creation...");

                // 🔧 CRITICAL FIX: 检查是否已存在托盘图标，防止重复创建
                if !app.tray_by_id("main").is_none() {
                    log::warn!("[TRAY] ⚠️ Tray icon already exists, skipping creation");
                    return Ok(());
                }

                // 创建托盘菜单项
                log::info!("[TRAY] 📋 Creating capture menu item...");
                let capture_item = MenuItem::new(app, "capture", true, None::<&str>)
                    .map_err(|e| format!("Failed to create capture menu item: {}", e))?;
                capture_item.set_text("Capture")
                    .map_err(|e| format!("Failed to set capture menu item text: {}", e))?;
                log::info!("[TRAY] ✅ Capture menu item created successfully with ID: {:?}", capture_item.id());

                log::info!("[TRAY] 📋 Creating show main window menu item...");
                let show_window_item = MenuItem::new(app, "show_main_window", true, None::<&str>)
                    .map_err(|e| format!("Failed to create show main window menu item: {}", e))?;
                show_window_item.set_text("Show Main Window")
                    .map_err(|e| format!("Failed to set show main window menu item text: {}", e))?;
                log::info!("[TRAY] ✅ Show main window menu item created successfully with ID: {:?}", show_window_item.id());

                log::info!("[TRAY] 📋 Creating exit menu item...");
                let exit_item = MenuItem::new(app, "exit", true, None::<&str>)
                    .map_err(|e| format!("Failed to create exit menu item: {}", e))?;
                exit_item.set_text("Exit")
                    .map_err(|e| format!("Failed to set exit menu item text: {}", e))?;
                log::info!("[TRAY] ✅ Exit menu item created successfully with ID: {:?}", exit_item.id());

                log::info!("[TRAY] 📋 Creating tray menu...");
                let tray_menu = Menu::new(app)
                    .map_err(|e| format!("Failed to create tray menu: {}", e))?;

                // 添加菜单项到托盘菜单
                tray_menu.append(&capture_item)
                    .map_err(|e| format!("Failed to add capture item to menu: {}", e))?;
                tray_menu.append(&show_window_item)
                    .map_err(|e| format!("Failed to add show main window item to menu: {}", e))?;
                tray_menu.append(&exit_item)
                    .map_err(|e| format!("Failed to add exit item to menu: {}", e))?;
                log::info!("[TRAY] ✅ Tray menu created with all menu items (Capture, Show Main Window, Exit)");

                // Store menu item IDs for comparison
                let capture_item_id = capture_item.id().clone();
                let show_window_item_id = show_window_item.id().clone();
                let exit_item_id = exit_item.id().clone();

                log::info!("[TRAY] 🎯 Building tray icon with enhanced menu event handler...");
                let _tray = TrayIconBuilder::with_id("main")
                    .icon(app.default_window_icon().unwrap().clone())
                    .menu(&tray_menu)
                    .on_menu_event(move |app, event| {
                        log::info!("[TRAY] 🖱️ Menu event received: id={:?}", event.id());

                        if event.id() == &capture_item_id {
                            // Handle Capture menu item
                            log::info!("[TRAY] 📸 Capture menu item clicked - initiating capture workflow");
                            let app_handle = app.clone();
                            tauri::async_runtime::spawn(async move {
                                log::info!("[TRAY] 🚀 Starting unified capture from tray...");
                                if let Err(e) = crate::modules::ux::start_unified_capture(app_handle).await {
                                    log::error!("[TRAY] ❌ Failed to trigger capture from tray: {}", e);
                                } else {
                                    log::info!("[TRAY] ✅ Capture workflow successfully triggered from tray");
                                }
                            });
                        } else if event.id() == &show_window_item_id {
                            // Handle Show Main Window menu item
                            log::info!("[TRAY] 🪟 Show Main Window menu item clicked - bringing main window to foreground");
                            if let Some(main_window) = app.get_webview_window("main") {
                                if let Err(e) = main_window.show() {
                                    log::error!("[TRAY] ❌ Failed to show main window: {}", e);
                                } else {
                                    log::info!("[TRAY] ✅ Main window shown successfully");
                                    // Also bring the window to front and focus it
                                    if let Err(e) = main_window.set_focus() {
                                        log::warn!("[TRAY] ⚠️ Failed to focus main window: {}", e);
                                    } else {
                                        log::info!("[TRAY] ✅ Main window focused successfully");
                                    }
                                }
                            } else {
                                log::warn!("[TRAY] ⚠️ Main window not found - cannot show window");
                            }
                        } else if event.id() == &exit_item_id {
                            // Handle Exit menu item
                            log::info!("[TRAY] 🚪 Exit menu item clicked - initiating application shutdown");
                            let app_handle = app.clone();
                            tauri::async_runtime::spawn(async move {
                                log::info!("[TRAY] 🧹 Starting application cleanup before exit...");

                                // Perform cleanup operations
                                if let Err(e) = crate::modules::ux::exit_capture_completely_direct(app_handle.clone()).await {
                                    log::warn!("[TRAY] ⚠️ Failed to complete capture cleanup: {}", e);
                                } else {
                                    log::info!("[TRAY] ✅ Capture cleanup completed successfully");
                                }

                                log::info!("[TRAY] 🚪 Exiting Mecap application...");
                                app_handle.exit(0);
                            });
                        } else {
                            log::warn!("[TRAY] ⚠️ Unhandled menu event: {:?}", event.id());
                        }
                    })
                    .build(app)
                    .map_err(|e| format!("Failed to build tray icon: {}", e))?;

                log::info!("[TRAY] 🎉 System tray created successfully with enhanced functionality (Capture, Show Main Window, Exit)");
            }

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
