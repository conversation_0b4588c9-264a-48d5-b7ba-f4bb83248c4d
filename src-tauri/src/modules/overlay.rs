// 覆盖层模块 - 专门处理覆盖层窗口管理和相关功能
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::sync::{Arc, Mutex};
use lazy_static::lazy_static;
use tauri::{command, A<PERSON><PERSON><PERSON><PERSON>, Emitter, Manager, WebviewUrl, WebviewWindowBuilder};


// 🔧 LOG REFACTOR: 移除自定义前端日志命令，使用Tauri log插件的前端API

// 编辑器窗口跟踪
lazy_static! {
    static ref ACTIVE_EDITOR_WINDOWS: Mutex<HashSet<String>> = Mutex::new(HashSet::new());
    // 🆕 工具栏窗口跟踪 - 映射工具栏ID到预览窗口ID
    static ref ACTIVE_TOOLBAR_WINDOWS: Mutex<HashMap<String, String>> = Mutex::new(HashMap::new());
}

// 覆盖层管理器 - 实时创建模式

// 新的模块化覆盖层功能

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OverlayInfo {
    pub id: String,
    pub window_type: OverlayType,
    pub created_at: u64,
    pub mode: OverlayMode,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OverlayType {
    ScreenshotOverlay,
    PinnedImage,
    RegionSelector,
    WindowHighlight,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OverlayMode {
    FullTransparent, // 完全透明模式
    SemiTransparent, // 半透明模式（Linux降级）
    Opaque,          // 不透明模式（最大兼容性）
}

// 全局覆盖层管理器
lazy_static::lazy_static! {
    static ref OVERLAY_MANAGER: Arc<Mutex<HashMap<String, OverlayInfo>>> = Arc::new(Mutex::new(HashMap::new()));
}

/// 创建现代化覆盖层窗口 - Phase 1核心功能
#[command]
pub async fn create_modern_overlay(
    app_handle: AppHandle,
    overlay_type: OverlayType,
) -> Result<String, String> {
    log::info!("[OVERLAY] Creating modern overlay: {:?}", overlay_type);

    let overlay_id = format!("modern_overlay_{}", chrono::Utc::now().timestamp_millis());

    // 检测系统能力并选择最佳模式
    let overlay_mode = detect_best_overlay_mode().await;

    // 根据覆盖层类型选择HTML页面
    let html_page = match overlay_type {
        // 这是一个模式匹配表达式,用于根据overlay_type枚举值选择对应的HTML页面
        // => 符号左边是匹配的模式(pattern),右边是要返回的值
        // 每个分支返回一个字符串字面量,表示对应类型的HTML页面路径
        OverlayType::ScreenshotOverlay => "overlay-screenshot.html",     // 截图覆盖层
        OverlayType::RegionSelector => "overlay-region-selector.html",   // 区域选择器
        OverlayType::WindowHighlight => "overlay-window-highlight.html", // 窗口高亮
        OverlayType::PinnedImage => "overlay-pinned-image.html",        // 固定图片
    };

    // 创建覆盖层窗口
    let mut window_builder =
        WebviewWindowBuilder::new(&app_handle, &overlay_id, WebviewUrl::App(html_page.into()))
            .title("Mecap Modern Overlay")
            .inner_size(1920.0, 1080.0)
            .position(0.0, 0.0)
            .decorations(false)
            .always_on_top(true)
            .resizable(false)
            .skip_taskbar(true)
            .focused(true);

    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent => {
            window_builder = window_builder.transparent(true);
        }
        OverlayMode::SemiTransparent => {
            window_builder = window_builder.transparent(false);
        }
        OverlayMode::Opaque => {
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            // 设置全屏
            if let Err(e) = window.set_fullscreen(true) {
                println!("[WARNING] Failed to set fullscreen: {}", e);
            }

            // 应用模式特定的样式
            apply_overlay_mode_styles(&window, &overlay_mode).await?;

            // 记录覆盖层信息
            let overlay_info = OverlayInfo {
                id: overlay_id.clone(),
                window_type: overlay_type.clone(),
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(overlay_id.clone(), overlay_info);
            }

            let success_msg = format!(
                "Modern overlay created successfully:\n- ID: {}\n- Type: {:?}\n- Mode: {:?}",
                overlay_id, overlay_type, overlay_mode
            );

            log::info!("[OVERLAY] {}", success_msg);
            Ok(overlay_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create modern overlay: {}", e);
            log::error!("[OVERLAY] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 检测最佳覆盖层模式
async fn detect_best_overlay_mode() -> OverlayMode {
    #[cfg(target_os = "linux")]
    {
        use std::process::Command;

        // 检测合成器支持
        let compositors = ["mutter", "kwin_x11", "kwin_wayland", "compiz", "xfwm4"];
        for compositor in &compositors {
            if let Ok(output) = Command::new("pgrep").arg(compositor).output() {
                if output.status.success() && !output.stdout.is_empty() {
                    return OverlayMode::FullTransparent;
                }
            }
        }

        // 检测Wayland
        if std::env::var("WAYLAND_DISPLAY").is_ok() {
            return OverlayMode::SemiTransparent;
        }

        // 降级到半透明模式
        OverlayMode::SemiTransparent
    }

    #[cfg(not(target_os = "linux"))]
    {
        // 为了确保键盘事件能被捕获，使用半透明模式
        // 完全透明的窗口在macOS上可能无法接收键盘事件
        OverlayMode::SemiTransparent
    }
}

/// 应用覆盖层模式特定的样式
async fn apply_overlay_mode_styles(
    window: &tauri::WebviewWindow,
    mode: &OverlayMode,
) -> Result<(), String> {
    let css = match mode {
        OverlayMode::FullTransparent => {
            // 完全透明模式 - 使用真正的透明度
            r#"
                document.body.style.setProperty('background-color', 'transparent', 'important');
                document.documentElement.style.setProperty('background-color', 'transparent', 'important');

                // 额外确保透明度
                document.body.style.setProperty('background', 'transparent', 'important');
                document.documentElement.style.setProperty('background', 'transparent', 'important');

                // 调试信息
                console.log('[OVERLAY] ✅ Applied full transparent mode with setProperty');
                console.log('[OVERLAY] Body background:', window.getComputedStyle(document.body).backgroundColor);
                console.log('[OVERLAY] HTML background:', window.getComputedStyle(document.documentElement).backgroundColor);
            "#
        }
        OverlayMode::SemiTransparent => {
            // 半透明模式 - 使用极低透明度以确保能接收键盘事件
            r#"
                document.body.style.setProperty('background-color', 'rgba(0, 0, 0, 0.01)', 'important');
                document.documentElement.style.setProperty('background-color', 'rgba(0, 0, 0, 0.01)', 'important');

                // 确保窗口能接收键盘事件
                document.body.style.setProperty('pointer-events', 'auto', 'important');
                document.documentElement.style.setProperty('pointer-events', 'auto', 'important');

                console.log('[OVERLAY] ✅ Semi-transparent mode with keyboard event support (alpha=0.01)');
            "#
        }
        OverlayMode::Opaque => {
            // 不透明模式 - 使用深色背景，但仍保持一定透明度以便窗口检测
            r#"
                document.body.style.setProperty('background-color', 'rgba(0, 0, 0, 0.25)', 'important');
                document.documentElement.style.setProperty('background-color', 'rgba(0, 0, 0, 0.25)', 'important');
                console.log('[OVERLAY] Applied opaque mode with window detection optimization: rgba(0, 0, 0, 0.25)');
            "#
        }
    };

    // 尝试执行CSS，如果失败则等待并重试
    println!("[OVERLAY] 🔍 Attempting to apply CSS styles...");

    for attempt in 1..=3 {
        match window.eval(css) {
            Ok(_) => {
                println!(
                    "[OVERLAY] 🔍 CSS styles applied successfully on attempt {}",
                    attempt
                );
                return Ok(());
            }
            Err(e) => {
                println!(
                    "[OVERLAY] 🔍 CSS application failed on attempt {}: {}",
                    attempt, e
                );
                if attempt < 3 {
                    println!("[OVERLAY] 🔍 Waiting 200ms before retry...");
                    tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
                } else {
                    return Err(format!(
                        "Failed to apply overlay styles after 3 attempts: {}",
                        e
                    ));
                }
            }
        }
    }

    Ok(())
}

/// 测试Tauri连接的ping命令
#[command]
pub async fn ping_test() -> Result<String, String> {
    log::debug!("[OVERLAY] 🏓 Ping test received from frontend");
    Ok("pong".to_string())
}

/// 关闭覆盖层窗口（新版本）
#[command]
pub async fn close_overlay_new(app_handle: AppHandle, overlay_id: String) -> Result<(), String> {
    log::info!("[OVERLAY] Closing overlay: {}", overlay_id);

    // 获取窗口并关闭
    if let Some(window) = app_handle.get_webview_window(&overlay_id) {
        if let Err(e) = window.close() {
            return Err(format!("Failed to close overlay window: {}", e));
        }
    }

    // 从管理器中移除
    if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
        manager.remove(&overlay_id);
    }

    log::info!("[OVERLAY] Overlay closed successfully: {}", overlay_id);
    Ok(())
}

/// 获取所有活动的覆盖层（新版本）
#[command]
pub async fn list_active_overlays_new() -> Result<Vec<OverlayInfo>, String> {
    match OVERLAY_MANAGER.lock() {
        Ok(manager) => {
            let overlays: Vec<OverlayInfo> = manager.values().cloned().collect();
            Ok(overlays)
        }
        Err(e) => Err(format!("Failed to access overlay manager: {}", e)),
    }
}

/// 更新覆盖层属性
#[command]
pub async fn update_overlay_properties(
    app_handle: AppHandle,
    overlay_id: String,
    properties: OverlayProperties,
) -> Result<(), String> {
    println!("[OVERLAY] Updating overlay properties: {}", overlay_id);

    if let Some(window) = app_handle.get_webview_window(&overlay_id) {
        // 应用新属性
        if let Some(opacity) = properties.opacity {
            let css = format!(
                "document.body.style.opacity = '{}'; console.log('Updated opacity to {}');",
                opacity, opacity
            );
            window
                .eval(&css)
                .map_err(|e| format!("Failed to update opacity: {}", e))?;
        }

        if let Some(visible) = properties.visible {
            if visible {
                window
                    .show()
                    .map_err(|e| format!("Failed to show overlay: {}", e))?;
            } else {
                window
                    .hide()
                    .map_err(|e| format!("Failed to hide overlay: {}", e))?;
            }
        }

        println!("[OVERLAY] Overlay properties updated successfully");
        Ok(())
    } else {
        Err(format!("Overlay not found: {}", overlay_id))
    }
}

#[derive(Serialize, Deserialize, Debug)]
pub struct OverlayProperties {
    pub opacity: Option<f64>,
    pub visible: Option<bool>,
    pub always_on_top: Option<bool>,
}

/// 获取覆盖层性能统计
#[command]
pub async fn get_overlay_performance_stats() -> Result<String, String> {
    let mut stats = Vec::new();
    stats.push("=== Overlay Performance Statistics ===".to_string());

    if let Ok(manager) = OVERLAY_MANAGER.lock() {
        stats.push(format!("Active overlays: {}", manager.len()));

        for (id, info) in manager.iter() {
            let age_ms = chrono::Utc::now().timestamp_millis() as u64 - info.created_at;
            stats.push(format!("- {}: {:?} mode, age {}ms", id, info.mode, age_ms));
        }
    }

    let report = stats.join("\n");
    println!("[OVERLAY] Performance stats: {}", report);
    Ok(report)
}

/// 全屏覆盖层管理器 - Phase 1核心功能
#[command]
pub async fn create_fullscreen_overlay_manager(app_handle: AppHandle) -> Result<String, String> {
    println!("[OVERLAY] Creating fullscreen overlay manager");

    let manager_id = format!("overlay_manager_{}", chrono::Utc::now().timestamp_millis());

    // 检测最佳覆盖层模式
    let overlay_mode = detect_best_overlay_mode().await;

    // 创建管理器窗口
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &manager_id,
        WebviewUrl::App("overlay-manager.html".into()),
    )
    .title("Mecap Overlay Manager")
    .inner_size(1920.0, 1080.0)
    .position(0.0, 0.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(true);

    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent => {
            window_builder = window_builder.transparent(true);
        }
        _ => {
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            // 设置全屏
            if let Err(e) = window.set_fullscreen(true) {
                println!("[WARNING] Failed to set fullscreen: {}", e);
            }

            // 应用模式特定的样式
            apply_overlay_mode_styles(&window, &overlay_mode).await?;

            // 记录管理器信息
            let overlay_info = OverlayInfo {
                id: manager_id.clone(),
                window_type: OverlayType::ScreenshotOverlay,
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(manager_id.clone(), overlay_info);
            }

            println!(
                "[OVERLAY] Fullscreen overlay manager created: {}",
                manager_id
            );
            Ok(manager_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create overlay manager: {}", e);
            println!("[ERROR] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 关闭所有覆盖层
#[command]
pub async fn close_all_overlays(app_handle: AppHandle) -> Result<u32, String> {
    println!("[OVERLAY] Closing all overlays");

    let overlay_ids: Vec<String> = if let Ok(manager) = OVERLAY_MANAGER.lock() {
        manager.keys().cloned().collect()
    } else {
        return Err("Failed to access overlay manager".to_string());
    };

    let mut closed_count = 0;

    for overlay_id in overlay_ids {
        if let Some(window) = app_handle.get_webview_window(&overlay_id) {
            if window.close().is_ok() {
                closed_count += 1;
            }
        }
    }

    // 清空管理器
    if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
        manager.clear();
    }

    println!("[OVERLAY] Closed {} overlays", closed_count);
    Ok(closed_count)
}

/// 隐藏所有覆盖层（实时创建模式 - 关闭所有覆盖层，但保留预创建的编辑器）
#[command]
pub async fn hide_all_overlays(app_handle: AppHandle) -> Result<u32, String> {
    println!("[OVERLAY] 🔧 Closing all overlays (real-time creation mode)");

    // 获取预创建的编辑器ID，避免关闭它
    let precreated_editor_id = crate::modules::state::get_precreated_editor_id().await.unwrap_or(None);

    let overlay_ids: Vec<String> = if let Ok(manager) = OVERLAY_MANAGER.lock() {
        manager.keys().cloned().collect()
    } else {
        return Err("Failed to access overlay manager".to_string());
    };

    let mut closed_count = 0;
    let mut preserved_overlays = Vec::new();

    for overlay_id in overlay_ids {
        // 跳过预创建的编辑器窗口
        if let Some(ref editor_id) = precreated_editor_id {
            if overlay_id == *editor_id {
                println!("[OVERLAY] 🔧 Preserving pre-created editor: {}", overlay_id);
                preserved_overlays.push(overlay_id.clone());
                continue;
            }
        }

        if let Some(window) = app_handle.get_webview_window(&overlay_id) {
            println!("[OVERLAY] 🔧 Closing overlay: {}", overlay_id);
            if window.close().is_ok() {
                closed_count += 1;
            }
        }
    }

    // 清除覆盖层管理器记录，但保留预创建的编辑器
    if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
        manager.clear();
        // 重新添加保留的覆盖层
        for preserved_id in preserved_overlays {
            if let Some(ref editor_id) = precreated_editor_id {
                if preserved_id == *editor_id {
                    let overlay_info = OverlayInfo {
                        id: preserved_id.clone(),
                        window_type: OverlayType::ScreenshotOverlay,
                        created_at: chrono::Utc::now().timestamp_millis() as u64,
                        mode: OverlayMode::SemiTransparent,
                    };
                    manager.insert(preserved_id, overlay_info);
                }
            }
        }
    }

    println!("[OVERLAY] 🔧 Closed {} overlays", closed_count);
    Ok(closed_count)
}

/// P1-T4: 高级区域选择功能
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegionSelection {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
    pub timestamp: u64,
    pub screen_width: u32,
    pub screen_height: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OverlayUIConfig {
    pub show_coordinates: bool,
    pub show_grid: bool,
    pub show_magnifier: bool,
    pub grid_size: u32,
    pub selection_color: String,
    pub highlight_color: String,
}

impl Default for OverlayUIConfig {
    fn default() -> Self {
        Self {
            show_coordinates: true,
            show_grid: true,
            show_magnifier: true,
            grid_size: 20,
            selection_color: "#007AFF".to_string(),
            highlight_color: "#FF6B35".to_string(),
        }
    }
}

/// 处理区域选择完成事件
#[command]
pub async fn handle_region_selection_complete(
    app_handle: AppHandle,
    selection: RegionSelection,
    action: String,
) -> Result<String, String> {
    println!("[OVERLAY] Region selection completed: {:?}", selection);

    // 更新状态管理
    let region_data = crate::modules::state::RegionData {
        x: selection.x,
        y: selection.y,
        width: selection.width,
        height: selection.height,
        screen_width: selection.screen_width,
        screen_height: selection.screen_height,
    };

    crate::modules::state::complete_capture_session(app_handle.clone(), Some(region_data), None)
        .await
        .map_err(|e| format!("Failed to complete capture session: {}", e))?;

    match action.as_str() {
        "capture" => {
            // 执行截图捕获
            let area = crate::modules::capture::ScreenshotArea {
                x: selection.x,
                y: selection.y,
                width: selection.width,
                height: selection.height,
            };

            match crate::modules::capture::capture_region_new(area).await {
                Ok(result) => {
                    println!("[OVERLAY] Region captured successfully");

                    // 关闭覆盖层
                    close_all_overlays(app_handle).await?;

                    Ok(format!(
                        "Region captured: {}x{}",
                        result.width.unwrap_or(0),
                        result.height.unwrap_or(0)
                    ))
                }
                Err(e) => Err(format!("Failed to capture region: {}", e)),
            }
        }
        "copy" => {
            // TODO: 实现复制到剪贴板功能
            println!("[OVERLAY] Copy to clipboard requested");
            Ok("Copy functionality not yet implemented".to_string())
        }
        "save" => {
            // 保存选择区域信息
            println!("[OVERLAY] Save selection requested");
            Ok("Selection saved".to_string())
        }
        _ => Err(format!("Unknown action: {}", action)),
    }
}



/// 创建截图编辑器窗口 - 独立的全屏编辑器（保留原有功能作为备用）
#[command]
pub async fn create_screenshot_editor_window(
    app_handle: AppHandle,
    screenshot_data: serde_json::Value,
) -> Result<String, String> {
    log::info!("[OVERLAY] Creating independent screenshot editor window");

    let editor_id = format!("screenshot_editor_{}", chrono::Utc::now().timestamp_millis());

    // 检测最佳覆盖层模式
    let overlay_mode = detect_best_overlay_mode().await;
    log::info!("[OVERLAY] 🎯 Detected overlay mode: {:?}", overlay_mode);

    // 创建编辑器窗口
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &editor_id,
        WebviewUrl::App("index.html".into()),
    )
    .title("Mecap Screenshot Editor")
    .inner_size(1920.0, 1080.0)
    .position(0.0, 0.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(true);

    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent => {
            window_builder = window_builder.transparent(true);
        }
        OverlayMode::SemiTransparent => {
            window_builder = window_builder.transparent(false);
        }
        OverlayMode::Opaque => {
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            // 设置全屏
            if let Err(e) = window.set_fullscreen(true) {
                println!("[WARNING] Failed to set fullscreen: {}", e);
            }

            // 应用模式特定的样式
            apply_overlay_mode_styles(&window, &overlay_mode).await?;

            // 延迟发送截图数据，确保前端有足够时间初始化
            log::info!("[OVERLAY] 📤 Preparing to send screenshot data to editor window");
            log::info!("[OVERLAY] 📤 Data keys: {:?}", screenshot_data.as_object().map(|obj| obj.keys().collect::<Vec<_>>()));

            let window_clone = window.clone();
            let data_clone = screenshot_data.clone();

            // 使用异步任务延迟发送数据
            tokio::spawn(async move {
                // 等待1秒确保前端完全初始化
                tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;

                log::info!("[OVERLAY] 📤 Sending screenshot data to editor window (delayed)");
                match window_clone.emit("screenshot-data", &data_clone) {
                    Ok(_) => log::info!("[OVERLAY] ✅ Screenshot data sent successfully to editor window"),
                    Err(e) => log::error!("[OVERLAY] ❌ Failed to send screenshot data: {}", e),
                }
            });

            // 记录编辑器信息
            let overlay_info = OverlayInfo {
                id: editor_id.clone(),
                window_type: OverlayType::ScreenshotOverlay,
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(editor_id.clone(), overlay_info);
            }

            log::info!("[OVERLAY] Screenshot editor window created: {}", editor_id);
            Ok(editor_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create screenshot editor: {}", e);
            log::error!("[OVERLAY] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 🆕 创建工具栏HTML内容
fn create_toolbar_html(position: &str, is_horizontal: bool) -> String {
    let layout_class = if is_horizontal { "horizontal" } else { "vertical" };
    let position_class = format!("position-{}", position);

    format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Toolbar</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: transparent;
            overflow: hidden;
            user-select: none;
            -webkit-user-select: none;
        }}

        .toolbar-container {{
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 8px;
            box-shadow:
                0 0 0 1px rgba(255, 107, 53, 0.2),
                0 8px 32px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            display: flex;
            gap: 4px;
            opacity: 1;
            transition: opacity 0.3s ease, transform 0.3s ease;
            z-index: 1000;
        }}

        .toolbar-container.vertical {{
            flex-direction: column;
        }}

        .toolbar-container.horizontal {{
            flex-direction: row;
        }}

        .toolbar-group {{
            display: flex;
            gap: 4px;
        }}

        .toolbar-container.vertical .toolbar-group {{
            flex-direction: column;
        }}

        .toolbar-container.horizontal .toolbar-group {{
            flex-direction: row;
        }}

        .toolbar-button {{
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.8);
            color: #333;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }}

        .toolbar-button:hover {{
            background: rgba(255, 107, 53, 0.9);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }}

        .toolbar-button:active {{
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }}

        .toolbar-button.active {{
            background: rgba(255, 107, 53, 1);
            color: white;
        }}

        .toolbar-separator {{
            width: 1px;
            height: 32px;
            background: rgba(0, 0, 0, 0.1);
            margin: 4px 0;
        }}

        .toolbar-container.horizontal .toolbar-separator {{
            width: 1px;
            height: 32px;
        }}

        .toolbar-container.vertical .toolbar-separator {{
            width: 32px;
            height: 1px;
        }}
    </style>
</head>
<body>
    <div class="toolbar-container {layout_class} {position_class}">
        <!-- 绘图工具组 -->
        <div class="toolbar-group">
            <button class="toolbar-button" data-tool="text" title="文字标注 (T)">
                T
            </button>
            <button class="toolbar-button" data-tool="arrow" title="箭头 (A)">
                ↗
            </button>
            <button class="toolbar-button" data-tool="rectangle" title="矩形 (R)">
                ▢
            </button>
            <button class="toolbar-button" data-tool="circle" title="圆形 (C)">
                ○
            </button>
            <button class="toolbar-button" data-tool="brush" title="画笔 (B)">
                ✏
            </button>
        </div>

        <div class="toolbar-separator"></div>

        <!-- 操作工具组 -->
        <div class="toolbar-group">
            <button class="toolbar-button" data-tool="undo" title="撤销 (Ctrl+Z)">
                ↶
            </button>
            <button class="toolbar-button" data-tool="save" title="保存 (Ctrl+S)">
                💾
            </button>
            <button class="toolbar-button" data-tool="copy" title="复制 (Ctrl+C)">
                📋
            </button>
            <button class="toolbar-button" data-tool="close" title="关闭 (ESC)">
                ✕
            </button>
        </div>
    </div>

    <script>
        console.log('[TOOLBAR] Independent toolbar window loaded');

        // 工具栏按钮事件处理
        document.addEventListener('DOMContentLoaded', function() {{
            const buttons = document.querySelectorAll('.toolbar-button');

            buttons.forEach(button => {{
                button.addEventListener('click', function() {{
                    const tool = this.getAttribute('data-tool');
                    console.log('[TOOLBAR] Tool clicked:', tool);

                    // 移除其他按钮的active状态
                    buttons.forEach(btn => btn.classList.remove('active'));

                    // 添加当前按钮的active状态（除了操作按钮）
                    if (!['undo', 'save', 'copy', 'close'].includes(tool)) {{
                        this.classList.add('active');
                    }}

                    // 通过Tauri事件系统通知预览窗口
                    if (window.__TAURI__ && window.__TAURI__.event) {{
                        window.__TAURI__.event.emit('toolbar-action', {{
                            tool: tool,
                            timestamp: Date.now()
                        }});
                    }}
                }});
            }});

            console.log('[TOOLBAR] Event listeners attached to', buttons.length, 'buttons');
        }});
    </script>
</body>
</html>
    "#, layout_class = layout_class, position_class = position_class)
}



/// 🆕 创建注解窗口 - 专用于截图注解功能
///
/// 这个函数创建一个专门用于注解的覆盖层窗口：
/// 1. 使用 overlay-screenshot.html 作为注解界面
/// 2. 支持与独立工具栏的通信
/// 3. 具有完整的绘图和注解功能
/// 4. 支持状态传输和恢复
#[command]
pub async fn create_annotation_window(
    app_handle: AppHandle,
    region_data: serde_json::Value,
    screenshot_path: String,
    toolbar_state: Option<serde_json::Value>,
) -> Result<String, String> {
    log::info!("[OVERLAY] 🎨 Creating annotation window for screenshot annotation");
    log::info!("[OVERLAY] 📊 Region data: {:?}", region_data);
    log::info!("[OVERLAY] 📸 Screenshot path: {}", screenshot_path);

    let annotation_id = format!("annotation_window_{}", chrono::Utc::now().timestamp_millis());

    // 检测最佳覆盖层模式
    let overlay_mode = detect_best_overlay_mode().await;
    log::info!("[OVERLAY] 🎯 Detected overlay mode for annotation: {:?}", overlay_mode);

    // 从region_data中提取尺寸信息
    let region_x = region_data.get("x").and_then(|v| v.as_f64()).unwrap_or(0.0);
    let region_y = region_data.get("y").and_then(|v| v.as_f64()).unwrap_or(0.0);
    let region_width = region_data.get("width").and_then(|v| v.as_f64()).unwrap_or(800.0);
    let region_height = region_data.get("height").and_then(|v| v.as_f64()).unwrap_or(600.0);

    log::info!("[OVERLAY] 📏 Annotation window region: {}x{} at ({}, {})",
               region_width, region_height, region_x, region_y);

    // 创建注解窗口 - 使用overlay-screenshot.html
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &annotation_id,
        WebviewUrl::App("overlay-screenshot.html".into()),
    )
    .title("Mecap Annotation Window")
    .inner_size(region_width, region_height)
    .position(region_x, region_y)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(true)
    .transparent(true);

    // 根据覆盖层模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent => {
            // 完全透明模式，支持点击穿透
            window_builder = window_builder.transparent(true);
        }
        OverlayMode::SemiTransparent => {
            // 半透明模式
            window_builder = window_builder.transparent(true);
        }
        OverlayMode::Opaque => {
            // 不透明模式
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            // 应用模式特定的样式
            apply_overlay_mode_styles(&window, &overlay_mode).await?;

            // 发送初始化数据到注解窗口
            let init_data = serde_json::json!({
                "type": "annotation-initialize",
                "data": {
                    "windowId": annotation_id,
                    "regionData": region_data,
                    "screenshotPath": screenshot_path,
                    "toolbarState": toolbar_state,
                    "overlayMode": overlay_mode,
                    "timestamp": chrono::Utc::now().timestamp_millis()
                }
            });

            // 延迟发送初始化数据，确保窗口完全加载
            let window_clone = window.clone();
            let init_data_clone = init_data.clone();
            tokio::spawn(async move {
                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                if let Err(e) = window_clone.emit("annotation-initialize", &init_data_clone) {
                    log::error!("[OVERLAY] 🎨 Failed to send initialization data: {}", e);
                } else {
                    log::info!("[OVERLAY] 🎨 Annotation initialization data sent successfully");
                }
            });

            // 记录注解窗口信息
            let overlay_info = OverlayInfo {
                id: annotation_id.clone(),
                window_type: OverlayType::ScreenshotOverlay, // 复用截图覆盖层类型
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(annotation_id.clone(), overlay_info);
            }

            log::info!("[OVERLAY] ✅ Annotation window created successfully: {}", annotation_id);

            // 发送注解窗口创建事件
            if let Err(e) = app_handle.emit("annotation-window-created", serde_json::json!({
                "windowId": annotation_id,
                "regionData": region_data,
                "screenshotPath": screenshot_path
            })) {
                log::warn!("[OVERLAY] 🎨 Failed to emit annotation window created event: {}", e);
            }

            Ok(annotation_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create annotation window: {}", e);
            log::error!("[OVERLAY] 🎨 {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 创建截图预览窗口
///
/// 这个函数创建一个置顶的预览窗口，显示刚刚捕获的截图：
/// 1. 窗口大小与截图尺寸完全一致
/// 2. 窗口始终保持在最顶层
/// 3. 具有统一的outline边框设计
/// 4. 支持双击打开编辑器
/// 5. 预览窗口位置与被截图窗口位置一致
#[command]
pub async fn create_screenshot_preview_window(
    app_handle: AppHandle,
    screenshot_path: String,
    width: u32,
    height: u32,
    window_x: Option<i32>,
    window_y: Option<i32>,
) -> Result<String, String> {
    log::info!("[OVERLAY] 🖼️ Creating screenshot preview window");
    log::info!("[OVERLAY] 📊 Screenshot: path={}, dimensions={}x{}, position=({:?},{:?})",
               screenshot_path, width, height, window_x, window_y);

    // 🔧 CRITICAL FIX: 清理任何孤立的工具栏窗口
    log::info!("[OVERLAY] 🧹 Cleaning up orphaned toolbar windows before creating new preview");
    match crate::modules::independent_toolbar::close_all_active_toolbars(app_handle.clone()).await {
        Ok(closed_count) => {
            if closed_count > 0 {
                log::info!("[OVERLAY] 🧹 Cleaned up {} orphaned toolbar windows", closed_count);
            }
        }
        Err(e) => {
            log::warn!("[OVERLAY] ⚠️ Failed to clean up orphaned toolbars: {}", e);
        }
    }

    let preview_id = format!("screenshot_preview_{}", chrono::Utc::now().timestamp_millis());

    // 🔧 CRITICAL FIX: 保持1:1显示比例，不进行缩放
    // 移除最大尺寸限制，确保预览窗口与原始截图尺寸完全一致
    let window_width = width as f64;
    let window_height = height as f64;

    log::info!("[OVERLAY] 📏 Preview window size (1:1 ratio): {}x{}", window_width, window_height);

    // 创建预览窗口URL，使用独立的HTML页面并通过查询参数传递截图信息
    let webview_url = WebviewUrl::App(format!(
        "screenshot-preview.html?path={}&width={}&height={}",
        urlencoding::encode(&screenshot_path),
        width,
        height
    ).into());

    log::info!("[OVERLAY] 🪟 Creating preview window: {}", preview_id);

    // 🔧 优化：根据被截图窗口位置定位预览窗口
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &preview_id,
        webview_url,
    )
    .title("Screenshot Preview")
    .inner_size(window_width, window_height)
    .decorations(false) // 无边框，使用自定义outline边框
    .resizable(false)   // 不可调整大小
    .always_on_top(true) // 始终置顶
    .visible(true)
    .focused(false)     // 不抢夺焦点
    .transparent(true); // 启用透明背景，让圆角截图自然显示

    // 🔧 精确坐标定位：如果提供了窗口坐标，使用原始位置；否则居中显示
    if let (Some(x), Some(y)) = (window_x, window_y) {
        log::info!("[OVERLAY] 📍 Positioning preview window at original location: ({}, {})", x, y);
        window_builder = window_builder.position(x as f64, y as f64);
    } else {
        log::info!("[OVERLAY] 📍 Centering preview window (no original position provided)");
        window_builder = window_builder.center();
    }

    let window = window_builder
        .build()
        .map_err(|e| format!("Failed to create preview window: {}", e))?;

    log::info!("[OVERLAY] ✅ Screenshot preview window created successfully: {}", preview_id);

    // 等待窗口加载完成
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    // 发送截图信息给预览窗口
    let preview_event = serde_json::json!({
        "type": "screenshot-preview-data",
        "path": screenshot_path,
        "width": width,
        "height": height,
        "timestamp": chrono::Utc::now().timestamp_millis()
    });

    if let Err(e) = window.emit("screenshot-preview-data", &preview_event) {
        log::warn!("[OVERLAY] ⚠️ Failed to send preview data to window: {}", e);
    } else {
        log::info!("[OVERLAY] 📤 Screenshot preview data sent to window");
    }

    // 🆕 创建独立工具栏窗口
    log::info!("[OVERLAY] 🔧 Creating independent toolbar for preview window: {}", preview_id);

    // 获取预览窗口的位置和大小信息
    if let Ok(position) = window.outer_position() {
        if let Ok(size) = window.outer_size() {
            let preview_bounds = crate::modules::independent_toolbar::PreviewWindowInfo {
                id: preview_id.clone(),
                x: position.x,
                y: position.y,
                width: size.width,
                height: size.height,
            };

            // 获取屏幕信息
            let screen_info = crate::modules::independent_toolbar::ScreenInfo {
                width: 3360, // 临时硬编码，后续可以动态获取
                height: 2100,
            };

            match crate::modules::independent_toolbar::create_independent_toolbar_window(
                app_handle.clone(),
                preview_id.clone(),
                preview_bounds,
                screen_info,
            ).await {
                Ok(toolbar_id) => {
                    log::info!("[OVERLAY] ✅ Independent toolbar created successfully: {}", toolbar_id);
                }
                Err(e) => {
                    log::warn!("[OVERLAY] ⚠️ Failed to create independent toolbar: {}", e);
                }
            }
        } else {
            log::warn!("[OVERLAY] ⚠️ Failed to get preview window size for toolbar creation");
        }
    } else {
        log::warn!("[OVERLAY] ⚠️ Failed to get preview window position for toolbar creation");
    }

    Ok(preview_id)
}

/// 读取截图文件数据
#[command]
pub async fn read_screenshot_file(file_path: String) -> Result<Vec<u8>, String> {
    log::info!("[OVERLAY] 📖 Reading screenshot file: {}", file_path);

    match std::fs::read(&file_path) {
        Ok(data) => {
            log::info!("[OVERLAY] ✅ Successfully read file, size: {} bytes", data.len());
            Ok(data)
        }
        Err(e) => {
            let error_msg = format!("Failed to read screenshot file: {}", e);
            log::error!("[OVERLAY] ❌ {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 创建基于文件的截图编辑器窗口
///
/// 这个函数实现了新的文件基础工作流：
/// 1. 将截图数据保存到临时文件
/// 2. 创建截图编辑器窗口
/// 3. 通过事件发送文件路径给编辑器
/// 4. 编辑器从文件加载截图
#[command]
pub async fn create_screenshot_editor_with_file(
    app_handle: AppHandle,
    screenshot_data: Vec<u8>,
    width: u32,
    height: u32,
    x: i32,
    y: i32,
) -> Result<String, String> {
    log::info!("[OVERLAY] 🚀 Creating file-based screenshot editor");
    log::info!("[OVERLAY] 📊 Screenshot dimensions: {}x{} at ({}, {})", width, height, x, y);

    // 检查是否已有活跃的编辑器窗口
    {
        let mut active_editors = ACTIVE_EDITOR_WINDOWS.lock().unwrap();
        if !active_editors.is_empty() {
            log::warn!("[OVERLAY] ⚠️ Active editor windows exist: {:?}", *active_editors);

            // 关闭所有现有的编辑器窗口
            for editor_id in active_editors.iter() {
                if let Some(existing_window) = app_handle.get_webview_window(editor_id) {
                    log::info!("[OVERLAY] 🗑️ Closing existing editor window: {}", editor_id);
                    if let Err(e) = existing_window.close() {
                        log::warn!("[OVERLAY] ⚠️ Failed to close existing editor: {}", e);
                    }
                }
            }
            active_editors.clear();
        }
    }

    // 1. 保存截图到临时文件
    let temp_dir = app_handle
        .path()
        .temp_dir()
        .map_err(|e| format!("Failed to get temp directory: {}", e))?;

    let filename = format!("mecap_screenshot_{}.png", chrono::Utc::now().timestamp_millis());
    let file_path = temp_dir.join(&filename);

    log::info!("[OVERLAY] 💾 Saving screenshot to: {:?}", file_path);

    std::fs::write(&file_path, &screenshot_data)
        .map_err(|e| format!("Failed to save screenshot file: {}", e))?;

    log::info!("[OVERLAY] ✅ Screenshot saved successfully");

    // 2. 创建截图编辑器窗口
    let editor_id = format!("screenshot_editor_{}", chrono::Utc::now().timestamp_millis());

    let webview_url = if cfg!(debug_assertions) {
        WebviewUrl::External("http://localhost:1420/#/screenshot-editor".parse().unwrap())
    } else {
        WebviewUrl::App("index.html#/screenshot-editor".into())
    };

    log::info!("[OVERLAY] 🪟 Creating editor window: {}", editor_id);

    let window = WebviewWindowBuilder::new(
        &app_handle,
        &editor_id,
        webview_url,
    )
    .title("Mecap Screenshot Editor")
    .inner_size(1200.0, 800.0)
    .center()
    .decorations(true)
    .resizable(true)
    .visible(true)
    .focused(true)
    .build()
    .map_err(|e| format!("Failed to create editor window: {}", e))?;

    log::info!("[OVERLAY] ✅ Editor window created successfully");

    // 将新编辑器窗口添加到跟踪列表
    {
        let mut active_editors = ACTIVE_EDITOR_WINDOWS.lock().unwrap();
        active_editors.insert(editor_id.clone());
        log::info!("[OVERLAY] 📝 Added editor to tracking list: {}", editor_id);
    }

    // 设置窗口关闭事件监听，以便从跟踪列表中移除
    let editor_id_for_cleanup = editor_id.clone();
    window.on_window_event(move |event| {
        if let tauri::WindowEvent::Destroyed = event {
            let mut active_editors = ACTIVE_EDITOR_WINDOWS.lock().unwrap();
            active_editors.remove(&editor_id_for_cleanup);
            log::info!("[OVERLAY] 🗑️ Removed editor from tracking list: {}", editor_id_for_cleanup);
        }
    });

    // 3. 等待窗口加载完成
    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;

    // 4. 发送文件路径和元数据给编辑器
    let screenshot_event = serde_json::json!({
        "filePath": file_path.to_string_lossy(),
        "width": width,
        "height": height,
        "x": x,
        "y": y,
        "timestamp": chrono::Utc::now().timestamp_millis()
    });

    log::info!("[OVERLAY] 📤 Sending screenshot data to editor");
    window
        .emit("load-screenshot", &screenshot_event)
        .map_err(|e| format!("Failed to send screenshot data: {}", e))?;

    log::info!("[OVERLAY] ✅ File-based screenshot editor created successfully: {}", editor_id);
    Ok(editor_id)
}




/// 创建窗口高亮覆盖层 - P1-T4增强功能
#[command]
pub async fn create_window_highlight_overlay(
    app_handle: AppHandle,
    config: Option<OverlayUIConfig>,
) -> Result<String, String> {
    log::info!("[OVERLAY] Creating window highlight overlay with enhanced UI");

    let ui_config = config.unwrap_or_default();
    let overlay_id = format!("window_highlight_{}", chrono::Utc::now().timestamp_millis());

    // 检测最佳覆盖层模式
    let overlay_mode = detect_best_overlay_mode().await;
    log::info!("[OVERLAY] 🎯 Detected overlay mode: {:?}", overlay_mode);

    // 创建窗口高亮覆盖层
    let mut window_builder = WebviewWindowBuilder::new(
        &app_handle,
        &overlay_id,
        WebviewUrl::App("overlay-window-highlight.html".into()),
    )
    .title("Mecap Window Highlight")
    .inner_size(3840.0, 2560.0) // 使用更大的初始尺寸，后面会根据实际显示器调整
    .position(0.0, 0.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .skip_taskbar(true)
    .focused(true)
    .fullscreen(false) // 明确禁用系统全屏模式
    .accept_first_mouse(true) // 确保能接收鼠标事件
    .content_protected(false); // 确保内容不被保护

    // 根据模式设置透明度
    match overlay_mode {
        OverlayMode::FullTransparent | OverlayMode::SemiTransparent => {
            println!(
                "[OVERLAY] 🎯 Setting window transparent: true (mode: {:?})",
                overlay_mode
            );
            window_builder = window_builder.transparent(true);
        }
        _ => {
            println!(
                "[OVERLAY] 🎯 Setting window transparent: false (mode: {:?})",
                overlay_mode
            );
            window_builder = window_builder.transparent(false);
        }
    }

    match window_builder.build() {
        Ok(window) => {
            println!("[OVERLAY] 🎯 Initializing Tauri API in overlay window");

            // 🔧 CRITICAL FIX: Ensure cursor events are NOT ignored
            println!("[OVERLAY] 🔧 Ensuring cursor events are enabled for click handling");
            if let Err(e) = window.set_ignore_cursor_events(false) {
                println!("[WARNING] Failed to enable cursor events: {}", e);
            } else {
                println!("[OVERLAY] ✅ Cursor events enabled successfully");
            }

            // 🔧 DEBUG: 开发模式下启用开发者工具
            #[cfg(debug_assertions)]
            {
                log::debug!("[OVERLAY] 🔧 Opening DevTools for frontend debugging");
                window.open_devtools();
                log::debug!("[OVERLAY] ✅ DevTools opened successfully");
            }

            // 确保Tauri API在覆盖层中可用
            if let Err(e) = window.eval("
                console.log('[OVERLAY] 🔍 Checking Tauri API availability...');
                if (typeof window.__TAURI__ === 'undefined') {
                    console.error('[OVERLAY] ❌ Tauri API not available');
                    window.tauriApiAvailable = false;
                } else {
                    console.log('[OVERLAY] ✅ Tauri API is available');
                    console.log('[OVERLAY] 🔍 Available Tauri modules:', Object.keys(window.__TAURI__));
                    window.tauriApiAvailable = true;
                }
            ") {
                println!("[WARNING] Failed to check Tauri API: {}", e);
            }

            // 🚫 不使用系统全屏模式，因为会拦截ESC键
            // 而是手动设置窗口大小和位置来覆盖整个屏幕
            println!("[OVERLAY] 🎯 Setting manual fullscreen to avoid ESC key interception");

            // 获取主显示器尺寸
            if let Ok(Some(monitor)) = window.primary_monitor() {
                let size = monitor.size();
                let position = monitor.position();

                println!(
                    "[OVERLAY] 🎯 Monitor size: {}x{}, position: ({}, {})",
                    size.width, size.height, position.x, position.y
                );

                // 设置窗口大小和位置（覆盖整个屏幕包括菜单栏）
                if let Err(e) = window.set_size(tauri::Size::Physical(*size)) {
                    println!("[WARNING] Failed to set window size: {}", e);
                }
                if let Err(e) = window.set_position(tauri::Position::Physical(*position)) {
                    println!("[WARNING] Failed to set window position: {}", e);
                }

                println!(
                    "[OVERLAY] 🎯 Window positioned to cover entire screen including menu bar"
                );

                println!("[OVERLAY] 🎯 Manual fullscreen setup completed");
            } else {
                println!("[WARNING] Failed to get monitor info, falling back to default size");
            }

            // 强制设置焦点以确保键盘事件能被捕获
            println!("[OVERLAY] 🎯 Setting window focus for keyboard events");
            if let Err(e) = window.set_focus() {
                println!("[WARNING] Failed to set window focus: {}", e);
            } else {
                println!("[OVERLAY] 🎯 Window focus set successfully");
            }

            // 额外的焦点确保措施
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            if let Err(e) = window.set_focus() {
                println!("[WARNING] Second focus attempt failed: {}", e);
            }

            // 设置窗口为可接收键盘输入
            println!("[OVERLAY] 🎯 Configuring window for keyboard input");
            if let Err(e) = window.eval("window.focus(); document.body.focus();") {
                println!("[WARNING] Failed to focus window via JavaScript: {}", e);
            }

            // 等待窗口加载完成
            println!("[OVERLAY] 🔍 Waiting for window to load...");
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

            // 检查窗口是否可见
            println!("[OVERLAY] 🔍 Window is visible: {:?}", window.is_visible());

            // 应用模式特定的样式
            println!("[OVERLAY] Applying overlay mode styles: {:?}", overlay_mode);
            apply_overlay_mode_styles(&window, &overlay_mode).await?;
            println!("[OVERLAY] Overlay mode styles applied successfully");

            // 🔧 TEMPORARILY DISABLED: 综合键盘事件诊断和修复
            println!(
                "[OVERLAY] 🔍 SKIPPING diagnostic JavaScript to allow HTML JavaScript to run..."
            );

            // 简单的测试JavaScript，不干扰HTML的JavaScript
            let diagnostic_js = r#"
                console.log('[DIAGNOSTIC] Simple test - not interfering with HTML JavaScript');
                // 返回简单状态
                JSON.stringify({ status: 'diagnostic_skipped', html_js_allowed: true });
            "#;

            /*
            println!("[OVERLAY] 🔍 Starting COMPREHENSIVE keyboard event diagnostics...");
            let diagnostic_js = r#"
                // === COMPREHENSIVE DIAGNOSTIC SYSTEM ===
                console.log('[FRONTEND] === STARTING COMPREHENSIVE DIAGNOSTICS ===');

                // === 创建全局诊断对象 ===
                window.MECAP_DIAGNOSTICS = {
                    escPressCount: 0,
                    lastEscTime: 0,
                    domSignalsSent: 0,
                    eventListenersAttached: 0,
                    tauriApiChecks: [],
                    windowStateChecks: [],
                    communicationLog: []
                };

                // === 日志函数 ===
                function logDiagnostic(category, message, data = null) {
                    const timestamp = Date.now();
                    const logEntry = { timestamp, category, message, data };
                    window.MECAP_DIAGNOSTICS.communicationLog.push(logEntry);
                    console.log(`[FRONTEND-${category}] ${message}`, data || '');

                    // 同时更新屏幕显示
                    const diagnosticDiv = document.getElementById('keyboard-diagnostic');
                    if (diagnosticDiv) {
                        const logLine = `<br><span style="color: cyan;">[${category}] ${message}</span>`;
                        diagnosticDiv.innerHTML += logLine;
                    }
                }

                logDiagnostic('INIT', 'Comprehensive diagnostic system initialized');
                // === TAURI API 全面检查 ===
                logDiagnostic('TAURI', 'Starting Tauri API availability check');
                const tauriChecks = {
                    'window.__TAURI__': typeof window.__TAURI__,
                    'window.Tauri': typeof window.Tauri,
                    'window.__TAURI_INTERNALS__': typeof window.__TAURI_INTERNALS__,
                    'window.__TAURI_METADATA__': typeof window.__TAURI_METADATA__,
                    'window.__TAURI__.core': window.__TAURI__ ? typeof window.__TAURI__.core : 'N/A',
                    'window.__TAURI__.core.invoke': window.__TAURI__ && window.__TAURI__.core ? typeof window.__TAURI__.core.invoke : 'N/A'
                };
                window.MECAP_DIAGNOSTICS.tauriApiChecks = tauriChecks;
                logDiagnostic('TAURI', 'API check results', tauriChecks);

                // === 基础状态检查 ===
                const bodyContent = document.body ? document.body.innerHTML.length : 0;
                const title = document.title || 'No title';

                // === 焦点和可见性检查 ===
                const hasFocus = document.hasFocus();
                const isVisible = document.visibilityState === 'visible';
                const activeElement = document.activeElement ? document.activeElement.tagName : 'NONE';

                // === 窗口属性检查 ===
                const windowFocused = window.document.hasFocus();
                const tabIndex = document.body.tabIndex;

                const windowState = { hasFocus, isVisible, activeElement, windowFocused, tabIndex };
                window.MECAP_DIAGNOSTICS.windowStateChecks = windowState;
                logDiagnostic('WINDOW', 'Window state check', windowState);

                // === 创建诊断显示 ===
                if (document.body) {
                    const diagnosticDiv = document.createElement('div');
                    diagnosticDiv.id = 'keyboard-diagnostic';
                    diagnosticDiv.style.cssText = `
                        position: fixed; top: 50px; left: 10px;
                        color: lime; font-size: 16px; z-index: 10000;
                        background: rgba(0,0,0,0.8); padding: 10px;
                        font-family: monospace; line-height: 1.4;
                    `;
                    diagnosticDiv.innerHTML = `
                        KEYBOARD DIAGNOSTICS:<br>
                        Focus: ${hasFocus}<br>
                        Visible: ${isVisible}<br>
                        Active: ${activeElement}<br>
                        TabIndex: ${tabIndex}<br>
                        Events: <span id="event-count">0</span>
                    `;
                    document.body.appendChild(diagnosticDiv);
                }

                // === 确保body可以接收焦点 ===
                if (document.body) {
                    document.body.tabIndex = 0;
                    document.body.focus();
                    document.body.style.outline = 'none';
                }

                // === 多层键盘事件监听器 ===
                let eventCount = 0;

                // === 综合ESC键处理器 ===
                logDiagnostic('LISTENER', 'Setting up comprehensive ESC key handler');

                function handleEscKeyPress(e, source) {
                    eventCount++;
                    const counter = document.getElementById('event-count');
                    if (counter) counter.textContent = eventCount;

                    logDiagnostic('KEY', `Key pressed: ${e.key} from ${source}`, {
                        key: e.key,
                        code: e.code,
                        target: e.target.tagName,
                        timestamp: Date.now()
                    });

                    if (e.key === 'Escape') {
                        window.MECAP_DIAGNOSTICS.escPressCount++;
                        window.MECAP_DIAGNOSTICS.lastEscTime = Date.now();

                        logDiagnostic('ESC', `ESC key detected! Count: ${window.MECAP_DIAGNOSTICS.escPressCount}`);

                        // === STEP 1: 尝试Tauri API ===
                        let tauriApi = null;
                        let tauriMethod = 'none';

                        if (window.__TAURI__ && window.__TAURI__.core && window.__TAURI__.core.invoke) {
                            tauriApi = window.__TAURI__;
                            tauriMethod = 'standard';
                        } else if (window.Tauri && window.Tauri.core && window.Tauri.core.invoke) {
                            tauriApi = window.Tauri;
                            tauriMethod = 'global';
                        } else if (window.__TAURI_INTERNALS__ && window.__TAURI_INTERNALS__.core) {
                            tauriApi = window.__TAURI_INTERNALS__;
                            tauriMethod = 'internals';
                        }

                        logDiagnostic('TAURI', `Tauri API method: ${tauriMethod}`, { available: !!tauriApi });

                        if (tauriApi) {
                            logDiagnostic('TAURI', 'Attempting Tauri API call');
                            tauriApi.core.invoke('exit_capture_completely_direct')
                                .then(() => {
                                    logDiagnostic('TAURI', 'Tauri API call successful');
                                })
                                .catch(err => {
                                    logDiagnostic('TAURI', 'Tauri API call failed', err);
                                    // 如果Tauri失败，继续到DOM方法
                                    sendDomSignal();
                                });
                        } else {
                            // === STEP 2: DOM信号方法 ===
                            sendDomSignal();
                        }

                        function sendDomSignal() {
                            logDiagnostic('DOM', 'Starting DOM signal method');
                            window.MECAP_DIAGNOSTICS.domSignalsSent++;

                            // 方法1: 创建DOM标记
                            const escMarker = document.createElement('div');
                            escMarker.id = 'esc-key-pressed-marker-' + Date.now();
                            escMarker.className = 'esc-marker';
                            escMarker.style.display = 'none';
                            escMarker.setAttribute('data-timestamp', Date.now().toString());
                            escMarker.setAttribute('data-count', window.MECAP_DIAGNOSTICS.escPressCount.toString());
                            document.body.appendChild(escMarker);

                            // 方法2: 修改页面标题
                            const newTitle = 'ESC_PRESSED_' + Date.now() + '_COUNT_' + window.MECAP_DIAGNOSTICS.escPressCount;
                            document.title = newTitle;

                            // 方法3: 创建全局变量
                            window.ESC_KEY_PRESSED = {
                                timestamp: Date.now(),
                                count: window.MECAP_DIAGNOSTICS.escPressCount,
                                method: 'dom_signal'
                            };

                            // 方法4: 修改body属性
                            document.body.setAttribute('data-esc-pressed', Date.now().toString());
                            document.body.setAttribute('data-esc-count', window.MECAP_DIAGNOSTICS.escPressCount.toString());

                            logDiagnostic('DOM', 'DOM signals sent', {
                                markerId: escMarker.id,
                                title: newTitle,
                                bodyAttribute: document.body.getAttribute('data-esc-pressed'),
                                globalVar: !!window.ESC_KEY_PRESSED
                            });
                        }
                    }
                }

                // 监听器1: document级别 (capture phase)
                document.addEventListener('keydown', function(e) {
                    handleEscKeyPress(e, 'document-capture');
                }, true);

                // 监听器2: document级别 (bubble phase)
                document.addEventListener('keydown', function(e) {
                    handleEscKeyPress(e, 'document-bubble');
                }, false);

                window.MECAP_DIAGNOSTICS.eventListenersAttached += 2;
                logDiagnostic('LISTENER', 'Document event listeners attached');

                // 监听器2: window级别
                window.addEventListener('keydown', function(e) {
                    console.log(`[DIAG-WIN] Key: ${e.key}, Code: ${e.code}`);
                }, true);

                // 监听器3: body级别
                if (document.body) {
                    document.body.addEventListener('keydown', function(e) {
                        console.log(`[DIAG-BODY] Key: ${e.key}, Code: ${e.code}`);
                    });
                }

                // === 测试键盘事件触发 ===
                setTimeout(() => {
                    console.log('[DIAG] Simulating test keydown event...');
                    const testEvent = new KeyboardEvent('keydown', {
                        key: 'F12',
                        code: 'F12',
                        bubbles: true,
                        cancelable: true
                    });
                    document.dispatchEvent(testEvent);
                }, 1000);

                // === 检查原始函数 ===
                const hasHandleKeyboard = typeof handleKeyboard === 'function';
                const hasHandleEscapeKey = typeof handleEscapeKey === 'function';

                // === 返回诊断信息 ===
                JSON.stringify({
                    html: bodyContent > 0,
                    title: title,
                    size: bodyContent,
                    tauri: typeof window.__TAURI__ !== 'undefined',
                    focus: hasFocus,
                    visible: isVisible,
                    active: activeElement,
                    tabIndex: tabIndex,
                    keyboardFn: hasHandleKeyboard,
                    escFn: hasHandleEscapeKey
                });
            */

            match window.eval(diagnostic_js) {
                Ok(result) => {
                    println!("[OVERLAY] 🔍 Diagnostic JavaScript executed successfully");
                    println!("[OVERLAY] 🔍 Diagnostic result: {:?}", result);

                    // 尝试解析诊断结果（如果有返回值）
                    println!(
                        "[OVERLAY] 🔍 Result type: {:?}",
                        std::any::type_name_of_val(&result)
                    );

                    // 由于eval可能返回不同类型，我们先检查结果
                    match format!("{:?}", result).as_str() {
                        "()" => println!("[OVERLAY] 🔍 JavaScript returned void - this is expected for diagnostic code"),
                        other => {
                            println!("[OVERLAY] 🔍 JavaScript returned: {}", other);
                            // 尝试解析JSON如果看起来像JSON
                            if other.starts_with('"') && other.ends_with('"') {
                                let json_str = &other[1..other.len()-1]; // 移除引号
                                if let Ok(diagnostic_json) = serde_json::from_str::<serde_json::Value>(json_str) {
                                    println!("[OVERLAY] 🔍 Parsed diagnostics:");
                                    println!("[OVERLAY] 🔍   - HTML loaded: {}", diagnostic_json.get("html").unwrap_or(&serde_json::Value::Bool(false)));
                                    println!("[OVERLAY] 🔍   - Has focus: {}", diagnostic_json.get("focus").unwrap_or(&serde_json::Value::Bool(false)));
                                    println!("[OVERLAY] 🔍   - Is visible: {}", diagnostic_json.get("visible").unwrap_or(&serde_json::Value::Bool(false)));
                                    println!("[OVERLAY] 🔍   - Active element: {}", diagnostic_json.get("active").unwrap_or(&serde_json::Value::String("UNKNOWN".to_string())));
                                    println!("[OVERLAY] 🔍   - Tab index: {}", diagnostic_json.get("tabIndex").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(-1))));
                                    println!("[OVERLAY] 🔍   - Tauri available: {}", diagnostic_json.get("tauri").unwrap_or(&serde_json::Value::Bool(false)));
                                }
                            }
                        }
                    }
                }
                Err(e) => {
                    println!("[WARNING] Failed to execute diagnostic JavaScript: {}", e);
                }
            }

            // 发送UI配置到前端
            window
                .emit("ui-config", &ui_config)
                .map_err(|e| format!("Failed to send UI config: {}", e))?;

            // 启动窗口检测
            crate::modules::window::start_smart_window_detection(app_handle.clone()).await?;

            // 记录覆盖层信息
            let overlay_info = OverlayInfo {
                id: overlay_id.clone(),
                window_type: OverlayType::WindowHighlight,
                created_at: chrono::Utc::now().timestamp_millis() as u64,
                mode: overlay_mode.clone(),
            };

            if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                manager.insert(overlay_id.clone(), overlay_info);
            }

            // 等待窗口完全加载
            println!("[OVERLAY] 🎯 Waiting for window to load...");
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

            // 注册全局ESC快捷键
            println!("[OVERLAY] 🎯 Registering global ESC shortcut for capture exit");
            if let Err(e) =
                crate::modules::global_shortcuts::register_global_esc_shortcut(&app_handle)
            {
                println!("[OVERLAY] ⚠️ Failed to register global ESC shortcut: {}", e);
                println!("[OVERLAY] 🎯 Falling back to backend ESC key monitoring");

                // 如果全局快捷键注册失败，启动后端监听作为备用方案
                let app_handle_clone = app_handle.clone();
                let overlay_id_clone = overlay_id.clone();

                tokio::spawn(async move {
                    println!(
                        "[BACKEND-MONITOR] === STARTING COMPREHENSIVE BACKEND ESC MONITORING ==="
                    );

                    // 每100ms检查一次是否需要退出
                    let mut interval =
                        tokio::time::interval(tokio::time::Duration::from_millis(100));
                    let mut check_count = 0;
                    let mut last_diagnostic_dump = 0;

                    loop {
                        interval.tick().await;
                        check_count += 1;

                        // 检查overlay窗口是否还存在
                        if let Some(overlay_window) =
                            app_handle_clone.get_webview_window(&overlay_id_clone)
                        {
                            // 每5秒输出详细状态
                            if check_count % 50 == 0 {
                                let seconds = check_count / 10;
                                println!(
                                    "[BACKEND-MONITOR] 🎯 Active monitoring ({}s) - Check #{}",
                                    seconds, check_count
                                );

                                // 每10秒进行一次全面诊断
                                if seconds % 10 == 0 && seconds != last_diagnostic_dump {
                                    last_diagnostic_dump = seconds;
                                    println!("[BACKEND-MONITOR] 🔍 Performing comprehensive diagnostic check...");

                                    // 获取前端诊断状态
                                    let diagnostic_js = r#"
                                    JSON.stringify({
                                        diagnostics: window.MECAP_DIAGNOSTICS || {},
                                        escPressed: window.ESC_KEY_PRESSED || null,
                                        title: document.title,
                                        bodyEscAttr: document.body.getAttribute('data-esc-pressed'),
                                        bodyEscCount: document.body.getAttribute('data-esc-count'),
                                        escMarkers: Array.from(document.querySelectorAll('.esc-marker')).length,
                                        allMarkers: Array.from(document.querySelectorAll('[id*="esc-key-pressed"]')).map(el => el.id)
                                    });
                                "#;

                                    match overlay_window.eval(diagnostic_js) {
                                        Ok(result) => {
                                            let result_str = format!("{:?}", result);
                                            println!("[BACKEND-MONITOR] 🔍 Frontend diagnostic result: {}", result_str);

                                            // 尝试解析JSON
                                            if let Some(json_start) = result_str.find('{') {
                                                if let Some(json_end) = result_str.rfind('}') {
                                                    let json_str =
                                                        &result_str[json_start..=json_end];
                                                    if let Ok(diagnostic_data) =
                                                        serde_json::from_str::<serde_json::Value>(
                                                            json_str,
                                                        )
                                                    {
                                                        println!("[BACKEND-MONITOR] 🔍 Parsed diagnostics:");
                                                        if let Some(diagnostics) =
                                                            diagnostic_data.get("diagnostics")
                                                        {
                                                            println!("[BACKEND-MONITOR] 🔍   ESC press count: {}", diagnostics.get("escPressCount").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(0))));
                                                            println!("[BACKEND-MONITOR] 🔍   DOM signals sent: {}", diagnostics.get("domSignalsSent").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(0))));
                                                            println!("[BACKEND-MONITOR] 🔍   Last ESC time: {}", diagnostics.get("lastEscTime").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(0))));
                                                        }
                                                        println!(
                                                            "[BACKEND-MONITOR] 🔍   Title: {}",
                                                            diagnostic_data.get("title").unwrap_or(
                                                                &serde_json::Value::String(
                                                                    "N/A".to_string()
                                                                )
                                                            )
                                                        );
                                                        println!("[BACKEND-MONITOR] 🔍   Body ESC attr: {}", diagnostic_data.get("bodyEscAttr").unwrap_or(&serde_json::Value::Null));
                                                        println!("[BACKEND-MONITOR] 🔍   ESC markers: {}", diagnostic_data.get("escMarkers").unwrap_or(&serde_json::Value::Number(serde_json::Number::from(0))));
                                                        println!("[BACKEND-MONITOR] 🔍   All markers: {:?}", diagnostic_data.get("allMarkers").unwrap_or(&serde_json::Value::Array(vec![])));
                                                    }
                                                }
                                            }
                                        }
                                        Err(e) => {
                                            println!("[BACKEND-MONITOR] ❌ Failed to get diagnostic data: {}", e);
                                        }
                                    }
                                }
                            }

                            // 每次都检查ESC键标记（多种方法）
                            let comprehensive_check_js = r#"
                            {
                                // 方法1: 检查特定ID的标记
                                oldMarker: document.getElementById('esc-key-pressed-marker') !== null,

                                // 方法2: 检查所有ESC标记
                                anyEscMarker: document.querySelectorAll('[id*="esc-key-pressed"]').length > 0,

                                // 方法3: 检查CSS类标记
                                classMarkers: document.querySelectorAll('.esc-marker').length,

                                // 方法4: 检查页面标题
                                titleCheck: document.title.includes('ESC_PRESSED_'),

                                // 方法5: 检查全局变量
                                globalVar: typeof window.ESC_KEY_PRESSED !== 'undefined',

                                // 方法6: 检查body属性
                                bodyAttr: document.body.hasAttribute('data-esc-pressed'),

                                // 详细信息
                                details: {
                                    title: document.title,
                                    bodyEscAttr: document.body.getAttribute('data-esc-pressed'),
                                    bodyEscCount: document.body.getAttribute('data-esc-count'),
                                    globalVarValue: window.ESC_KEY_PRESSED || null
                                }
                            }
                        "#;

                            match overlay_window.eval(comprehensive_check_js) {
                                Ok(result) => {
                                    let result_str = format!("{:?}", result);

                                    // 检查是否有任何ESC信号
                                    let has_esc_signal = result_str.contains("true")
                                        || result_str.contains("ESC_PRESSED_")
                                        || result_str.contains("\"classMarkers\":")
                                            && !result_str.contains("\"classMarkers\":0");

                                    if has_esc_signal {
                                        println!(
                                            "[BACKEND-MONITOR] 🎯 ESC SIGNAL DETECTED! Result: {}",
                                            result_str
                                        );
                                        println!(
                                            "[BACKEND-MONITOR] 🎯 Triggering exit sequence..."
                                        );

                                        // 调用退出函数
                                        match crate::modules::ux::exit_capture_completely_direct(
                                            app_handle_clone.clone(),
                                        )
                                        .await
                                        {
                                            Ok(_) => {
                                                println!("[BACKEND-MONITOR] ✅ Successfully exited capture via backend monitor");
                                            }
                                            Err(e) => {
                                                println!("[BACKEND-MONITOR] ❌ Failed to exit capture: {}", e);
                                            }
                                        }
                                        break;
                                    } else if check_count % 100 == 0 {
                                        // 每10秒输出一次检查结果（用于调试）
                                        println!("[BACKEND-MONITOR] 🔍 No ESC signal detected. Check result: {}", result_str);
                                    }
                                }
                                Err(e) => {
                                    println!(
                                        "[BACKEND-MONITOR] ❌ Failed to evaluate ESC check: {}",
                                        e
                                    );
                                }
                            }

                            // 检查窗口是否仍然可见
                            if let Ok(is_visible) = overlay_window.is_visible() {
                                if !is_visible {
                                    println!("[BACKEND-MONITOR] 🎯 Overlay window became invisible - stopping monitor");
                                    break;
                                }
                            }
                        } else {
                            println!("[BACKEND-MONITOR] 🎯 Overlay window no longer exists - stopping monitor");
                            break;
                        }

                        // 超时保护：60秒后自动停止监听（增加到60秒用于调试）
                        if check_count > 600 {
                            println!(
                                "[BACKEND-MONITOR] 🎯 Backend ESC monitor timeout (60s) - stopping"
                            );
                            break;
                        }
                    }

                    println!("[BACKEND-MONITOR] === ESC MONITORING STOPPED ===");
                });
            } else {
                println!(
                    "[OVERLAY] ✅ Global ESC shortcut registered successfully - no fallback needed"
                );
            }

            println!("[OVERLAY] Window highlight overlay created: {}", overlay_id);
            Ok(overlay_id)
        }
        Err(e) => {
            let error_msg = format!("Failed to create window highlight overlay: {}", e);
            println!("[ERROR] {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 处理窗口选择完成事件
#[command]
pub async fn handle_window_selection_complete(
    app_handle: AppHandle,
    window_id: u32,
    action: String,
) -> Result<String, String> {
    println!(
        "[OVERLAY] Window selection completed: window_id={}, action={}",
        window_id, action
    );

    match action.as_str() {
        "capture" => {
            // 获取窗口信息
            if let Ok(Some(window_info)) =
                crate::modules::window::get_window_info_new(window_id as u64).await
            {
                // 执行窗口截图
                let area = crate::modules::capture::ScreenshotArea {
                    x: window_info.x as f64,
                    y: window_info.y as f64,
                    width: window_info.width as f64,
                    height: window_info.height as f64,
                };

                match crate::modules::capture::capture_region_new(area).await {
                    Ok(result) => {
                        println!("[OVERLAY] Window captured successfully");

                        // 关闭覆盖层
                        close_all_overlays(app_handle).await?;

                        Ok(format!(
                            "Window captured: {}x{}",
                            result.width.unwrap_or(0),
                            result.height.unwrap_or(0)
                        ))
                    }
                    Err(e) => Err(format!("Failed to capture window: {}", e)),
                }
            } else {
                Err("Window not found".to_string())
            }
        }
        "highlight" => {
            // 持续高亮窗口
            println!("[OVERLAY] Window highlight mode activated");
            Ok("Window highlighted".to_string())
        }
        _ => Err(format!("Unknown action: {}", action)),
    }
}

/// 获取覆盖层UI配置
#[command]
pub async fn get_overlay_ui_config() -> Result<OverlayUIConfig, String> {
    Ok(OverlayUIConfig::default())
}

/// 更新覆盖层UI配置
#[command]
pub async fn update_overlay_ui_config(config: OverlayUIConfig) -> Result<(), String> {
    println!("[OVERLAY] Updating UI config: {:?}", config);
    // TODO: 持久化配置到本地存储
    Ok(())
}









/// 获取覆盖层兼容性信息
#[command]
pub async fn get_overlay_compatibility_info() -> Result<String, String> {
    let mut info = Vec::new();
    info.push("=== Overlay Compatibility Information ===".to_string());

    // 平台信息
    #[cfg(target_os = "macos")]
    {
        info.push("Platform: macOS".to_string());
        info.push("✅ Full transparency support".to_string());
        info.push("✅ Always on top support".to_string());
        info.push("✅ Fullscreen overlay support".to_string());
    }

    #[cfg(target_os = "windows")]
    {
        info.push("Platform: Windows".to_string());
        info.push("✅ Full transparency support".to_string());
        info.push("✅ Always on top support".to_string());
        info.push("✅ Fullscreen overlay support".to_string());
    }

    #[cfg(target_os = "linux")]
    {
        info.push("Platform: Linux".to_string());

        // 检测合成器
        use std::process::Command;
        let mut compositor_found = false;
        let compositors = ["mutter", "kwin_x11", "kwin_wayland", "compiz", "xfwm4"];

        for compositor in &compositors {
            if let Ok(output) = Command::new("pgrep").arg(compositor).output() {
                if output.status.success() && !output.stdout.is_empty() {
                    info.push(format!("✅ Compositor detected: {}", compositor));
                    info.push("✅ Transparency support available".to_string());
                    compositor_found = true;
                    break;
                }
            }
        }

        if !compositor_found {
            info.push("⚠️ No compositor detected".to_string());
            info.push("⚠️ Limited transparency support".to_string());
        }
    }

    // 检测当前模式
    let current_mode = detect_best_overlay_mode().await;
    info.push(format!("\nRecommended mode: {:?}", current_mode));

    // 功能支持
    info.push("\n=== Feature Support ===".to_string());
    info.push("✅ Fullscreen overlays".to_string());
    info.push("✅ Multi-monitor support".to_string());
    info.push("✅ DPI scaling support".to_string());
    info.push("✅ Cross-platform compatibility".to_string());

    let report = info.join("\n");
    println!("[OVERLAY] Compatibility info: {}", report);
    Ok(report)
}

// === 混合截图功能扩展 ===

/// 创建多显示器截图覆盖层
#[command]
pub async fn create_multi_monitor_screenshot_overlay(
    app_handle: AppHandle,
) -> Result<Vec<String>, String> {
    use xcap::Monitor;

    println!("[OVERLAY] Creating multi-monitor screenshot overlay");

    // 获取所有显示器
    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;

    if monitors.is_empty() {
        return Err("No monitors detected".to_string());
    }

    let mut overlay_ids = Vec::new();
    let timestamp = chrono::Utc::now().timestamp_millis();

    // 为每个显示器创建覆盖层
    for (index, monitor) in monitors.iter().enumerate() {
        let overlay_id = format!("screenshot_overlay_{}_{}", index, timestamp);

        // 检测最佳覆盖层模式
        let overlay_mode = detect_best_overlay_mode().await;

        // 创建覆盖层窗口
        let mut window_builder = WebviewWindowBuilder::new(
            &app_handle,
            &overlay_id,
            WebviewUrl::App("overlay-screenshot.html".into()),
        )
        .title(&format!("Mecap Screenshot Overlay - Monitor {}", index))
        .inner_size(
            monitor.width().unwrap_or(1920) as f64,
            monitor.height().unwrap_or(1080) as f64,
        )
        .position(
            monitor.x().unwrap_or(0) as f64,
            monitor.y().unwrap_or(0) as f64,
        )
        .decorations(false)
        .always_on_top(true)
        .resizable(false)
        .skip_taskbar(true)
        .focused(index == 0); // 只有主显示器的覆盖层获得焦点

        // 根据模式设置透明度
        match overlay_mode {
            OverlayMode::FullTransparent => {
                window_builder = window_builder.transparent(true);
            }
            OverlayMode::SemiTransparent | OverlayMode::Opaque => {
                window_builder = window_builder.transparent(false);
            }
        }

        match window_builder.build() {
            Ok(window) => {
                // 应用模式特定的样式
                apply_overlay_mode_styles(&window, &overlay_mode).await?;

                // 发送显示器信息到前端
                let monitor_info = serde_json::json!({
                    "index": index,
                    "x": monitor.x().unwrap_or(0),
                    "y": monitor.y().unwrap_or(0),
                    "width": monitor.width().unwrap_or(1920),
                    "height": monitor.height().unwrap_or(1080),
                    "scale_factor": monitor.scale_factor().unwrap_or(1.0),
                    "is_primary": monitor.is_primary().unwrap_or(false)
                });

                if let Err(e) = window.emit("monitor-info", &monitor_info) {
                    println!("[WARNING] Failed to emit monitor info: {}", e);
                }

                // 记录覆盖层信息
                let overlay_info = OverlayInfo {
                    id: overlay_id.clone(),
                    window_type: OverlayType::ScreenshotOverlay,
                    created_at: timestamp as u64,
                    mode: overlay_mode,
                };

                if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
                    manager.insert(overlay_id.clone(), overlay_info);
                }

                overlay_ids.push(overlay_id);
                println!(
                    "[OVERLAY] Created screenshot overlay for monitor {}: {}x{} at ({}, {})",
                    index,
                    monitor.width().unwrap_or(1920),
                    monitor.height().unwrap_or(1080),
                    monitor.x().unwrap_or(0),
                    monitor.y().unwrap_or(0)
                );
            }
            Err(e) => {
                let error_msg = format!("Failed to create overlay for monitor {}: {}", index, e);
                println!("[ERROR] {}", error_msg);

                // 清理已创建的覆盖层
                for existing_id in &overlay_ids {
                    if let Some(existing_window) = app_handle.get_webview_window(existing_id) {
                        let _ = existing_window.close();
                    }
                }

                return Err(error_msg);
            }
        }
    }

    println!(
        "[OVERLAY] Successfully created {} screenshot overlays",
        overlay_ids.len()
    );
    Ok(overlay_ids)
}

/// 关闭所有截图覆盖层
#[command]
pub async fn close_all_screenshot_overlays(app_handle: AppHandle) -> Result<(), String> {
    println!("[OVERLAY] Closing all screenshot overlays");

    let overlay_ids: Vec<String> = {
        if let Ok(manager) = OVERLAY_MANAGER.lock() {
            manager
                .iter()
                .filter(|(_, info)| matches!(info.window_type, OverlayType::ScreenshotOverlay))
                .map(|(id, _)| id.clone())
                .collect()
        } else {
            return Err("Failed to access overlay manager".to_string());
        }
    };

    for overlay_id in overlay_ids {
        if let Some(window) = app_handle.get_webview_window(&overlay_id) {
            if let Err(e) = window.close() {
                println!("[WARNING] Failed to close overlay {}: {}", overlay_id, e);
            }
        }

        // 从管理器中移除
        if let Ok(mut manager) = OVERLAY_MANAGER.lock() {
            manager.remove(&overlay_id);
        }
    }

    println!("[OVERLAY] All screenshot overlays closed");
    Ok(())
}

/// 显示主窗口
#[command]
pub async fn show_main_window(app_handle: AppHandle) -> Result<(), String> {
    log::info!("[OVERLAY] Showing main window");

    if let Some(main_window) = app_handle.get_webview_window("main") {
        main_window.show().map_err(|e| format!("Failed to show main window: {}", e))?;
        main_window.set_focus().map_err(|e| format!("Failed to focus main window: {}", e))?;
        log::info!("[OVERLAY] Main window shown and focused");
        Ok(())
    } else {
        let error_msg = "Main window not found";
        log::error!("[OVERLAY] {}", error_msg);
        Err(error_msg.to_string())
    }
}

/// 🆕 为Overlay标注模式计算工具栏位置
#[command]
pub async fn calculate_overlay_toolbar_position(
    region: serde_json::Value,
    screen_width: u32,
    screen_height: u32,
) -> Result<serde_json::Value, String> {
    log::info!("[OVERLAY] 🔧 Calculating toolbar position for overlay annotation mode");

    // 解析区域坐标
    let x = region["x"].as_f64().ok_or("Invalid region x coordinate")?;
    let y = region["y"].as_f64().ok_or("Invalid region y coordinate")?;
    let width = region["width"].as_f64().ok_or("Invalid region width")?;
    let height = region["height"].as_f64().ok_or("Invalid region height")?;

    // 创建预览窗口信息结构（复用independent_toolbar的逻辑）
    let preview_bounds = crate::modules::independent_toolbar::PreviewWindowInfo {
        id: "overlay_annotation".to_string(),
        x: x as i32,
        y: y as i32,
        width: width as u32,
        height: height as u32,
    };

    // 创建屏幕信息
    let screen_info = crate::modules::independent_toolbar::ScreenInfo {
        width: screen_width,
        height: screen_height,
    };

    // 使用independent_toolbar的定位算法
    let position_result = crate::modules::independent_toolbar::calculate_optimal_position(
        &preview_bounds,
        &screen_info,
    );

    log::info!("[OVERLAY] 📊 Calculated overlay toolbar position: {:?}", position_result);

    // 转换为JSON返回给前端
    let result = serde_json::json!({
        "x": position_result.x,
        "y": position_result.y,
        "layout": position_result.layout,
        "position": position_result.position,
        "score": position_result.score,
        "reason": position_result.reason
    });

    Ok(result)
}

/// 🆕 开始Overlay标注模式
#[command]
pub async fn start_overlay_annotation_mode(
    app_handle: AppHandle,
    overlay_id: String,
    region: serde_json::Value,
) -> Result<(), String> {
    log::info!("[OVERLAY] 🎨 Starting overlay annotation mode: {}", overlay_id);

    // 解析区域数据
    let region_data = crate::modules::state::RegionData {
        x: region["x"].as_f64().unwrap_or(0.0),
        y: region["y"].as_f64().unwrap_or(0.0),
        width: region["width"].as_f64().unwrap_or(0.0),
        height: region["height"].as_f64().unwrap_or(0.0),
        screen_width: region["screen_width"].as_u64().unwrap_or(1920) as u32,
        screen_height: region["screen_height"].as_u64().unwrap_or(1080) as u32,
    };

    // 启动overlay标注会话
    crate::modules::state::start_overlay_annotation_session(
        app_handle.clone(),
        overlay_id.clone(),
        region_data,
    ).await?;

    // 通知前端进入标注模式
    app_handle.emit("overlay-annotation-started", &overlay_id)
        .map_err(|e| format!("Failed to emit annotation started event: {}", e))?;

    log::info!("[OVERLAY] ✅ Overlay annotation mode started successfully");
    Ok(())
}

/// 🆕 结束Overlay标注模式
#[command]
pub async fn end_overlay_annotation_mode(app_handle: AppHandle) -> Result<(), String> {
    log::info!("[OVERLAY] 🎨 Ending overlay annotation mode");

    // 结束overlay标注会话
    crate::modules::state::end_overlay_annotation_session(app_handle.clone()).await?;

    // 通知前端退出标注模式
    app_handle.emit("overlay-annotation-ended", ())
        .map_err(|e| format!("Failed to emit annotation ended event: {}", e))?;

    log::info!("[OVERLAY] ✅ Overlay annotation mode ended successfully");
    Ok(())
}

/// 🆕 更新Overlay工具栏位置
#[command]
pub async fn update_overlay_toolbar_position(
    app_handle: AppHandle,
    position: serde_json::Value,
) -> Result<(), String> {
    log::info!("[OVERLAY] 🔧 Updating overlay toolbar position");

    // 解析位置数据
    let toolbar_position = crate::modules::state::ToolbarPosition {
        x: position["x"].as_i64().unwrap_or(0) as i32,
        y: position["y"].as_i64().unwrap_or(0) as i32,
        layout: position["layout"].as_str().unwrap_or("vertical").to_string(),
        position: position["position"].as_str().unwrap_or("right").to_string(),
    };

    // 更新状态
    {
        let mut state = crate::modules::state::get_app_state_lock()?;
        state.overlay_annotation.toolbar_position = Some(toolbar_position.clone());
        state.last_updated = chrono::Utc::now().timestamp_millis() as u64;
    }

    // 通知前端位置更新
    app_handle.emit("overlay-toolbar-position-updated", &toolbar_position)
        .map_err(|e| format!("Failed to emit position update event: {}", e))?;

    log::info!("[OVERLAY] ✅ Overlay toolbar position updated: {:?}", toolbar_position);
    Ok(())
}

/// 🆕 保存标注后的图片
#[command]
pub async fn save_annotated_image(
    app_handle: AppHandle,
    canvas_data: String,
    _original_image_path: Option<String>,
    save_path: Option<String>,
) -> Result<String, String> {
    log::info!("[OVERLAY] 💾 Saving annotated image");

    // 解码Canvas数据（Base64格式）
    let canvas_data = canvas_data.strip_prefix("data:image/png;base64,")
        .or_else(|| canvas_data.strip_prefix("data:image/jpeg;base64,"))
        .unwrap_or(&canvas_data);

    use base64::{Engine as _, engine::general_purpose};
    let image_data = general_purpose::STANDARD.decode(canvas_data)
        .map_err(|e| format!("Failed to decode canvas data: {}", e))?;

    // 确定保存路径
    let final_save_path = if let Some(path) = save_path {
        path
    } else {
        // 生成默认保存路径
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let filename = format!("annotated_screenshot_{}.png", timestamp);

        // 获取用户的图片目录
        let pictures_dir = dirs::picture_dir()
            .ok_or("Failed to get pictures directory")?;

        pictures_dir.join("Mecap").join(filename)
            .to_string_lossy()
            .to_string()
    };

    // 确保目录存在
    if let Some(parent_dir) = std::path::Path::new(&final_save_path).parent() {
        std::fs::create_dir_all(parent_dir)
            .map_err(|e| format!("Failed to create directory: {}", e))?;
    }

    // 保存图片文件
    std::fs::write(&final_save_path, image_data)
        .map_err(|e| format!("Failed to save image: {}", e))?;

    // 更新状态
    crate::modules::state::save_overlay_annotation_data(app_handle.clone(), canvas_data.to_string()).await?;

    // 通知前端保存完成
    app_handle.emit("annotated-image-saved", &final_save_path)
        .map_err(|e| format!("Failed to emit save event: {}", e))?;

    log::info!("[OVERLAY] ✅ Annotated image saved: {}", final_save_path);
    Ok(final_save_path)
}

/// 🆕 导出标注后的图片到剪贴板
#[command]
pub async fn export_annotated_image_to_clipboard(
    app_handle: AppHandle,
    canvas_data: String,
) -> Result<(), String> {
    log::info!("[OVERLAY] 📋 Exporting annotated image to clipboard");

    // 解码Canvas数据
    let canvas_data = canvas_data.strip_prefix("data:image/png;base64,")
        .or_else(|| canvas_data.strip_prefix("data:image/jpeg;base64,"))
        .unwrap_or(&canvas_data);

    use base64::{Engine as _, engine::general_purpose};
    let image_data = general_purpose::STANDARD.decode(canvas_data)
        .map_err(|e| format!("Failed to decode canvas data: {}", e))?;

    // 使用arboard库复制到剪贴板
    use arboard::{Clipboard, ImageData};

    // 解析图片数据
    let img = image::load_from_memory(&image_data)
        .map_err(|e| format!("Failed to load image: {}", e))?;

    let rgba_img = img.to_rgba8();
    let (width, height) = rgba_img.dimensions();

    let img_data = ImageData {
        width: width as usize,
        height: height as usize,
        bytes: rgba_img.into_raw().into(),
    };

    let mut clipboard = Clipboard::new()
        .map_err(|e| format!("Failed to access clipboard: {}", e))?;

    clipboard.set_image(img_data)
        .map_err(|e| format!("Failed to copy to clipboard: {}", e))?;

    // 通知前端复制完成
    app_handle.emit("annotated-image-copied", ())
        .map_err(|e| format!("Failed to emit copy event: {}", e))?;

    log::info!("[OVERLAY] ✅ Annotated image copied to clipboard");
    Ok(())
}

/// 🆕 保存注解截图
#[command]
pub async fn save_annotated_screenshot(
    app_handle: AppHandle,
    image_data: String,
    format: Option<String>,
    annotations: Option<serde_json::Value>,
) -> Result<serde_json::Value, String> {
    log::info!("[OVERLAY] 🎨 Saving annotated screenshot");

    // 解码图像数据
    let image_data = image_data.strip_prefix("data:image/png;base64,")
        .or_else(|| image_data.strip_prefix("data:image/jpeg;base64,"))
        .unwrap_or(&image_data);

    use base64::{Engine as _, engine::general_purpose};
    let decoded_data = general_purpose::STANDARD.decode(image_data)
        .map_err(|e| format!("Failed to decode image data: {}", e))?;

    // 加载图像
    let img = image::load_from_memory(&decoded_data)
        .map_err(|e| format!("Failed to load image: {}", e))?;

    // 生成文件名
    let timestamp = chrono::Utc::now().timestamp_millis() as u64;
    let format_str = format.as_deref().unwrap_or("png");
    let filename = format!("annotation_{}.{}", timestamp, format_str);

    // 获取保存目录
    let screenshots_dir = crate::modules::editor::get_default_screenshots_dir()
        .map_err(|e| format!("Failed to get screenshots directory: {}", e))?;
    let save_path = screenshots_dir.join(&filename);

    // 保存图像
    let image_format = match format_str {
        "jpg" | "jpeg" => image::ImageFormat::Jpeg,
        "png" => image::ImageFormat::Png,
        _ => image::ImageFormat::Png,
    };

    img.save_with_format(&save_path, image_format)
        .map_err(|e| format!("Failed to save image: {}", e))?;

    // 获取文件信息
    let file_size = std::fs::metadata(&save_path)
        .map_err(|e| format!("Failed to get file metadata: {}", e))?
        .len();

    let result = serde_json::json!({
        "success": true,
        "path": save_path.to_string_lossy().to_string(),
        "filename": filename,
        "width": img.width(),
        "height": img.height(),
        "size_bytes": file_size,
        "timestamp": timestamp,
        "format": format_str
    });

    // 发送保存完成事件
    let event_payload = crate::modules::editor::ScreenshotSavedEvent {
        path: save_path.to_string_lossy().to_string(),
        width: img.width(),
        height: img.height(),
        size_bytes: file_size,
        timestamp,
        name: format!("Annotation {}", chrono::DateTime::from_timestamp_millis(timestamp as i64)
            .map(|dt| dt.format("%H:%M:%S").to_string())
            .unwrap_or_else(|| "Unknown".to_string())),
    };

    if let Err(e) = app_handle.emit("screenshot-saved", &event_payload) {
        log::warn!("[OVERLAY] 🎨 Failed to emit screenshot-saved event: {}", e);
    }

    log::info!("[OVERLAY] 🎨 Annotated screenshot saved: {}", save_path.display());
    Ok(result)
}

/// 🆕 复制注解截图到剪贴板
#[command]
pub async fn copy_annotated_screenshot(
    app_handle: AppHandle,
    image_data: String,
) -> Result<serde_json::Value, String> {
    log::info!("[OVERLAY] 🎨 Copying annotated screenshot to clipboard");

    // 解码图像数据
    let image_data = image_data.strip_prefix("data:image/png;base64,")
        .or_else(|| image_data.strip_prefix("data:image/jpeg;base64,"))
        .unwrap_or(&image_data);

    use base64::{Engine as _, engine::general_purpose};
    let decoded_data = general_purpose::STANDARD.decode(image_data)
        .map_err(|e| format!("Failed to decode image data: {}", e))?;

    // 加载图像
    let img = image::load_from_memory(&decoded_data)
        .map_err(|e| format!("Failed to load image: {}", e))?;

    let rgba_img = img.to_rgba8();
    let (width, height) = rgba_img.dimensions();

    // 复制到剪贴板
    use arboard::{Clipboard, ImageData};

    let img_data = ImageData {
        width: width as usize,
        height: height as usize,
        bytes: rgba_img.into_raw().into(),
    };

    let mut clipboard = Clipboard::new()
        .map_err(|e| format!("Failed to access clipboard: {}", e))?;

    clipboard.set_image(img_data)
        .map_err(|e| format!("Failed to copy to clipboard: {}", e))?;

    let result = serde_json::json!({
        "success": true,
        "width": width,
        "height": height,
        "timestamp": chrono::Utc::now().timestamp_millis() as u64
    });

    // 发送复制完成事件
    if let Err(e) = app_handle.emit("annotated-screenshot-copied", &result) {
        log::warn!("[OVERLAY] 🎨 Failed to emit copy event: {}", e);
    }

    log::info!("[OVERLAY] 🎨 Annotated screenshot copied to clipboard: {}x{}", width, height);
    Ok(result)
}

/// 🆕 获取保存选项
#[command]
pub async fn get_save_options() -> Result<serde_json::Value, String> {
    log::info!("[OVERLAY] 📁 Getting save options");

    // 获取用户的图片目录
    let pictures_dir = dirs::picture_dir()
        .map(|p| p.to_string_lossy().to_string())
        .unwrap_or_else(|| "~/Pictures".to_string());

    let mecap_dir = std::path::Path::new(&pictures_dir).join("Mecap");

    // 确保Mecap目录存在
    if !mecap_dir.exists() {
        std::fs::create_dir_all(&mecap_dir)
            .map_err(|e| format!("Failed to create Mecap directory: {}", e))?;
    }

    let options = serde_json::json!({
        "defaultDirectory": mecap_dir.to_string_lossy(),
        "supportedFormats": ["png", "jpg", "jpeg"],
        "defaultFormat": "png",
        "qualityOptions": {
            "png": { "compression": 6 },
            "jpg": { "quality": 90 }
        }
    });

    Ok(options)
}

/// 🆕 打开保存对话框 (简化版本)
#[command]
pub async fn show_save_dialog(
    _app_handle: AppHandle,
    default_filename: Option<String>,
) -> Result<Option<String>, String> {
    log::info!("[OVERLAY] 💬 Generating save path (dialog temporarily disabled)");

    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
    let default_name = default_filename.unwrap_or_else(|| {
        format!("annotated_screenshot_{}.png", timestamp)
    });

    // 获取默认保存目录
    let pictures_dir = dirs::picture_dir()
        .unwrap_or_else(|| std::path::PathBuf::from("~/Pictures"));
    let default_dir = pictures_dir.join("Mecap");

    // 确保目录存在
    std::fs::create_dir_all(&default_dir)
        .map_err(|e| format!("Failed to create directory: {}", e))?;

    let save_path = default_dir.join(default_name);
    let path_str = save_path.to_string_lossy().to_string();

    log::info!("[OVERLAY] ✅ Generated save path: {}", path_str);
    Ok(Some(path_str))
}
