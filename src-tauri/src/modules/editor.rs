// 截图编辑模块 - 处理编辑后的截图保存和相关功能
use base64::{engine::general_purpose, Engine as _};
use image::ImageFormat;
use log::{debug, info};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tauri::{command, AppHandle, Emitter};

/// 保存编辑后截图的请求参数
#[derive(Debug, Deserialize)]
pub struct SaveEditedScreenshotRequest {
    #[serde(rename = "imageDataUrl")]
    pub image_data_url: String,
    #[serde(rename = "finalPath")]
    pub final_path: Option<String>,
}

/// 保存编辑后截图的响应结果
#[derive(Debug, Serialize)]
pub struct SaveEditedScreenshotResponse {
    pub path: String,
    pub width: u32,
    pub height: u32,
    pub size_bytes: u64,
    pub timestamp: u64,
}

/// 截图保存事件的载荷数据
#[derive(Debug, Serialize)]
pub struct ScreenshotSavedEvent {
    pub path: String,
    pub width: u32,
    pub height: u32,
    pub size_bytes: u64,
    pub timestamp: u64,
    pub name: String,
}

/// 复制到剪贴板的请求参数
#[derive(Debug, Deserialize)]
pub struct CopyToClipboardRequest {
    #[serde(rename = "imageDataUrl")]
    pub image_data_url: String,
}

/// 获取默认截图保存目录
pub fn get_default_screenshots_dir() -> Result<PathBuf, String> {
    let pictures_dir = dirs::picture_dir()
        .ok_or_else(|| "无法获取图片目录".to_string())?;
    
    let screenshots_dir = pictures_dir.join("Mecap");
    
    // 确保目录存在
    std::fs::create_dir_all(&screenshots_dir)
        .map_err(|e| format!("创建截图目录失败: {}", e))?;
    
    Ok(screenshots_dir)
}

/// 生成唯一的文件名
fn generate_unique_filename(base_name: Option<&str>) -> String {
    let timestamp = chrono::Utc::now().timestamp_millis();
    match base_name {
        Some(name) if !name.is_empty() => {
            if name.ends_with(".png") {
                format!("{}_{}.png", &name[..name.len()-4], timestamp)
            } else {
                format!("{}_{}.png", name, timestamp)
            }
        }
        _ => format!("edited_screenshot_{}.png", timestamp)
    }
}

/// 解析Base64数据URL并提取图像数据
fn parse_data_url(data_url: &str) -> Result<Vec<u8>, String> {
    // 检查是否是有效的data URL格式
    if !data_url.starts_with("data:") {
        return Err("无效的数据URL格式".to_string());
    }
    
    // 查找base64数据部分
    let base64_start = data_url.find(",")
        .ok_or_else(|| "数据URL格式错误：找不到逗号分隔符".to_string())?;
    
    let base64_data = &data_url[base64_start + 1..];
    
    // 解码Base64数据
    general_purpose::STANDARD
        .decode(base64_data)
        .map_err(|e| format!("Base64解码失败: {}", e))
}

/// 保存编辑后的截图
#[command]
pub async fn save_edited_screenshot(
    app_handle: AppHandle,
    request: SaveEditedScreenshotRequest
) -> Result<SaveEditedScreenshotResponse, String> {
    info!("[EDITOR] 开始保存编辑后的截图");
    debug!("[EDITOR] 请求参数: final_path={:?}", request.final_path);
    
    // 解析Base64图像数据
    let image_data = parse_data_url(&request.image_data_url)?;
    debug!("[EDITOR] 成功解析Base64数据，大小: {} bytes", image_data.len());
    
    // 加载图像
    let image = image::load_from_memory(&image_data)
        .map_err(|e| format!("加载图像失败: {}", e))?;
    
    let width = image.width();
    let height = image.height();
    info!("[EDITOR] 图像尺寸: {}x{}", width, height);
    
    // 确定保存路径
    let save_path = match request.final_path {
        Some(path) if !path.is_empty() => {
            let path_buf = PathBuf::from(path);
            // 确保父目录存在
            if let Some(parent) = path_buf.parent() {
                std::fs::create_dir_all(parent)
                    .map_err(|e| format!("创建目录失败: {}", e))?;
            }
            path_buf
        }
        _ => {
            // 使用默认目录和生成的文件名
            let screenshots_dir = get_default_screenshots_dir()?;
            let filename = generate_unique_filename(None);
            screenshots_dir.join(filename)
        }
    };
    
    info!("[EDITOR] 保存路径: {:?}", save_path);
    
    // 保存图像为PNG格式
    image.save_with_format(&save_path, ImageFormat::Png)
        .map_err(|e| format!("保存图像失败: {}", e))?;
    
    // 获取文件大小
    let file_size = std::fs::metadata(&save_path)
        .map_err(|e| format!("获取文件信息失败: {}", e))?
        .len();
    
    let timestamp = chrono::Utc::now().timestamp_millis() as u64;
    
    let response = SaveEditedScreenshotResponse {
        path: save_path.to_string_lossy().to_string(),
        width,
        height,
        size_bytes: file_size,
        timestamp,
    };

    info!("[EDITOR] 截图保存成功: {:?}", response.path);

    // 发送全局事件通知截图已保存，用于集成到Recent Screenshots
    let _filename = save_path.file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("screenshot.png");

    let event_payload = ScreenshotSavedEvent {
        path: response.path.clone(),
        width: response.width,
        height: response.height,
        size_bytes: response.size_bytes,
        timestamp: response.timestamp,
        name: format!("Screenshot {}", chrono::DateTime::from_timestamp_millis(timestamp as i64)
            .map(|dt| dt.format("%H:%M:%S").to_string())
            .unwrap_or_else(|| "Unknown".to_string())),
    };

    info!("[EDITOR] 准备发送screenshot-saved事件: path={}, name={}", event_payload.path, event_payload.name);

    // 发送全局事件到所有窗口
    match app_handle.emit("screenshot-saved", &event_payload) {
        Ok(_) => {
            info!("[EDITOR] ✅ 成功发送screenshot-saved事件到所有窗口");
        }
        Err(e) => {
            log::warn!("[EDITOR] ⚠️ 发送screenshot-saved事件失败: {}", e);
            // 事件发送失败不影响保存操作的成功
        }
    }

    Ok(response)
}

/// 复制编辑后的图像到剪贴板
#[command]
pub async fn copy_edited_screenshot_to_clipboard(
    request: CopyToClipboardRequest
) -> Result<String, String> {
    info!("[EDITOR] 开始复制编辑后的截图到剪贴板");
    
    // 解析Base64图像数据
    let image_data = parse_data_url(&request.image_data_url)?;
    debug!("[EDITOR] 成功解析Base64数据，大小: {} bytes", image_data.len());
    
    // 加载图像
    let image = image::load_from_memory(&image_data)
        .map_err(|e| format!("加载图像失败: {}", e))?;
    
    // 转换为RGBA格式用于剪贴板
    let rgba_image = image.to_rgba8();
    let width = rgba_image.width();
    let height = rgba_image.height();
    
    // 使用Tauri的剪贴板插件
    // 注意：这里需要根据实际的剪贴板插件API进行调整
    // 目前先返回成功消息，实际实现需要集成剪贴板功能
    
    info!("[EDITOR] 图像复制到剪贴板成功: {}x{}", width, height);
    Ok(format!("图像已复制到剪贴板 ({}x{})", width, height))
}

/// 保存Canvas图像的请求参数
#[derive(Debug, Deserialize)]
pub struct SaveCanvasImageRequest {
    #[serde(rename = "imageData")]
    pub image_data: String,
    pub filename: String,
    pub format: String,
}

/// 保存Canvas图像
#[command]
pub async fn save_canvas_image(
    request: SaveCanvasImageRequest
) -> Result<String, String> {
    info!("[EDITOR] 开始保存Canvas图像: {}", request.filename);

    // 解码Base64数据
    let image_data = general_purpose::STANDARD
        .decode(&request.image_data)
        .map_err(|e| format!("Base64解码失败: {}", e))?;

    // 加载图像
    let image = image::load_from_memory(&image_data)
        .map_err(|e| format!("加载图像失败: {}", e))?;

    // 确定保存路径
    let screenshots_dir = get_default_screenshots_dir()?;
    let save_path = screenshots_dir.join(&request.filename);

    // 根据格式保存图像
    let format = match request.format.as_str() {
        "png" => ImageFormat::Png,
        "jpeg" | "jpg" => ImageFormat::Jpeg,
        "webp" => ImageFormat::WebP,
        _ => ImageFormat::Png, // 默认PNG
    };

    image.save_with_format(&save_path, format)
        .map_err(|e| format!("保存图像失败: {}", e))?;

    let path_str = save_path.to_string_lossy().to_string();
    info!("[EDITOR] Canvas图像保存成功: {}", path_str);

    Ok(path_str)
}

/// 复制图像到剪贴板的请求参数
#[derive(Debug, Deserialize)]
pub struct CopyImageToClipboardRequest {
    #[serde(rename = "imageData")]
    pub image_data: String,
}

/// 复制图像到剪贴板
#[command]
pub async fn copy_image_to_clipboard(
    request: CopyImageToClipboardRequest
) -> Result<String, String> {
    info!("[EDITOR] 开始复制图像到剪贴板");

    // 解码Base64数据
    let image_data = general_purpose::STANDARD
        .decode(&request.image_data)
        .map_err(|e| format!("Base64解码失败: {}", e))?;

    // 加载图像
    let image = image::load_from_memory(&image_data)
        .map_err(|e| format!("加载图像失败: {}", e))?;

    let width = image.width();
    let height = image.height();

    // TODO: 实现实际的剪贴板复制功能
    // 这里需要根据平台实现剪贴板操作

    info!("[EDITOR] 图像复制到剪贴板成功: {}x{}", width, height);
    Ok(format!("图像已复制到剪贴板 ({}x{})", width, height))
}

/// 保存标注元数据的请求参数
#[derive(Debug, Deserialize)]
pub struct SaveAnnotationMetadataRequest {
    pub metadata: String,
    pub filename: String,
}

/// 保存标注元数据
#[command]
pub async fn save_annotation_metadata(
    request: SaveAnnotationMetadataRequest
) -> Result<String, String> {
    info!("[EDITOR] 开始保存标注元数据: {}", request.filename);

    // 确定保存路径
    let screenshots_dir = get_default_screenshots_dir()?;
    let save_path = screenshots_dir.join(&request.filename);

    // 保存JSON文件
    std::fs::write(&save_path, &request.metadata)
        .map_err(|e| format!("保存元数据失败: {}", e))?;

    let path_str = save_path.to_string_lossy().to_string();
    info!("[EDITOR] 标注元数据保存成功: {}", path_str);

    Ok(path_str)
}

/// 获取编辑器配置
#[derive(Debug, Serialize)]
pub struct EditorConfig {
    pub default_save_dir: String,
    pub supported_formats: Vec<String>,
    pub max_image_size: u64,
    pub auto_save_enabled: bool,
}

#[command]
pub async fn get_editor_config() -> Result<EditorConfig, String> {
    let default_dir = get_default_screenshots_dir()?
        .to_string_lossy()
        .to_string();
    
    Ok(EditorConfig {
        default_save_dir: default_dir,
        supported_formats: vec!["PNG".to_string(), "JPEG".to_string()],
        max_image_size: 50 * 1024 * 1024, // 50MB
        auto_save_enabled: false,
    })
}

/// 验证图像数据URL格式
#[command]
pub async fn validate_image_data_url(data_url: String) -> Result<bool, String> {
    match parse_data_url(&data_url) {
        Ok(data) => {
            // 尝试加载图像以验证数据有效性
            match image::load_from_memory(&data) {
                Ok(_) => Ok(true),
                Err(e) => {
                    debug!("[EDITOR] 图像数据验证失败: {}", e);
                    Ok(false)
                }
            }
        }
        Err(e) => {
            debug!("[EDITOR] 数据URL解析失败: {}", e);
            Ok(false)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_unique_filename() {
        let filename1 = generate_unique_filename(None);
        let filename2 = generate_unique_filename(Some("test"));
        let filename3 = generate_unique_filename(Some("test.png"));
        
        assert!(filename1.starts_with("edited_screenshot_"));
        assert!(filename1.ends_with(".png"));
        assert!(filename2.starts_with("test_"));
        assert!(filename2.ends_with(".png"));
        assert!(filename3.starts_with("test_"));
        assert!(filename3.ends_with(".png"));
    }

    #[test]
    fn test_parse_data_url() {
        let valid_data_url = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==";
        let result = parse_data_url(valid_data_url);
        assert!(result.is_ok());
        
        let invalid_data_url = "invalid_data_url";
        let result = parse_data_url(invalid_data_url);
        assert!(result.is_err());
    }
}

/// 窗口选择事件数据
#[derive(Debug, Serialize)]
pub struct WindowSelectedEventData {
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
    #[serde(rename = "imageData")]
    pub image_data: String,
}

/// 发送窗口选择事件到主应用
#[command]
#[allow(non_snake_case)]
pub async fn send_window_selected_event(
    app_handle: AppHandle,
    x: i32,
    y: i32,
    width: u32,
    height: u32,
    imageData: String,
) -> Result<(), String> {
    info!("[EDITOR] 🎯 Sending window-selected event to main app");
    info!("[EDITOR] 🎯 Event data: x={}, y={}, width={}, height={}, image_data_length={}",
          x, y, width, height, imageData.len());

    let event_data = WindowSelectedEventData {
        x,
        y,
        width,
        height,
        image_data: imageData,
    };

    // 只发送全局事件，避免重复
    match app_handle.emit("window-selected", &event_data) {
        Ok(_) => info!("[EDITOR] ✅ Global event sent successfully"),
        Err(e) => log::error!("[EDITOR] ❌ Failed to send global event: {}", e),
    }

    info!("[EDITOR] ✅ Window-selected event sending completed");
    Ok(())
}
