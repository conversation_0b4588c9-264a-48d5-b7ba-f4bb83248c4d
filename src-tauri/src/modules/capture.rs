// 截图捕获模块 - 专门处理截图相关功能
use base64::{engine::general_purpose, Engine as _};
use serde::{Deserialize, Serialize};
use std::time::Instant;
use tauri::{command, AppHandle, Emitter};
use xcap::Monitor;
use log;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ScreenshotArea {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct WindowCaptureRequest {
    pub window_id: u32,
    pub window_title: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotResult {
    pub success: bool,
    pub message: String,
    pub path: Option<String>,
    pub base64: Option<String>,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub capture_time_ms: Option<u64>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotPreview {
    pub base64: String,
    pub width: u32,
    pub height: u32,
    pub capture_time_ms: u64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CaptureConfig {
    pub enable_optimized_capture: bool,
    pub default_format: String,
    pub quality: u8,
    pub enable_preview: bool,
    pub max_capture_size: u32,
}

impl Default for CaptureConfig {
    fn default() -> Self {
        Self {
            enable_optimized_capture: true,
            default_format: "png".to_string(),
            quality: 95,
            enable_preview: true,
            max_capture_size: 4096,
        }
    }
}

/// 捕获指定区域的截图
#[command]
pub async fn capture_region_new(area: ScreenshotArea) -> Result<ScreenshotResult, String> {
    log::info!(
        "[CAPTURE] Starting region capture: x={}, y={}, w={}, h={}",
        area.x, area.y, area.width, area.height
    );
    log::debug!(
        "[DEBUG] Parameter type: ScreenshotArea, size: {} bytes",
        std::mem::size_of_val(&area)
    );

    let start_time = Instant::now();

    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;

    if monitors.is_empty() {
        return Err("No monitors found".to_string());
    }

    // 使用第一个显示器进行截图
    let monitor = &monitors[0];
    let scale_factor = monitor.scale_factor().unwrap_or(1.0);

    log::info!("[CAPTURE] Monitor scale factor: {}", scale_factor);

    let raw_image = monitor
        .capture_image()
        .map_err(|e| format!("Failed to capture screen: {}", e))?;

    // 🔧 LOGICAL DIMENSION FIX: 计算物理坐标用于裁剪
    let physical_x = (area.x * scale_factor as f64) as u32;
    let physical_y = (area.y * scale_factor as f64) as u32;
    let physical_width = (area.width * scale_factor as f64) as u32;
    let physical_height = (area.height * scale_factor as f64) as u32;

    log::info!(
        "[CAPTURE] Logical area: {}x{} at ({},{}) → Physical area: {}x{} at ({},{})",
        area.width, area.height, area.x, area.y,
        physical_width, physical_height, physical_x, physical_y
    );

    // 裁剪指定区域（使用物理坐标）
    let cropped_physical = image::imageops::crop_imm(
        &raw_image,
        physical_x,
        physical_y,
        physical_width,
        physical_height,
    )
    .to_image();

    // 🔧 LOGICAL DIMENSION FIX: 缩放回逻辑尺寸
    let final_image = if scale_factor > 1.0 {
        log::info!(
            "[CAPTURE] Scaling cropped region to logical dimensions: {}x{} → {}x{}",
            cropped_physical.width(), cropped_physical.height(),
            area.width as u32, area.height as u32
        );

        image::imageops::resize(
            &cropped_physical,
            area.width as u32,
            area.height as u32,
            image::imageops::FilterType::Lanczos3
        )
    } else {
        cropped_physical
    };

    let capture_time = start_time.elapsed().as_millis() as u64;

    // 转换为base64
    let mut buffer = Vec::new();
    final_image
        .write_to(
            &mut std::io::Cursor::new(&mut buffer),
            image::ImageFormat::Png,
        )
        .map_err(|e| format!("Failed to encode image: {}", e))?;

    let base64_data = general_purpose::STANDARD.encode(&buffer);

    log::info!(
        "[CAPTURE] Region capture completed: final image {}x{} (logical dimensions)",
        final_image.width(), final_image.height()
    );

    Ok(ScreenshotResult {
        success: true,
        message: "Region captured successfully".to_string(),
        path: None,
        base64: Some(base64_data),
        width: Some(final_image.width()),
        height: Some(final_image.height()),
        capture_time_ms: Some(capture_time),
    })
}

/// 捕获全屏截图
#[command(async)]
pub async fn capture_fullscreen(save_path: Option<String>) -> Result<ScreenshotResult, String> {
    log::info!("[CAPTURE] Starting fullscreen capture");

    let start_time = Instant::now();
    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;

    if monitors.is_empty() {
        return Err("No monitors found".to_string());
    }

    // 使用主显示器
    let monitor = &monitors[0];
    let scale_factor = monitor.scale_factor().unwrap_or(1.0);

    log::info!("[CAPTURE] Monitor scale factor: {}", scale_factor);

    let raw_image = monitor
        .capture_image()
        .map_err(|e| format!("Failed to capture screen: {}", e))?;

    // 🔧 LOGICAL DIMENSION FIX: 缩放到逻辑尺寸
    let final_image = if scale_factor > 1.0 {
        let logical_width = monitor.width().unwrap_or(1920);
        let logical_height = monitor.height().unwrap_or(1080);

        log::info!(
            "[CAPTURE] Scaling fullscreen to logical dimensions: {}x{} → {}x{}",
            raw_image.width(), raw_image.height(),
            logical_width, logical_height
        );

        image::imageops::resize(
            &raw_image,
            logical_width,
            logical_height,
            image::imageops::FilterType::Lanczos3
        )
    } else {
        raw_image
    };

    let capture_time = start_time.elapsed().as_millis() as u64;

    // 转换为base64
    let mut buffer = Vec::new();
    final_image
        .write_to(
            &mut std::io::Cursor::new(&mut buffer),
            image::ImageFormat::Png,
        )
        .map_err(|e| format!("Failed to encode image: {}", e))?;

    let base64_data = general_purpose::STANDARD.encode(&buffer);

    // 保存文件（如果指定了路径）
    let saved_path = if let Some(path) = save_path {
        final_image
            .save(&path)
            .map_err(|e| format!("Failed to save image: {}", e))?;
        Some(path)
    } else {
        None
    };

    log::info!(
        "[CAPTURE] Fullscreen capture completed: final image {}x{} (logical dimensions)",
        final_image.width(), final_image.height()
    );

    Ok(ScreenshotResult {
        success: true,
        message: "Fullscreen captured successfully".to_string(),
        path: saved_path,
        base64: Some(base64_data),
        width: Some(final_image.width()),
        height: Some(final_image.height()),
        capture_time_ms: Some(capture_time),
    })
}

/// 获取截图配置
#[command(async)]
pub async fn get_capture_config() -> Result<CaptureConfig, String> {
    Ok(CaptureConfig::default())
}

/// 🔧 NEW: 获取窗口所在显示器的缩放因子
async fn get_window_scale_factor(window_info: &crate::modules::hybrid_screenshot::WindowInfo) -> Result<f32, String> {
    log::debug!(
        "[CAPTURE] 🔍 Getting scale factor for window at ({}, {})",
        window_info.x, window_info.y
    );

    // 获取所有显示器
    let monitors = Monitor::all().map_err(|e| format!("Failed to get monitors: {}", e))?;

    // 查找包含窗口中心点的显示器
    let window_center_x = window_info.x + (window_info.width as i32) / 2;
    let window_center_y = window_info.y + (window_info.height as i32) / 2;

    for monitor in &monitors {
        if let (Ok(m_x), Ok(m_y), Ok(m_width), Ok(m_height)) =
            (monitor.x(), monitor.y(), monitor.width(), monitor.height()) {

            // 检查窗口中心是否在这个显示器内
            if window_center_x >= m_x && window_center_x < m_x + m_width as i32 &&
               window_center_y >= m_y && window_center_y < m_y + m_height as i32 {

                let scale_factor = monitor.scale_factor().unwrap_or(1.0);
                log::debug!(
                    "[CAPTURE] 🔍 Found monitor for window: {}x{} at ({}, {}), scale_factor={}",
                    m_width, m_height, m_x, m_y, scale_factor
                );
                return Ok(scale_factor);
            }
        }
    }

    // 如果没找到匹配的显示器，使用主显示器的缩放因子
    if let Some(primary_monitor) = monitors.iter().find(|m| m.is_primary().unwrap_or(false)) {
        let scale_factor = primary_monitor.scale_factor().unwrap_or(1.0);
        log::debug!(
            "[CAPTURE] 🔍 Using primary monitor scale factor: {}",
            scale_factor
        );
        Ok(scale_factor)
    } else {
        log::warn!("[CAPTURE] ⚠️ No monitor found for window, defaulting to scale factor 1.0");
        Ok(1.0)
    }
}

/// 🔧 BUG FIX: 基于坐标的实时窗口截图命令
#[command(async)]
pub async fn capture_window_at_coordinates(app_handle: AppHandle, x: i32, y: i32) -> Result<ScreenshotResult, String> {
    log::info!(
        "[CAPTURE] 🎯 Starting coordinate-based window capture at ({}, {})",
        x, y
    );

    let start_time = Instant::now();

    // 🔧 OVERLAY FIX: 临时隐藏所有覆盖层窗口，避免干扰截图
    log::info!("[CAPTURE] 🔧 Temporarily hiding overlay windows for clean capture");
    if let Err(e) = crate::modules::overlay::hide_all_overlays(app_handle.clone()).await {
        log::warn!("[CAPTURE] ⚠️ Failed to hide overlays: {}, continuing anyway", e);
    }

    // 等待一小段时间确保窗口隐藏生效
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 使用混合截图模块的智能检测功能
    let window_info = match crate::modules::hybrid_screenshot::detect_window_smart(x, y).await {
        Ok(Some(window)) => window,
        Ok(None) => {
            let error_msg = format!("No window found at coordinates ({}, {})", x, y);
            log::error!("[CAPTURE] ❌ {}", error_msg);
            return Err(error_msg);
        }
        Err(e) => {
            let error_msg = format!("Window detection failed: {}", e);
            log::error!("[CAPTURE] ❌ {}", error_msg);
            return Err(error_msg);
        }
    };

    // 🔧 NEW: 获取窗口所在显示器的缩放因子
    let scale_factor = match get_window_scale_factor(&window_info).await {
        Ok(factor) => factor,
        Err(e) => {
            log::warn!("[CAPTURE] ⚠️ Failed to get scale factor: {}, using 1.0", e);
            1.0
        }
    };

    log::info!(
        "[CAPTURE] 🎯 Detected window: {:?} ({}x{} at {},{}) handle={}, scale_factor={}",
        window_info.title,
        window_info.width,
        window_info.height,
        window_info.x,
        window_info.y,
        window_info.handle,
        scale_factor
    );

    // 使用xcap获取所有窗口
    let windows = xcap::Window::all().map_err(|e| {
        let error_msg = format!("Failed to get windows: {}", e);
        log::error!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    log::debug!(
        "[CAPTURE] 🎯 Found {} total windows from xcap",
        windows.len()
    );

    // 通过窗口位置和尺寸查找匹配的xcap窗口
    let target_window = windows.iter().find(|w| {
        if let (Ok(w_x), Ok(w_y), Ok(w_width), Ok(w_height)) = (w.x(), w.y(), w.width(), w.height()) {
            // 🔧 OVERLAY FIX: 过滤掉Mecap覆盖层窗口
            if let Ok(title) = w.title() {
                let title_lower = title.to_lowercase();
                if title_lower.contains("mecap") && (title_lower.contains("highlight") || title_lower.contains("overlay")) {
                    log::debug!("[CAPTURE] 🚫 Skipping Mecap overlay window: '{}'", title);
                    return false;
                }
            }

            // 检查位置和尺寸是否匹配（允许小的误差）
            let x_match = (w_x - window_info.x).abs() <= 5;
            let y_match = (w_y - window_info.y).abs() <= 5;
            let width_match = (w_width as i32 - window_info.width as i32).abs() <= 10;
            let height_match = (w_height as i32 - window_info.height as i32).abs() <= 10;

            if x_match && y_match && width_match && height_match {
                if let Ok(title) = w.title() {
                    log::info!("[CAPTURE] 🎯 Found matching xcap window: '{}' ({}x{} at {},{}) id={:?}",
                             title, w_width, w_height, w_x, w_y, w.id());
                }
                return true;
            }
        }
        false
    }).ok_or_else(|| {
        // 如果通过位置找不到，列出所有可用窗口用于调试
        log::debug!("[CAPTURE] 🎯 Available windows in xcap:");
        for (i, w) in windows.iter().enumerate() {
            if let (Ok(title), Ok(id), Ok(x), Ok(y), Ok(width), Ok(height)) =
                (w.title(), w.id(), w.x(), w.y(), w.width(), w.height()) {
                log::debug!("[CAPTURE] 🎯   {}: id={}, title='{}' ({}x{} at {},{})",
                         i, id, title, width, height, x, y);
            }
        }

        let error_msg = format!("No matching xcap window found for detected window at ({},{}) {}x{}",
                               window_info.x, window_info.y, window_info.width, window_info.height);
        log::error!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    let window_title = target_window.title().unwrap_or_default();
    let window_width = target_window.width().unwrap_or(0);
    let window_height = target_window.height().unwrap_or(0);
    let window_id = target_window.id().unwrap_or(0);

    log::info!(
        "[CAPTURE] 🎯 Target window: '{}' (id={}, {}x{})",
        window_title, window_id, window_width, window_height
    );

    // 使用xcap的Window::capture_image接口捕获窗口
    let raw_image = target_window.capture_image().map_err(|e| {
        let error_msg = format!("Failed to capture window image: {}", e);
        log::error!("[CAPTURE] ❌ {}", error_msg);
        error_msg
    })?;

    let raw_width = raw_image.width();
    let raw_height = raw_image.height();
    let expected_width = (window_info.width as f32 * scale_factor) as u32;
    let expected_height = (window_info.height as f32 * scale_factor) as u32;

    log::info!(
        "[CAPTURE] 📏 Image dimensions - Raw: {}x{}, Expected: {}x{}, Logical: {}x{}, Scale: {}",
        raw_width, raw_height,
        expected_width, expected_height,
        window_info.width, window_info.height,
        scale_factor
    );

    // 🔧 LOGICAL DIMENSION FIX: 始终缩放到逻辑尺寸以保持一致性
    let final_image = if scale_factor > 1.0 {
        // 在HiDPI显示器上，始终缩放到逻辑尺寸
        let logical_width = window_info.width;
        let logical_height = window_info.height;

        log::info!(
            "[CAPTURE] 🔧 Scaling to logical dimensions: {}x{} → {}x{} (scale factor: {})",
            raw_width, raw_height, logical_width, logical_height, scale_factor
        );

        // 缩放图像到逻辑尺寸
        let scaled_image = image::imageops::resize(
            &raw_image,
            logical_width,
            logical_height,
            image::imageops::FilterType::Lanczos3
        );

        log::info!("[CAPTURE] ✅ Image scaled to logical dimensions: {}x{}", scaled_image.width(), scaled_image.height());
        scaled_image
    } else {
        log::info!("[CAPTURE] ✅ Using raw image (no scaling needed): {}x{}", raw_width, raw_height);
        raw_image
    };

    let capture_time = start_time.elapsed().as_millis() as u64;

    // 🔧 PERFORMANCE: 移除冗余的base64转换，预览窗口直接使用文件路径
    log::info!("[CAPTURE] Skipping base64 conversion - preview uses file path directly");

    // 🔧 NEW: 自动保存截图到永久目录
    let save_path = {
        let screenshots_dir = crate::modules::editor::get_default_screenshots_dir()
            .map_err(|e| format!("Failed to get screenshots directory: {}", e))?;

        let timestamp = chrono::Utc::now().timestamp_millis();
        let safe_title = window_title
            .chars()
            .filter(|c| c.is_alphanumeric() || *c == ' ' || *c == '-' || *c == '_')
            .collect::<String>()
            .replace(' ', "_");

        let filename = format!("window_{}_{}.png", safe_title, timestamp);
        screenshots_dir.join(filename)
    };

    // 保存图像到永久目录
    final_image.save(&save_path)
        .map_err(|e| format!("Failed to save screenshot: {}", e))?;

    let saved_path_str = save_path.to_string_lossy().to_string();
    log::info!("[CAPTURE] 💾 Screenshot automatically saved to: {}", saved_path_str);

    // 🔧 NEW: 发送screenshot-saved事件，用于集成Recent Screenshots
    let file_size = std::fs::metadata(&save_path)
        .map(|metadata| metadata.len())
        .unwrap_or(0);

    let timestamp = chrono::Utc::now().timestamp_millis() as u64;

    let event_payload = crate::modules::editor::ScreenshotSavedEvent {
        path: saved_path_str.clone(),
        width: final_image.width(),
        height: final_image.height(),
        size_bytes: file_size,
        timestamp,
        name: format!("Window Screenshot {}", chrono::DateTime::from_timestamp_millis(timestamp as i64)
            .map(|dt| dt.with_timezone(&chrono::Local).format("%H:%M:%S").to_string())
            .unwrap_or_else(|| "Unknown".to_string())),
    };

    log::info!("[CAPTURE] 📤 准备发送screenshot-saved事件: path={}, name={}", event_payload.path, event_payload.name);

    // 发送全局事件到所有窗口
    match app_handle.emit("screenshot-saved", &event_payload) {
        Ok(_) => {
            log::info!("[CAPTURE] ✅ 成功发送screenshot-saved事件到所有窗口");
        }
        Err(e) => {
            log::warn!("[CAPTURE] ⚠️ 发送screenshot-saved事件失败: {}", e);
            // 事件发送失败不影响截图保存操作的成功
        }
    }

    // 🆕 创建截图预览窗口，传递原始窗口坐标实现精确定位
    log::info!("[CAPTURE] 🖼️ 创建截图预览窗口，使用原始窗口坐标: ({}, {})", window_info.x, window_info.y);
    match crate::modules::overlay::create_screenshot_preview_window(
        app_handle.clone(),
        saved_path_str.clone(),
        final_image.width(),
        final_image.height(),
        Some(window_info.x),
        Some(window_info.y),
    ).await {
        Ok(preview_id) => {
            log::info!("[CAPTURE] ✅ 截图预览窗口创建成功: {}", preview_id);
        }
        Err(e) => {
            log::warn!("[CAPTURE] ⚠️ 截图预览窗口创建失败: {}", e);
            // 预览窗口创建失败不影响截图保存操作的成功
        }
    }

    // 🔧 FIX: 停止窗口检测系统，避免资源浪费和后续干扰
    log::info!("[CAPTURE] 🛑 Stopping window detection after capture completion");
    if let Err(e) = crate::modules::window::stop_smart_window_detection().await {
        log::warn!("[CAPTURE] ⚠️ Failed to stop window detection: {}", e);
        // 窗口检测停止失败不影响截图保存操作的成功
    } else {
        log::info!("[CAPTURE] ✅ Window detection stopped successfully");
    }

    // 🔧 FIX: 关闭所有覆盖层，确保没有后台检测继续运行
    log::info!("[CAPTURE] 🛑 Closing all overlay windows after capture completion");
    if let Err(e) = crate::modules::overlay::close_all_overlays(app_handle.clone()).await {
        log::warn!("[CAPTURE] ⚠️ Failed to close overlay windows: {}", e);
        // 覆盖层关闭失败不影响截图保存操作的成功
    } else {
        log::info!("[CAPTURE] ✅ All overlay windows closed successfully");
    }

    let result = ScreenshotResult {
        success: true,
        message: "Window captured successfully".to_string(),
        path: Some(saved_path_str.clone()),
        base64: None, // 🔧 PERFORMANCE: 移除冗余的base64数据
        width: Some(final_image.width()),
        height: Some(final_image.height()),
        capture_time_ms: Some(capture_time),
    };

    log::info!(
        "[CAPTURE] 🎯 Coordinate-based window capture completed in {}ms",
        capture_time
    );

    // 🔧 LOGICAL DIMENSION SUMMARY: 最终结果摘要
    log::info!(
        "[CAPTURE] 📊 LOGICAL DIMENSION SUMMARY - Window: {}x{} (logical) → Image: {}x{} (final) | Scale: {} | Raw: {}x{}",
        window_info.width, window_info.height,
        final_image.width(), final_image.height(),
        scale_factor,
        raw_width, raw_height
    );

    Ok(result)
}
