// 原生窗口菜单模块 - 实现跨平台菜单系统
use serde::{Deserialize, Serialize};
use tauri::menu::{Menu<PERSON>uilder, MenuItemBuilder, PredefinedMenuItem, SubmenuBuilder};
use tauri::{command, AppHandle, Emitter, Manager};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MenuConfig {
    pub show_capture: bool,
    pub show_manage_screenshots: bool,
    pub show_exit: bool,
    pub capture_accelerator: Option<String>,
}

impl Default for MenuConfig {
    fn default() -> Self {
        Self {
            show_capture: true,
            show_manage_screenshots: true,
            show_exit: true,
            capture_accelerator: Some("Cmd+Shift+C".to_string()),
        }
    }
}

/// 创建原生应用菜单
#[command]
pub async fn create_native_menu(
    app_handle: AppHandle,
    config: Option<MenuConfig>,
) -> Result<String, String> {
    println!("[MENU] Creating native application menu");

    let menu_config = config.unwrap_or_default();

    // 根据平台创建不同的菜单结构
    #[cfg(target_os = "macos")]
    {
        create_macos_menu(&app_handle, &menu_config).await
    }

    #[cfg(not(target_os = "macos"))]
    {
        create_desktop_menu(&app_handle, &menu_config).await
    }
}

/// 创建macOS风格的菜单栏
#[cfg(target_os = "macos")]
async fn create_macos_menu(app_handle: &AppHandle, config: &MenuConfig) -> Result<String, String> {
    println!("[MENU] Creating macOS menu bar");

    // 应用菜单（Mecap）
    let mut app_menu_builder = SubmenuBuilder::new(app_handle, "Mecap");

    // 关于菜单项
    app_menu_builder = app_menu_builder.item(
        &MenuItemBuilder::with_id("about", "About Mecap")
            .build(app_handle)
            .map_err(|e| format!("Failed to create about menu item: {}", e))?,
    );

    app_menu_builder = app_menu_builder.separator();

    // 偏好设置
    app_menu_builder = app_menu_builder.item(
        &MenuItemBuilder::with_id("preferences", "Preferences...")
            .accelerator("Cmd+,")
            .build(app_handle)
            .map_err(|e| format!("Failed to create preferences menu item: {}", e))?,
    );

    app_menu_builder = app_menu_builder.separator();

    // 隐藏应用
    app_menu_builder = app_menu_builder.item(
        &PredefinedMenuItem::hide(app_handle, Some("Hide Mecap"))
            .map_err(|e| format!("Failed to create hide menu item: {}", e))?,
    );

    // 隐藏其他应用
    app_menu_builder = app_menu_builder.item(
        &PredefinedMenuItem::hide_others(app_handle, None)
            .map_err(|e| format!("Failed to create hide others menu item: {}", e))?,
    );

    // 显示全部
    app_menu_builder = app_menu_builder.item(
        &PredefinedMenuItem::show_all(app_handle, None)
            .map_err(|e| format!("Failed to create show all menu item: {}", e))?,
    );

    app_menu_builder = app_menu_builder.separator();

    // 退出应用
    if config.show_exit {
        app_menu_builder = app_menu_builder.item(
            &PredefinedMenuItem::quit(app_handle, Some("Quit Mecap"))
                .map_err(|e| format!("Failed to create quit menu item: {}", e))?,
        );
    }

    let app_menu = app_menu_builder
        .build()
        .map_err(|e| format!("Failed to build app menu: {}", e))?;

    // 截图菜单
    let mut capture_menu_builder = SubmenuBuilder::new(app_handle, "Capture");

    // Single unified capture option as per design document
    let accelerator = config
        .capture_accelerator
        .as_deref()
        .unwrap_or("Cmd+Shift+C");
    capture_menu_builder = capture_menu_builder.item(
        &MenuItemBuilder::with_id("capture_screen", "Capture Screen")
            .accelerator(accelerator)
            .build(app_handle)
            .map_err(|e| format!("Failed to create capture screen menu item: {}", e))?,
    );

    capture_menu_builder = capture_menu_builder.separator();

    if config.show_manage_screenshots {
        capture_menu_builder = capture_menu_builder.item(
            &MenuItemBuilder::with_id("manage_screenshots", "Manage Screenshots")
                .accelerator("Cmd+Shift+M")
                .build(app_handle)
                .map_err(|e| format!("Failed to create manage screenshots menu item: {}", e))?,
        );
    }

    let capture_menu = capture_menu_builder
        .build()
        .map_err(|e| format!("Failed to build capture menu: {}", e))?;

    // 编辑菜单
    let edit_menu = SubmenuBuilder::new(app_handle, "Edit")
        .item(
            &PredefinedMenuItem::undo(app_handle, None)
                .map_err(|e| format!("Failed to create undo menu item: {}", e))?,
        )
        .item(
            &PredefinedMenuItem::redo(app_handle, None)
                .map_err(|e| format!("Failed to create redo menu item: {}", e))?,
        )
        .separator()
        .item(
            &PredefinedMenuItem::cut(app_handle, None)
                .map_err(|e| format!("Failed to create cut menu item: {}", e))?,
        )
        .item(
            &PredefinedMenuItem::copy(app_handle, None)
                .map_err(|e| format!("Failed to create copy menu item: {}", e))?,
        )
        .item(
            &PredefinedMenuItem::paste(app_handle, None)
                .map_err(|e| format!("Failed to create paste menu item: {}", e))?,
        )
        .separator()
        .item(
            &PredefinedMenuItem::select_all(app_handle, None)
                .map_err(|e| format!("Failed to create select all menu item: {}", e))?,
        )
        .build()
        .map_err(|e| format!("Failed to build edit menu: {}", e))?;

    // 窗口菜单
    let window_menu = SubmenuBuilder::new(app_handle, "Window")
        .item(
            &PredefinedMenuItem::minimize(app_handle, None)
                .map_err(|e| format!("Failed to create minimize menu item: {}", e))?,
        )
        .item(
            &PredefinedMenuItem::close_window(app_handle, None)
                .map_err(|e| format!("Failed to create close window menu item: {}", e))?,
        )
        .build()
        .map_err(|e| format!("Failed to build window menu: {}", e))?;

    // 构建完整菜单
    let menu = MenuBuilder::new(app_handle)
        .items(&[&app_menu, &capture_menu, &edit_menu, &window_menu])
        .build()
        .map_err(|e| format!("Failed to build main menu: {}", e))?;

    // 设置为应用菜单
    app_handle
        .set_menu(menu)
        .map_err(|e| format!("Failed to set app menu: {}", e))?;

    println!("[MENU] macOS menu bar created successfully");
    Ok("macOS menu bar created".to_string())
}

/// 创建Windows/Linux风格的窗口菜单
#[cfg(not(target_os = "macos"))]
async fn create_desktop_menu(
    app_handle: &AppHandle,
    config: &MenuConfig,
) -> Result<String, String> {
    println!("[MENU] Creating desktop window menu");

    // 文件菜单
    let mut file_menu_builder = SubmenuBuilder::new(app_handle, "File");

    // Single unified capture option as per design document
    let accelerator = config
        .capture_accelerator
        .as_deref()
        .unwrap_or("Ctrl+Shift+C");
    file_menu_builder = file_menu_builder.item(
        &MenuItemBuilder::with_id("capture_screen", "Capture Screen")
            .accelerator(accelerator)
            .build(app_handle)
            .map_err(|e| format!("Failed to create capture screen menu item: {}", e))?,
    );

    file_menu_builder = file_menu_builder.separator();

    if config.show_manage_screenshots {
        file_menu_builder = file_menu_builder.item(
            &MenuItemBuilder::with_id("manage_screenshots", "Manage Screenshots")
                .accelerator("Ctrl+Shift+M")
                .build(app_handle)
                .map_err(|e| format!("Failed to create manage screenshots menu item: {}", e))?,
        );
    }

    file_menu_builder = file_menu_builder.separator();

    // 偏好设置
    file_menu_builder = file_menu_builder.item(
        &MenuItemBuilder::with_id("preferences", "Preferences...")
            .accelerator("Ctrl+,")
            .build(app_handle)
            .map_err(|e| format!("Failed to create preferences menu item: {}", e))?,
    );

    file_menu_builder = file_menu_builder.separator();

    // 退出应用
    if config.show_exit {
        file_menu_builder = file_menu_builder.item(
            &MenuItemBuilder::with_id("exit_app", "Exit")
                .accelerator("Ctrl+Q")
                .build(app_handle)
                .map_err(|e| format!("Failed to create exit menu item: {}", e))?,
        );
    }

    let file_menu = file_menu_builder
        .build()
        .map_err(|e| format!("Failed to build file menu: {}", e))?;

    // 编辑菜单
    let edit_menu = SubmenuBuilder::new(app_handle, "Edit")
        .item(
            &PredefinedMenuItem::undo(app_handle, None)
                .map_err(|e| format!("Failed to create undo menu item: {}", e))?,
        )
        .item(
            &PredefinedMenuItem::redo(app_handle, None)
                .map_err(|e| format!("Failed to create redo menu item: {}", e))?,
        )
        .separator()
        .item(
            &PredefinedMenuItem::cut(app_handle, None)
                .map_err(|e| format!("Failed to create cut menu item: {}", e))?,
        )
        .item(
            &PredefinedMenuItem::copy(app_handle, None)
                .map_err(|e| format!("Failed to create copy menu item: {}", e))?,
        )
        .item(
            &PredefinedMenuItem::paste(app_handle, None)
                .map_err(|e| format!("Failed to create paste menu item: {}", e))?,
        )
        .separator()
        .item(
            &PredefinedMenuItem::select_all(app_handle, None)
                .map_err(|e| format!("Failed to create select all menu item: {}", e))?,
        )
        .build()
        .map_err(|e| format!("Failed to build edit menu: {}", e))?;

    // 帮助菜单
    let help_menu = SubmenuBuilder::new(app_handle, "Help")
        .item(
            &MenuItemBuilder::with_id("about", "About Mecap")
                .build(app_handle)
                .map_err(|e| format!("Failed to create about menu item: {}", e))?,
        )
        .build()
        .map_err(|e| format!("Failed to build help menu: {}", e))?;

    // 构建完整菜单
    let menu = MenuBuilder::new(app_handle)
        .items(&[&file_menu, &edit_menu, &help_menu])
        .build()
        .map_err(|e| format!("Failed to build main menu: {}", e))?;

    // 设置为应用菜单
    app_handle
        .set_menu(menu)
        .map_err(|e| format!("Failed to set app menu: {}", e))?;

    println!("[MENU] Desktop window menu created successfully");
    Ok("Desktop window menu created".to_string())
}

/// 处理菜单事件
pub fn setup_menu_event_handler(app_handle: &AppHandle) -> Result<(), String> {
    println!("[MENU] Setting up menu event handler");

    app_handle.on_menu_event(move |app_handle, event| {
        println!("[MENU] Menu event received: {:?}", event.id());

        match event.id().0.as_str() {
            "capture_screen" => {
                println!("[MENU] Capture screen triggered - starting unified capture workflow");
                // 触发统一的截图工作流，默认从窗口检测模式开始
                if let Err(e) = trigger_unified_capture_workflow(app_handle) {
                    eprintln!("[ERROR] Failed to trigger unified capture workflow: {}", e);
                }
            }
            "manage_screenshots" => {
                println!("[MENU] Manage screenshots triggered");
                // 打开截图管理器
                if let Err(e) = open_screenshot_manager(app_handle) {
                    eprintln!("[ERROR] Failed to open screenshot manager: {}", e);
                }
            }
            "preferences" => {
                println!("[MENU] Preferences triggered");
                // 打开偏好设置
                if let Err(e) = open_preferences(app_handle) {
                    eprintln!("[ERROR] Failed to open preferences: {}", e);
                }
            }
            "about" => {
                println!("[MENU] About triggered");
                // 显示关于对话框
                if let Err(e) = show_about_dialog(app_handle) {
                    eprintln!("[ERROR] Failed to show about dialog: {}", e);
                }
            }
            "exit_app" => {
                println!("[MENU] Exit app triggered");
                // 退出应用
                app_handle.exit(0);
            }
            _ => {
                println!("[MENU] Unhandled menu event: {}", event.id().0);
            }
        }
    });

    println!("[MENU] Menu event handler setup completed");
    Ok(())
}

/// 触发统一的截图工作流 - 默认从窗口检测模式开始，支持自动切换到区域选择模式
fn trigger_unified_capture_workflow(app_handle: &AppHandle) -> Result<(), String> {
    println!("[MENU] Starting unified capture workflow with window detection mode as default");

    // 隐藏主窗口
    if let Some(main_window) = app_handle.get_webview_window("main") {
        main_window
            .hide()
            .map_err(|e| format!("Failed to hide main window: {}", e))?;
    }

    // 开始统一的截图工作流
    let app_handle_clone = app_handle.clone();
    tauri::async_runtime::spawn(async move {
        match create_unified_capture_overlay(app_handle_clone.clone(), None).await {
            Ok(overlay_id) => {
                println!("[MENU] Unified capture overlay created: {}", overlay_id);
            }
            Err(e) => {
                eprintln!("[ERROR] Failed to create unified capture overlay: {}", e);
            }
        }
    });

    Ok(())
}

/// 创建统一的截图覆盖层 - 使用实时创建模式
async fn create_unified_capture_overlay(app_handle: AppHandle, editor_id: Option<String>) -> Result<String, String> {
    println!(
        "[CAPTURE] Creating capture overlay with real-time creation and automatic mode switching"
    );

    // 开始截图会话，默认为窗口检测模式，传递预创建的编辑器ID
    crate::modules::state::start_capture_session_with_editor(app_handle.clone(), "window".to_string(), None, editor_id)
        .await
        .map_err(|e| format!("Failed to start capture session: {}", e))?;

    // 实时创建窗口高亮覆盖层
    println!("[CAPTURE] 🚀 Creating window highlight overlay in real-time...");
    let overlay_id =
        crate::modules::overlay::create_window_highlight_overlay(app_handle.clone(), None)
            .await
            .map_err(|e| format!("Failed to create window highlight overlay: {}", e))?;

    println!(
        "[CAPTURE] ✅ Window highlight overlay created successfully: {}",
        overlay_id
    );
    println!("[CAPTURE] Window detection mode activated. Press SPACEBAR or start dragging to switch to region selection mode.");

    // 设置键盘和鼠标事件监听器来实现自动模式切换
    setup_mode_switching_listeners(app_handle, overlay_id.clone()).await?;

    Ok(overlay_id)
}

/// 设置模式切换监听器 - 监听空格键和鼠标拖拽事件
async fn setup_mode_switching_listeners(
    app_handle: AppHandle,
    current_overlay_id: String,
) -> Result<(), String> {
    println!("[CAPTURE] Setting up automatic mode switching listeners");

    // 设置UX交互模式为窗口选择模式，并启用自动切换
    crate::modules::ux::set_interaction_mode(
        app_handle.clone(),
        current_overlay_id.clone(),
        "window_with_auto_switch".to_string(),
    )
    .await
    .map_err(|e| format!("Failed to set interaction mode: {}", e))?;

    println!("[CAPTURE] Mode switching listeners configured. Window detection mode active with auto-switch capability.");
    Ok(())
}

/// 打开截图管理器
fn open_screenshot_manager(app_handle: &AppHandle) -> Result<(), String> {
    // 显示主窗口并切换到截图管理器页面
    if let Some(main_window) = app_handle.get_webview_window("main") {
        main_window
            .show()
            .map_err(|e| format!("Failed to show main window: {}", e))?;
        main_window
            .set_focus()
            .map_err(|e| format!("Failed to focus main window: {}", e))?;

        // 发送事件切换到截图管理器页面
        main_window
            .emit("navigate-to-manager", ())
            .map_err(|e| format!("Failed to emit navigation event: {}", e))?;
    }

    println!("[MENU] Screenshot manager opened");
    Ok(())
}

/// 显示主窗口 - 当覆盖层关闭时调用（使用screenshot.rs中的实现）

/// 打开偏好设置
fn open_preferences(app_handle: &AppHandle) -> Result<(), String> {
    // 显示主窗口并切换到偏好设置页面
    if let Some(main_window) = app_handle.get_webview_window("main") {
        main_window
            .show()
            .map_err(|e| format!("Failed to show main window: {}", e))?;
        main_window
            .set_focus()
            .map_err(|e| format!("Failed to focus main window: {}", e))?;

        // 发送事件切换到偏好设置页面
        main_window
            .emit("navigate-to-preferences", ())
            .map_err(|e| format!("Failed to emit navigation event: {}", e))?;
    }

    println!("[MENU] Preferences opened");
    Ok(())
}

/// 显示关于对话框
fn show_about_dialog(app_handle: &AppHandle) -> Result<(), String> {
    // 显示主窗口并显示关于对话框
    if let Some(main_window) = app_handle.get_webview_window("main") {
        main_window
            .show()
            .map_err(|e| format!("Failed to show main window: {}", e))?;
        main_window
            .set_focus()
            .map_err(|e| format!("Failed to focus main window: {}", e))?;

        // 发送事件显示关于对话框
        main_window
            .emit("show-about-dialog", ())
            .map_err(|e| format!("Failed to emit about dialog event: {}", e))?;
    }

    println!("[MENU] About dialog shown");
    Ok(())
}

/// 获取菜单配置
#[command]
pub async fn get_menu_config() -> Result<MenuConfig, String> {
    Ok(MenuConfig::default())
}

/// 更新菜单配置
#[command]
pub async fn update_menu_config(app_handle: AppHandle, config: MenuConfig) -> Result<(), String> {
    println!("[MENU] Updating menu configuration");

    // 重新创建菜单
    create_native_menu(app_handle, Some(config)).await?;

    println!("[MENU] Menu configuration updated");
    Ok(())
}
