// 混合截图模块 - 结合平台特定API和全局快捷键的高级截图功能
use anyhow::{anyhow, Result};
use log::{debug, info};
use serde::{Deserialize, Serialize};
use tauri::{command, AppHandle, Emitter, Manager};
use xcap::{Monitor, Window};

use super::coordinate::{CoordinateSystem, MonitorInfo, ScreenRegion};
use super::overlay::{create_modern_overlay, OverlayType};

/// 截图结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScreenshotResult {
    pub path: String,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub timestamp: u64,
    pub region: Option<ScreenRegion>,
}

/// 窗口信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowInfo {
    pub handle: u64,
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
    pub title: Option<String>,
    pub process_name: Option<String>,
}

/// 混合截图管理器
pub struct HybridScreenshotManager {
    coordinate_system: CoordinateSystem,
    active_overlay_id: Option<String>,
}

impl HybridScreenshotManager {
    pub fn new() -> Result<Self> {
        Ok(Self {
            coordinate_system: CoordinateSystem::new()?,
            active_overlay_id: None,
        })
    }

    /// 启动混合截图流程
    pub async fn start_hybrid_screenshot(&mut self, app_handle: AppHandle) -> Result<String> {
        // 刷新显示器信息
        self.coordinate_system.refresh()?;

        // 创建截图覆盖层
        let overlay_id = create_modern_overlay(app_handle.clone(), OverlayType::ScreenshotOverlay)
            .await
            .map_err(|e| anyhow!("Failed to create screenshot overlay: {}", e))?;

        self.active_overlay_id = Some(overlay_id.clone());

        // 发送显示器信息到前端
        let monitors = self.coordinate_system.get_monitors();
        app_handle
            .emit("monitors-info", monitors)
            .map_err(|e| anyhow!("Failed to emit monitors info: {}", e))?;

        println!(
            "[HYBRID_SCREENSHOT] Started hybrid screenshot with overlay: {}",
            overlay_id
        );
        Ok(overlay_id)
    }

    /// 从覆盖层捕获区域
    pub async fn capture_region_from_overlay(
        &self,
        region: ScreenRegion,
    ) -> Result<ScreenshotResult> {
        // 验证区域
        self.coordinate_system.validate_region(&region)?;

        // 转换坐标
        let (x, y, width, height, monitor_index) =
            self.coordinate_system.region_to_xcap_coordinates(&region)?;

        // 获取目标显示器
        let monitors = Monitor::all().map_err(|e| anyhow!("Failed to get monitors: {}", e))?;

        let target_monitor = monitors
            .get(monitor_index)
            .ok_or_else(|| anyhow!("Invalid monitor index: {}", monitor_index))?;

        // 🔧 LOGICAL DIMENSION FIX: 获取缩放因子
        let scale_factor = target_monitor.scale_factor().unwrap_or(1.0);

        // 执行截图 - 先捕获整个显示器，然后裁剪区域
        let full_image = target_monitor
            .capture_image()
            .map_err(|e| anyhow!("Failed to capture monitor: {}", e))?;

        // 🔧 LOGICAL DIMENSION FIX: 计算物理坐标用于裁剪
        let physical_x = (x as f32 * scale_factor) as u32;
        let physical_y = (y as f32 * scale_factor) as u32;
        let physical_width = (width as f32 * scale_factor) as u32;
        let physical_height = (height as f32 * scale_factor) as u32;

        // 裁剪到指定区域（使用物理坐标）
        let cropped_physical = image::imageops::crop_imm(
            &full_image,
            physical_x,
            physical_y,
            physical_width,
            physical_height
        ).to_image();

        // 🔧 LOGICAL DIMENSION FIX: 缩放回逻辑尺寸
        let final_image = if scale_factor > 1.0 {
            info!(
                "[HYBRID_SCREENSHOT] Scaling cropped region to logical dimensions: {}x{} → {}x{}",
                cropped_physical.width(), cropped_physical.height(),
                width, height
            );

            image::imageops::resize(
                &cropped_physical,
                width,
                height,
                image::imageops::FilterType::Lanczos3
            )
        } else {
            cropped_physical
        };

        // 生成保存路径
        let timestamp = chrono::Utc::now().timestamp_millis() as u64;
        let filename = format!("screenshot_{}.png", timestamp);
        let save_path = self.generate_save_path(&filename)?;

        // 保存图像
        final_image
            .save(&save_path)
            .map_err(|e| anyhow!("Failed to save image: {}", e))?;

        Ok(ScreenshotResult {
            path: save_path,
            width: Some(final_image.width()),
            height: Some(final_image.height()),
            timestamp,
            region: Some(region),
        })
    }

    /// 生成保存路径
    fn generate_save_path(&self, filename: &str) -> Result<String> {
        let pictures_dir =
            dirs::picture_dir().ok_or_else(|| anyhow!("Could not find pictures directory"))?;

        let mecap_dir = pictures_dir.join("Mecap");
        std::fs::create_dir_all(&mecap_dir)
            .map_err(|e| anyhow!("Failed to create Mecap directory: {}", e))?;

        Ok(mecap_dir.join(filename).to_string_lossy().to_string())
    }

    /// 关闭活动的覆盖层
    pub async fn close_active_overlay(&mut self, app_handle: AppHandle) -> Result<()> {
        if let Some(overlay_id) = &self.active_overlay_id {
            if let Some(window) = app_handle.get_webview_window(overlay_id) {
                window
                    .close()
                    .map_err(|e| anyhow!("Failed to close overlay: {}", e))?;
            }
            self.active_overlay_id = None;
        }
        Ok(())
    }
}

// 全局管理器实例
lazy_static::lazy_static! {
    static ref HYBRID_MANAGER: std::sync::Mutex<Option<HybridScreenshotManager>> =
        std::sync::Mutex::new(None);
}

/// 初始化混合截图管理器
fn ensure_manager() -> Result<()> {
    let mut manager = HYBRID_MANAGER
        .lock()
        .map_err(|e| anyhow!("Failed to lock manager: {}", e))?;

    if manager.is_none() {
        *manager = Some(HybridScreenshotManager::new()?);
    }
    Ok(())
}

// Tauri命令接口

/// 启动混合截图
#[command]
pub async fn start_hybrid_screenshot(app_handle: AppHandle) -> Result<String, String> {
    ensure_manager().map_err(|e| e.to_string())?;

    // 创建临时管理器实例来避免跨await的锁持有
    let mut temp_manager = {
        let mut manager = HYBRID_MANAGER
            .lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;

        if let Some(mgr) = manager.take() {
            mgr
        } else {
            return Err("Manager not initialized".to_string());
        }
    };

    // 执行操作
    let result = temp_manager
        .start_hybrid_screenshot(app_handle)
        .await
        .map_err(|e| e.to_string());

    // 将管理器放回
    {
        let mut manager = HYBRID_MANAGER
            .lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;
        *manager = Some(temp_manager);
    }

    result
}

/// 从覆盖层捕获区域
#[command]
pub async fn trigger_region_capture_from_toolbar(
    app_handle: AppHandle,
    selected_tool: String,
) -> Result<(), String> {
    log::info!("[HYBRID_SCREENSHOT] 🛠️ Toolbar triggered region capture with tool: {}", selected_tool);

    // 🔧 ENHANCED: 尝试找到活动的overlay窗口并发送事件
    let overlay_windows: Vec<String> = app_handle.webview_windows()
        .iter()
        .filter_map(|(label, _)| {
            if label.starts_with("window_highlight_") {
                Some(label.clone())
            } else {
                None
            }
        })
        .collect();

    if overlay_windows.is_empty() {
        log::warn!("[HYBRID_SCREENSHOT] ⚠️ No overlay windows found, sending global event");
        // 发送全局事件作为备用
        app_handle.emit("toolbar-trigger-capture", serde_json::json!({
            "selectedTool": selected_tool
        })).map_err(|e| format!("Failed to emit global toolbar capture event: {}", e))?;
    } else {
        // 🔧 CRITICAL FIX: 只发送到第一个overlay窗口，避免重复处理
        let first_overlay = &overlay_windows[0];
        log::info!("[HYBRID_SCREENSHOT] 🎯 Sending event to primary overlay window: {}", first_overlay);

        app_handle.emit_to(first_overlay, "toolbar-trigger-capture", serde_json::json!({
            "selectedTool": selected_tool
        })).map_err(|e| format!("Failed to emit toolbar capture event to overlay {}: {}", first_overlay, e))?;

        // 🔧 CRITICAL FIX: 不再发送全局事件，避免重复处理
        log::info!("[HYBRID_SCREENSHOT] 🎯 Event sent to primary overlay only, skipping global event to prevent duplicates");
    }

    log::info!("[HYBRID_SCREENSHOT] 🛠️ Capture trigger event sent to {} overlay windows", overlay_windows.len());
    Ok(())
}

/// 🆕 从工具栏触发区域捕获并进入注解模式
#[command]
pub async fn capture_region_with_tool_selection(
    app_handle: AppHandle,
    region: ScreenRegion,
    selected_tool: String,
) -> Result<ScreenshotResult, String> {
    log::info!("[HYBRID_SCREENSHOT] 🎨 Capturing region with tool selection: {}", selected_tool);

    // 首先执行正常的区域捕获
    let result = capture_region_from_overlay(app_handle.clone(), region).await?;

    // 如果捕获成功（函数返回Ok），发送工具选择事件到预览窗口
    log::info!("[HYBRID_SCREENSHOT] 🎨 Region capture successful, sending tool selection to preview window: {}", selected_tool);

    // 发送工具选择事件到预览窗口
    if let Err(e) = app_handle.emit("preview-tool-selection", serde_json::json!({
        "selectedTool": selected_tool,
        "timestamp": chrono::Utc::now().timestamp_millis()
    })) {
        log::warn!("[HYBRID_SCREENSHOT] ⚠️ Failed to send tool selection event: {}", e);
    } else {
        log::info!("[HYBRID_SCREENSHOT] ✅ Tool selection event sent to preview window");
    }

    Ok(result)
}

#[command]
pub async fn capture_region_from_overlay(app_handle: AppHandle, region: ScreenRegion) -> Result<ScreenshotResult, String> {
    ensure_manager().map_err(|e| e.to_string())?;

    // 创建临时管理器实例
    let temp_manager = {
        let manager = HYBRID_MANAGER
            .lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;

        if let Some(ref mgr) = manager.as_ref() {
            // 克隆必要的数据而不是整个管理器
            mgr.coordinate_system.clone()
        } else {
            return Err("Manager not initialized".to_string());
        }
    };

    // 使用临时管理器执行截图
    let temp_mgr = HybridScreenshotManager {
        coordinate_system: temp_manager,
        active_overlay_id: None,
    };

    let result = temp_mgr
        .capture_region_from_overlay(region)
        .await
        .map_err(|e| e.to_string())?;

    // 🔧 CRITICAL FIX: 先关闭所有覆盖层，确保overlay工具栏被清理
    log::info!("[HYBRID_SCREENSHOT] 🧹 Closing all overlay windows before creating preview");
    if let Err(e) = crate::modules::overlay::close_all_overlays(app_handle.clone()).await {
        log::warn!("[HYBRID_SCREENSHOT] ⚠️ Failed to close overlay windows: {}", e);
    } else {
        log::info!("[HYBRID_SCREENSHOT] ✅ All overlay windows closed successfully");
    }

    // 🔧 CRITICAL FIX: 停止窗口检测系统，避免干扰
    log::info!("[HYBRID_SCREENSHOT] 🛑 Stopping window detection system");
    if let Err(e) = crate::modules::window::stop_smart_window_detection().await {
        log::warn!("[HYBRID_SCREENSHOT] ⚠️ Failed to stop window detection: {}", e);
    } else {
        log::info!("[HYBRID_SCREENSHOT] ✅ Window detection stopped successfully");
    }

    // 🔧 CRITICAL FIX: 添加短暂延迟，确保覆盖层完全关闭
    tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

    // 🆕 创建截图预览窗口，实现与窗口截图相同的标注功能
    log::info!("[HYBRID_SCREENSHOT] 🖼️ 创建区域截图预览窗口，启用标注功能");

    match crate::modules::overlay::create_screenshot_preview_window(
        app_handle.clone(),
        result.path.clone(),
        result.width.unwrap_or(800),
        result.height.unwrap_or(600),
        None, // 区域截图没有固定位置，让系统自动居中
        None,
    ).await {
        Ok(preview_id) => {
            log::info!("[HYBRID_SCREENSHOT] ✅ 区域截图预览窗口创建成功: {}", preview_id);
        }
        Err(e) => {
            log::warn!("[HYBRID_SCREENSHOT] ⚠️ 区域截图预览窗口创建失败: {}", e);
            // 预览窗口创建失败不影响截图保存操作的成功
        }
    }

    Ok(result)
}

/// 关闭混合截图覆盖层
#[command]
pub async fn close_hybrid_screenshot(app_handle: AppHandle) -> Result<(), String> {
    ensure_manager().map_err(|e| e.to_string())?;

    // 创建临时管理器实例
    let mut temp_manager = {
        let mut manager = HYBRID_MANAGER
            .lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;

        if let Some(mgr) = manager.take() {
            mgr
        } else {
            return Err("Manager not initialized".to_string());
        }
    };

    // 执行操作
    let result = temp_manager
        .close_active_overlay(app_handle)
        .await
        .map_err(|e| e.to_string());

    // 将管理器放回
    {
        let mut manager = HYBRID_MANAGER
            .lock()
            .map_err(|e| format!("Failed to lock manager: {}", e))?;
        *manager = Some(temp_manager);
    }

    result
}

/// 获取当前显示器信息
#[command]
pub async fn get_current_monitors() -> Result<Vec<MonitorInfo>, String> {
    // 直接创建新的坐标系统实例，避免锁的问题
    let mut coord_system = CoordinateSystem::new()
        .map_err(|e| format!("Failed to create coordinate system: {}", e))?;

    coord_system
        .refresh()
        .map_err(|e| format!("Failed to refresh monitors: {}", e))?;

    Ok(coord_system.get_monitors().to_vec())
}

// Windows平台特定的窗口检测实现
#[cfg(target_os = "windows")]
mod windows_detection {
    use super::*;
    use windows::Win32::Foundation::{BOOL, HWND, POINT, RECT};
    use windows::Win32::System::ProcessStatus::GetModuleBaseNameW;
    use windows::Win32::System::Threading::{
        GetCurrentProcessId, OpenProcess, PROCESS_QUERY_INFORMATION,
    };
    use windows::Win32::UI::WindowsAndMessaging::GW_OWNER;
    use windows::Win32::UI::WindowsAndMessaging::{
        GetWindow, GetWindowRect, GetWindowTextW, GetWindowThreadProcessId, IsWindowVisible,
        WindowFromPoint,
    };

    /// Windows平台高级窗口检测
    /// 🔧 ISSUE 3 FIX: Enhanced to properly detect topmost visible window
    pub fn get_window_under_cursor(x: i32, y: i32) -> Option<WindowInfo> {
        trace!(
            "[WIN_DETECT] Starting Windows window detection at ({}, {})",
            x,
            y
        );

        unsafe {
            let point = POINT { x, y };

            // 🔧 ISSUE 3 FIX: Use WindowFromPoint to get the actual topmost window
            let hwnd = WindowFromPoint(point);

            if hwnd.0 == 0 {
                debug!("[WIN_DETECT] WindowFromPoint returned null handle");
                return None;
            }

            debug!("[WIN_DETECT] Found topmost window handle: 0x{:X}", hwnd.0);

            // 检查窗口是否可见
            if IsWindowVisible(hwnd) == BOOL(0) {
                debug!("[WIN_DETECT] Window is not visible, skipping");
                return None;
            }

            trace!("[WIN_DETECT] Window is visible, proceeding with detection");

            // 🔧 ISSUE 3 FIX: Get the actual topmost window that should be highlighted
            // WindowFromPoint already returns the topmost window at the point
            let target_hwnd = get_top_level_window(hwnd);
            if target_hwnd.0 != hwnd.0 {
                debug!(
                    "[WIN_DETECT] Using top-level window: 0x{:X} (child was 0x{:X})",
                    target_hwnd.0, hwnd.0
                );
            } else {
                debug!("[WIN_DETECT] Using direct window: 0x{:X}", hwnd.0);
            }

            // 获取窗口矩形
            let mut rect = RECT::default();
            if GetWindowRect(top_level_hwnd, &mut rect).is_err() {
                warn!(
                    "[WIN_DETECT] Failed to get window rectangle for handle 0x{:X}",
                    top_level_hwnd.0
                );
                return None;
            }

            debug!(
                "[WIN_DETECT] Window rect: ({}, {}) {}x{}",
                rect.left,
                rect.top,
                rect.right - rect.left,
                rect.bottom - rect.top
            );

            // 获取窗口标题
            let title = get_window_title(top_level_hwnd);
            debug!(
                "[WIN_DETECT] Window title: {:?}",
                if title.is_empty() { None } else { Some(&title) }
            );

            // 获取进程名
            let process_name = get_process_name(top_level_hwnd);
            debug!("[WIN_DETECT] Process name: {:?}", process_name);

            let window_info = WindowInfo {
                handle: top_level_hwnd.0 as u64,
                x: rect.left,
                y: rect.top,
                width: (rect.right - rect.left) as u32,
                height: (rect.bottom - rect.top) as u32,
                title: if title.is_empty() { None } else { Some(title) },
                process_name,
            };

            info!("[WIN_DETECT] Successfully detected window: handle=0x{:X}, size={}x{}, title={:?}, process={:?}",
                window_info.handle, window_info.width, window_info.height, window_info.title, window_info.process_name);

            Some(window_info)
        }
    }

    /// 获取顶层窗口
    unsafe fn get_top_level_window(mut hwnd: HWND) -> HWND {
        // 向上遍历找到顶层窗口
        loop {
            let owner = GetWindow(hwnd, GW_OWNER);
            if owner.0 == 0 {
                break;
            }
            hwnd = owner;
        }
        hwnd
    }

    /// 获取窗口标题
    unsafe fn get_window_title(hwnd: HWND) -> String {
        trace!("[WIN_TITLE] Getting window title for handle 0x{:X}", hwnd.0);

        let mut title_buffer = [0u16; 512];
        let title_len = GetWindowTextW(hwnd, &mut title_buffer);
        if title_len > 0 {
            let title = String::from_utf16_lossy(&title_buffer[..title_len as usize]);
            debug!("[WIN_TITLE] Retrieved title: '{}'", title);
            title
        } else {
            debug!("[WIN_TITLE] No title found for window 0x{:X}", hwnd.0);
            String::new()
        }
    }

    /// 获取进程名
    unsafe fn get_process_name(hwnd: HWND) -> Option<String> {
        trace!(
            "[WIN_PROCESS] Getting process name for window 0x{:X}",
            hwnd.0
        );

        let mut process_id = 0u32;
        GetWindowThreadProcessId(hwnd, Some(&mut process_id));

        if process_id == 0 {
            debug!(
                "[WIN_PROCESS] Failed to get process ID for window 0x{:X}",
                hwnd.0
            );
            return None;
        }

        debug!("[WIN_PROCESS] Process ID: {}", process_id);

        // 打开进程句柄
        let process_handle = OpenProcess(PROCESS_QUERY_INFORMATION, BOOL(0), process_id);
        if process_handle.is_err() {
            warn!(
                "[WIN_PROCESS] Failed to open process handle for PID {}",
                process_id
            );
            return None;
        }

        let process_handle = process_handle.unwrap();

        // 获取进程模块名
        let mut module_name = [0u16; 256];
        let name_len = GetModuleBaseNameW(process_handle, None, &mut module_name);

        let _ = windows::Win32::Foundation::CloseHandle(process_handle);

        if name_len > 0 {
            let process_name = String::from_utf16_lossy(&module_name[..name_len as usize]);
            debug!("[WIN_PROCESS] Retrieved process name: '{}'", process_name);
            Some(process_name)
        } else {
            warn!(
                "[WIN_PROCESS] Failed to get module name for PID {}",
                process_id
            );
            None
        }
    }

    /// 检查是否为系统窗口或特殊窗口
    pub fn is_system_window(window_info: &WindowInfo) -> bool {
        trace!(
            "[WIN_SYSTEM_CHECK] Checking if window is system window: handle=0x{:X}",
            window_info.handle
        );

        if let Some(ref process_name) = window_info.process_name {
            debug!("[WIN_SYSTEM_CHECK] Process name: {}", process_name);

            let system_processes = [
                "dwm.exe",      // Desktop Window Manager
                "winlogon.exe", // Windows Logon
                "csrss.exe",    // Client Server Runtime Process
                "explorer.exe", // Windows Explorer (部分情况)
                "svchost.exe",  // Service Host
                "lsass.exe",    // Local Security Authority
                "wininit.exe",  // Windows Initialization
                "services.exe", // Service Control Manager
            ];

            let process_lower = process_name.to_lowercase();
            for &sys_proc in &system_processes {
                if process_lower.contains(&sys_proc.to_lowercase()) {
                    info!(
                        "[WIN_SYSTEM_CHECK] Identified as system window: process={}, matched={}",
                        process_name, sys_proc
                    );
                    return true;
                }
            }

            // 额外检查：无标题的系统窗口
            if window_info.title.is_none() || window_info.title.as_ref().unwrap().trim().is_empty()
            {
                debug!("[WIN_SYSTEM_CHECK] Window has no title, might be system window");
                // 对于某些已知的系统进程，即使没有明确匹配也认为是系统窗口
                if process_lower.contains("system") || process_lower.contains("registry") {
                    info!("[WIN_SYSTEM_CHECK] Identified as system window due to process name pattern: {}", process_name);
                    return true;
                }
            }

            debug!("[WIN_SYSTEM_CHECK] Not a system window: {}", process_name);
            false
        } else {
            warn!("[WIN_SYSTEM_CHECK] No process name available, assuming system window");
            true // 没有进程名的窗口通常是系统窗口
        }
    }

    /// 获取窗口详细信息
    pub fn get_enhanced_window_info(x: i32, y: i32) -> Option<EnhancedWindowInfo> {
        trace!(
            "[WIN_ENHANCED] Starting enhanced window detection at ({}, {})",
            x,
            y
        );

        let basic_info = match get_window_under_cursor(x, y) {
            Some(info) => {
                debug!("[WIN_ENHANCED] Basic window info obtained");
                info
            }
            None => {
                debug!("[WIN_ENHANCED] No basic window info found");
                return None;
            }
        };

        let hwnd = HWND(basic_info.handle as isize);

        // 检查是否为系统窗口
        let is_system = is_system_window(&basic_info);
        debug!("[WIN_ENHANCED] System window check: {}", is_system);

        // 检查是否最大化
        let is_maximized = is_window_maximized(hwnd);
        debug!("[WIN_ENHANCED] Maximized check: {}", is_maximized);

        // 获取Z序
        let z_order = get_window_z_order(hwnd);
        debug!("[WIN_ENHANCED] Z-order: {}", z_order);

        let enhanced_info = EnhancedWindowInfo {
            basic: basic_info.clone(),
            is_system,
            is_maximized,
            z_order,
        };

        info!("[WIN_ENHANCED] Enhanced window info complete: handle=0x{:X}, is_system={}, is_maximized={}, z_order={}",
            basic_info.handle, is_system, is_maximized, z_order);

        Some(enhanced_info)
    }

    /// 检查窗口是否最大化
    unsafe fn is_window_maximized(hwnd: HWND) -> bool {
        use windows::Win32::UI::WindowsAndMessaging::{
            GetWindowPlacement, SW_SHOWMAXIMIZED, WINDOWPLACEMENT,
        };

        trace!(
            "[WIN_MAXIMIZED] Checking if window 0x{:X} is maximized",
            hwnd.0
        );

        let mut placement = WINDOWPLACEMENT::default();
        placement.length = std::mem::size_of::<WINDOWPLACEMENT>() as u32;

        if GetWindowPlacement(hwnd, &mut placement).is_ok() {
            let is_maximized = placement.showCmd == SW_SHOWMAXIMIZED.0 as u32;
            debug!(
                "[WIN_MAXIMIZED] Window 0x{:X} maximized status: {}",
                hwnd.0, is_maximized
            );
            is_maximized
        } else {
            warn!(
                "[WIN_MAXIMIZED] Failed to get window placement for 0x{:X}",
                hwnd.0
            );
            false
        }
    }

    /// 获取窗口Z序
    unsafe fn get_window_z_order(hwnd: HWND) -> i32 {
        use windows::Win32::UI::WindowsAndMessaging::{GetWindow, GW_HWNDPREV};

        trace!("[WIN_ZORDER] Calculating Z-order for window 0x{:X}", hwnd.0);

        let mut z_order = 0;
        let mut current = hwnd;

        // 向前遍历计算Z序
        while current.0 != 0 {
            current = GetWindow(current, GW_HWNDPREV);
            if current.0 != 0 {
                z_order += 1;
            }
        }

        debug!("[WIN_ZORDER] Window 0x{:X} Z-order: {}", hwnd.0, z_order);
        z_order
    }

    /// 增强的窗口信息
    #[derive(Debug, Clone)]
    pub struct EnhancedWindowInfo {
        pub basic: WindowInfo,
        pub is_system: bool,
        pub is_maximized: bool,
        pub z_order: i32,
    }
}

// macOS平台特定的窗口检测实现
#[cfg(target_os = "macos")]
mod macos_detection {
    use super::*;
    use core_foundation::{
        array::{CFArray, CFArrayRef},
        base::TCFType,
        dictionary::{CFDictionary, CFDictionaryRef},
        number::{CFNumber, CFNumberRef},
        string::{CFString, CFStringRef},
    };
    use core_graphics::window::{
        kCGNullWindowID, kCGWindowListOptionOnScreenOnly, CGWindowListCopyWindowInfo,
    };
    use log::{debug, error, info, warn};

    /// macOS平台窗口检测实现
    pub fn get_window_under_cursor(x: f64, y: f64) -> Option<WindowInfo> {
        let start_time = std::time::Instant::now();
        log::trace!(
            "[MACOS] Starting window detection at coordinates ({}, {})",
            x,
            y
        );

        // 检查屏幕录制权限
        if !check_screen_recording_permission() {
            warn!("[MACOS] Screen recording permission not granted");
            return None;
        }

        // 获取窗口列表（优化：在一次检测中复用窗口列表）
        let window_list = match get_window_list() {
            Some(list) => list,
            None => {
                error!("[MACOS] Failed to get window list");
                return None;
            }
        };

        log::debug!(
            "[MACOS] Retrieved {} windows from system",
            window_list.len()
        );

        // 查找包含指定坐标的窗口（按Z-order排序，最上层优先）
        let mut candidates = Vec::new();

        for (i, window_info) in window_list.iter().enumerate() {
            if point_in_window(x, y, window_info) {
                log::debug!("[MACOS] Window {} contains point ({}, {}): {:?} - {:?} ({}x{} at {},{}) layer={}",
                    i, x, y, window_info.process_name, window_info.title,
                    window_info.width, window_info.height, window_info.x, window_info.y, window_info.handle);

                // 🔧 Z-ORDER FIX: 检查窗口在该点是否真正可见（未被遮挡）
                if is_window_visible_at_point(window_info, x, y, &window_list) {
                    candidates.push(window_info.clone());
                } else {
                    log::debug!(
                        "[MACOS] Window '{}' contains point but is obscured by higher layer windows",
                        window_info.title.as_ref().unwrap_or(&"Untitled".to_string())
                    );
                }
            }
        }

        let detection_time = start_time.elapsed();

        if candidates.is_empty() {
            log::debug!(
                "[MACOS] No window found at coordinates ({}, {}) in {:.2}ms",
                x,
                y,
                detection_time.as_millis()
            );
            None
        } else {
            // 🔧 Z-ORDER DEBUG: 记录所有候选窗口的层级信息
            log::trace!(
                "[MACOS] Found {} candidate windows at ({}, {}), Z-order analysis:",
                candidates.len(),
                x,
                y
            );
            for (i, candidate) in candidates.iter().enumerate() {
                log::trace!(
                    "[MACOS]   {}. {} ({}) - layer={} ({})",
                    i + 1,
                    candidate.title.as_ref().unwrap_or(&"Untitled".to_string()),
                    candidate.process_name.as_ref().unwrap_or(&"Unknown".to_string()),
                    candidate.handle,
                    if i == 0 { "TOPMOST" } else { "below" }
                );
            }

            // 使用修复后的窗口选择算法，严格按照Z-order选择
            if let Some(selected_window) = select_best_window_with_focus(&candidates, &window_list)
            {
                log::trace!(
                    "[MACOS] ✅ Selected window at ({}, {}): {} ({}) layer={} in {:.2}ms",
                    x,
                    y,
                    selected_window
                        .title
                        .as_ref()
                        .unwrap_or(&"Untitled".to_string()),
                    selected_window
                        .process_name
                        .as_ref()
                        .unwrap_or(&"Unknown".to_string()),
                    selected_window.handle,
                    detection_time.as_millis()
                );
                Some(selected_window)
            } else {
                log::warn!(
                    "[MACOS] ❌ No suitable window found at coordinates ({}, {}) in {:.2}ms",
                    x,
                    y,
                    detection_time.as_millis()
                );
                None
            }
        }
    }

    /// 检查屏幕录制权限
    pub fn check_screen_recording_permission() -> bool {
        unsafe {
            // 尝试获取窗口列表来检查权限
            let window_list_info =
                CGWindowListCopyWindowInfo(kCGWindowListOptionOnScreenOnly, kCGNullWindowID);

            if window_list_info.is_null() {
                info!("[MACOS] Screen recording permission check failed - window list is null");
                return false;
            }

            let window_array =
                CFArray::<*const core_foundation::base::CFTypeRef>::wrap_under_get_rule(
                    window_list_info as CFArrayRef,
                );
            let window_count = window_array.len();

            // 如果能获取到窗口列表且数量合理，说明有权限
            if window_count > 0 {
                log::trace!(
                    "[MACOS] Screen recording permission check passed - found {} windows",
                    window_count
                );
                true
            } else {
                info!("[MACOS] Screen recording permission check failed - no windows found");
                false
            }
        }
    }

    /// 请求屏幕录制权限（引导用户到系统设置）
    pub fn request_screen_recording_permission() -> Result<(), String> {
        info!("[MACOS] Requesting screen recording permission");

        // 在macOS中，我们不能直接请求权限，只能引导用户到系统设置
        // 这里可以显示一个对话框或通知，告诉用户如何授权

        // 尝试打开系统偏好设置的隐私页面
        use std::process::Command;

        let result = Command::new("open")
            .arg("x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture")
            .output();

        match result {
            Ok(_) => {
                info!("[MACOS] Successfully opened System Preferences for screen recording permission");
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("Failed to open System Preferences: {}", e);
                info!("[MACOS] {}", error_msg);
                Err(error_msg)
            }
        }
    }

    /// 获取系统窗口列表
    fn get_window_list() -> Option<Vec<WindowInfo>> {
        unsafe {
            log::trace!("[MACOS] Starting to get window list from system");

            // 获取屏幕上所有可见窗口的信息
            let window_list_info =
                CGWindowListCopyWindowInfo(kCGWindowListOptionOnScreenOnly, kCGNullWindowID);

            if window_list_info.is_null() {
                error!("[MACOS] Failed to get window list from system - CGWindowListCopyWindowInfo returned null");
                return None;
            }

            let window_array =
                CFArray::<CFDictionary>::wrap_under_get_rule(window_list_info as CFArrayRef);
            let total_windows = window_array.len();
            log::trace!(
                "[MACOS] Retrieved {} total windows from system",
                total_windows
            );

            let mut windows = Vec::new();
            let mut filtered_count = 0;

            for i in 0..window_array.len() {
                if let Some(window_dict) = window_array.get(i) {
                    log::trace!("[MACOS] Processing window {}/{}", i + 1, total_windows);

                    if let Some(window_info) = parse_window_info(&window_dict) {
                        log::trace!(
                            "[MACOS] Parsed window: {:?} ({}x{} at {},{}) layer={}",
                            window_info.process_name,
                            window_info.width,
                            window_info.height,
                            window_info.x,
                            window_info.y,
                            window_info.handle
                        );

                        // 过滤掉系统窗口和无效窗口
                        if is_valid_user_window(&window_info) {
                            windows.push(window_info);
                        } else {
                            filtered_count += 1;
                        }
                    } else {
                        debug!("[MACOS] Failed to parse window {}", i + 1);
                    }
                }
            }

            log::trace!(
                "[MACOS] Filtered out {} windows, keeping {} valid windows",
                filtered_count,
                windows.len()
            );

            // 按Z-order排序（layer值越大越在上层）
            windows.sort_by(|a, b| {
                let layer_a = a.handle; // 使用handle存储layer信息
                let layer_b = b.handle;
                layer_b.cmp(&layer_a) // 降序排列，最上层在前
            });

            log::trace!(
                "[MACOS] Final window list has {} windows, sorted by Z-order",
                windows.len()
            );
            // 只在TRACE级别显示完整窗口列表，减少日志噪音
            for (i, window) in windows.iter().enumerate() {
                log::trace!(
                    "[MACOS] Window {}: {:?} - {:?} (layer={})",
                    i,
                    window.process_name,
                    window.title,
                    window.handle
                );
            }

            Some(windows)
        }
    }

    /// 解析窗口信息字典
    fn parse_window_info(window_dict: &CFDictionary) -> Option<WindowInfo> {
        unsafe {
            // 获取窗口ID
            let _window_number = get_dict_number(window_dict, "kCGWindowNumber")?;

            // 获取窗口边界
            let bounds_dict = get_dict_dict(window_dict, "kCGWindowBounds")?;
            let x = get_dict_number(&bounds_dict, "X")? as i32;
            let y = get_dict_number(&bounds_dict, "Y")? as i32;
            let width = get_dict_number(&bounds_dict, "Width")? as u32;
            let height = get_dict_number(&bounds_dict, "Height")? as u32;

            // 获取窗口标题
            let title = get_dict_string(window_dict, "kCGWindowName");

            // 获取应用名称
            let process_name = get_dict_string(window_dict, "kCGWindowOwnerName");

            // 获取进程ID
            let _process_id = get_dict_number(window_dict, "kCGWindowOwnerPID").unwrap_or(0.0);

            // 获取窗口层级
            let layer = get_dict_number(window_dict, "kCGWindowLayer").unwrap_or(0.0);

            Some(WindowInfo {
                handle: layer as u64, // 使用layer作为handle
                x,
                y,
                width,
                height,
                title,
                process_name,
            })
        }
    }

    /// 辅助函数：从字典中获取数字值
    unsafe fn get_dict_number(dict: &CFDictionary, key: &str) -> Option<f64> {
        let cf_key = CFString::new(key);
        if let Some(cf_value) = dict.find(cf_key.as_concrete_TypeRef() as *const _) {
            let cf_number = CFNumber::wrap_under_get_rule(*cf_value as CFNumberRef);
            cf_number.to_f64()
        } else {
            None
        }
    }

    /// 辅助函数：从字典中获取字符串值
    unsafe fn get_dict_string(dict: &CFDictionary, key: &str) -> Option<String> {
        let cf_key = CFString::new(key);
        if let Some(cf_value) = dict.find(cf_key.as_concrete_TypeRef() as *const _) {
            let cf_string = CFString::wrap_under_get_rule(*cf_value as CFStringRef);
            Some(cf_string.to_string())
        } else {
            None
        }
    }

    /// 辅助函数：从字典中获取字典值
    unsafe fn get_dict_dict(dict: &CFDictionary, key: &str) -> Option<CFDictionary> {
        let cf_key = CFString::new(key);
        if let Some(cf_value) = dict.find(cf_key.as_concrete_TypeRef() as *const _) {
            Some(CFDictionary::wrap_under_get_rule(
                *cf_value as CFDictionaryRef,
            ))
        } else {
            None
        }
    }

    /// 检查坐标点是否在窗口内
    fn point_in_window(x: f64, y: f64, window: &WindowInfo) -> bool {
        x >= window.x as f64
            && x <= (window.x + window.width as i32) as f64
            && y >= window.y as f64
            && y <= (window.y + window.height as i32) as f64
    }

    /// 检查窗口在指定坐标点是否真正可见（未被其他窗口完全遮挡）
    fn is_window_visible_at_point(
        target_window: &WindowInfo,
        x: f64,
        y: f64,
        all_windows: &[WindowInfo],
    ) -> bool {
        // 检查是否有更高层级的窗口完全遮挡了目标窗口在该点的可见性
        for window in all_windows {
            // 跳过目标窗口本身
            if window.handle == target_window.handle {
                continue;
            }

            // 只检查层级更高的窗口（layer值更大）
            if window.handle > target_window.handle {
                // 如果更高层级的窗口包含该点，则目标窗口在该点被遮挡
                if point_in_window(x, y, window) {
                    log::trace!(
                        "[MACOS_VISIBILITY] Window '{}' (layer={}) is obscured by '{}' (layer={}) at point ({}, {})",
                        target_window.title.as_ref().unwrap_or(&"Untitled".to_string()),
                        target_window.handle,
                        window.title.as_ref().unwrap_or(&"Untitled".to_string()),
                        window.handle,
                        x,
                        y
                    );
                    return false;
                }
            }
        }

        log::trace!(
            "[MACOS_VISIBILITY] Window '{}' (layer={}) is visible at point ({}, {})",
            target_window.title.as_ref().unwrap_or(&"Untitled".to_string()),
            target_window.handle,
            x,
            y
        );
        true
    }

    /// 检查是否为有效的用户窗口
    fn is_valid_user_window(window: &WindowInfo) -> bool {
        // 只在DEBUG级别记录详细过滤信息，减少日志噪音
        log::trace!(
            "[MACOS_FILTER] Checking window: title={:?}, process={:?}, size={}x{}",
            window.title,
            window.process_name,
            window.width,
            window.height
        );

        // 使用静态变量跟踪已过滤的Mecap窗口，避免重复日志
        use std::sync::Mutex;
        static MECAP_FILTER_LOGGED: Mutex<bool> = Mutex::new(false);

        // 过滤掉无效尺寸的窗口
        if window.width < 10 || window.height < 10 {
            log::trace!("[MACOS_FILTER] Filtered out: window too small");
            return false;
        }

        // 过滤掉系统窗口和Mecap自身的窗口
        if let Some(ref process_name) = window.process_name {
            let system_processes = [
                "Window Server",
                "Dock",
                "SystemUIServer",
                "ControlCenter",
                "NotificationCenter",
                "Spotlight",
                "loginwindow",
            ];

            // 检查系统进程
            for &sys_proc in &system_processes {
                if process_name.contains(sys_proc) {
                    log::trace!("[MACOS_FILTER] Filtered out: system process {}", sys_proc);
                    return false;
                }
            }

            // 特别过滤掉Mecap自身的窗口（不区分大小写）
            let process_lower = process_name.to_lowercase();
            if process_lower.contains("mecap") {
                // 使用智能日志记录，避免重复的Mecap过滤日志
                let mut logged = MECAP_FILTER_LOGGED.lock().unwrap();
                if !*logged {
                    log::debug!(
                        "[MACOS_FILTER] 🚫 Filtering out Mecap overlay windows (process={})",
                        process_name
                    );
                    *logged = true;
                }
                return false;
            }
        }

        // 额外检查窗口标题，过滤掉Mecap的覆盖层窗口
        if let Some(ref title) = window.title {
            let title_lower = title.to_lowercase();
            if title_lower.contains("mecap")
                && (title_lower.contains("highlight") || title_lower.contains("overlay"))
            {
                log::trace!(
                    "[MACOS_FILTER] Filtered out: Mecap overlay window (title={})",
                    title
                );
                return false;
            }
        }

        // 检查窗口层级 - 过滤掉太低层级的窗口
        let layer = window.handle as i32; // handle存储的是layer
        if layer < 0 {
            log::trace!("[MACOS_FILTER] Filtered out: negative layer {}", layer);
            return false;
        }

        log::trace!(
            "[MACOS_FILTER] ✅ Window passed all filters: process={:?}, title={:?}, size={}x{}",
            window.process_name,
            window.title,
            window.width,
            window.height
        );
        true
    }

    /// 获取当前聚焦的应用程序名称 - 优化版本，复用窗口列表
    fn get_focused_application_from_list(window_list: &[WindowInfo]) -> Option<String> {
        // 通过分析窗口Z-order来推断当前聚焦的应用程序
        // 获取最上层的非系统窗口作为聚焦应用程序的指示
        for window in window_list {
            // 跳过系统窗口和菜单栏窗口
            if let Some(ref process_name) = window.process_name {
                let system_processes = [
                    "Window Server",
                    "Dock",
                    "SystemUIServer",
                    "ControlCenter",
                    "NotificationCenter",
                    "Spotlight",
                    "loginwindow",
                    "程序坞",
                    "控制中心",
                    "聚焦",
                    "TextInputMenuAgent",
                ];

                let is_system = system_processes
                    .iter()
                    .any(|&sys_proc| process_name.contains(sys_proc));

                // 如果是用户应用程序窗口且尺寸合理，认为是聚焦的应用程序
                if !is_system && window.width > 100 && window.height > 100 {
                    log::trace!(
                        "[MACOS_FOCUS] Inferred focused application from top window: {}",
                        process_name
                    );
                    return Some(process_name.clone());
                }
            }
        }

        log::debug!("[MACOS_FOCUS] Could not determine focused application");
        None
    }

    /// 窗口选择 - 严格按照Z-order选择最顶层窗口，确保不会选择被遮挡的窗口
    fn select_best_window_with_focus(
        candidates: &[WindowInfo],
        window_list: &[WindowInfo],
    ) -> Option<WindowInfo> {
        if candidates.is_empty() {
            return None;
        }

        // 🔧 BUG FIX: 严格按照Z-order选择，不允许焦点状态覆盖层级顺序
        // 候选窗口已经按照Z-order排序（layer值降序），第一个就是最顶层的窗口
        let topmost_window = candidates.first().cloned();

        if let Some(ref window) = topmost_window {
            // 获取当前聚焦的应用程序用于日志记录
            let focused_app = get_focused_application_from_list(window_list);

            // 使用静态变量跟踪上次的聚焦应用程序，只在变化时记录日志
            use std::sync::Mutex;
            static LAST_FOCUSED_APP: Mutex<Option<String>> = Mutex::new(None);

            if let Some(ref focused_name) = focused_app {
                let mut last_focused = LAST_FOCUSED_APP.lock().unwrap();
                let should_log = last_focused.as_ref() != Some(focused_name);
                if should_log {
                    log::debug!("[MACOS_FOCUS] 🔄 Focus changed to: {}", focused_name);
                    *last_focused = Some(focused_name.clone());
                }
            }

            // 检查选中的窗口是否来自焦点应用
            let is_from_focused_app = if let (Some(ref focused_name), Some(ref process_name)) =
                (&focused_app, &window.process_name) {
                process_name.to_lowercase().contains(&focused_name.to_lowercase()) ||
                focused_name.to_lowercase().contains(&process_name.to_lowercase())
            } else {
                false
            };

            if is_from_focused_app {
                log::trace!(
                    "[MACOS_FOCUS] ✅ Selected topmost window (also from focused app): {} ({})",
                    window.title.as_ref().unwrap_or(&"Untitled".to_string()),
                    window.process_name.as_ref().unwrap_or(&"Unknown".to_string())
                );
            } else {
                log::info!(
                    "[MACOS_FOCUS] ✅ Selected topmost window by Z-order: {} ({}) - layer={}, size={}x{}",
                    window.title.as_ref().unwrap_or(&"Untitled".to_string()),
                    window.process_name.as_ref().unwrap_or(&"Unknown".to_string()),
                    window.handle,
                    window.width,
                    window.height
                );

                // 如果选中的不是焦点应用的窗口，记录调试信息
                if let Some(ref focused_name) = focused_app {
                    log::debug!(
                        "[MACOS_FOCUS] Note: Focused app is '{}', but topmost window is from '{}'",
                        focused_name,
                        window.process_name.as_ref().unwrap_or(&"Unknown".to_string())
                    );
                }
            }
        }

        topmost_window
    }
}

// Linux平台特定的窗口检测实现
#[cfg(target_os = "linux")]
mod linux_detection {
    use super::*;

    /// Linux平台窗口检测（占位实现）
    pub fn get_window_under_cursor(x: i16, y: i16) -> Option<WindowInfo> {
        // TODO: 实现Linux窗口检测
        // 使用x11rb或检测Wayland环境
        println!(
            "[LINUX] Window detection not yet implemented for coordinates ({}, {})",
            x, y
        );
        None
    }
}

/// Windows平台增强窗口检测
#[cfg(target_os = "windows")]
#[command]
pub async fn detect_window_enhanced_windows(
    x: i32,
    y: i32,
) -> Result<Option<windows_detection::EnhancedWindowInfo>, String> {
    info!(
        "[WIN_ENHANCED_CMD] Windows enhanced detection command called at ({}, {})",
        x, y
    );

    match windows_detection::get_enhanced_window_info(x, y) {
        Some(enhanced_info) => {
            info!("[WIN_ENHANCED_CMD] Enhanced detection successful: handle=0x{:X}, title={:?}, process={:?}",
                enhanced_info.basic.handle, enhanced_info.basic.title, enhanced_info.basic.process_name);
            Ok(Some(enhanced_info))
        }
        None => {
            debug!(
                "[WIN_ENHANCED_CMD] No window found at coordinates ({}, {})",
                x, y
            );
            Ok(None)
        }
    }
}

/// macOS权限检查命令
#[cfg(target_os = "macos")]
#[command]
pub async fn check_macos_permissions() -> Result<bool, String> {
    info!("[MACOS_PERM] Checking macOS screen recording permissions");
    let has_permission = macos_detection::check_screen_recording_permission();
    info!("[MACOS_PERM] Permission check result: {}", has_permission);
    Ok(has_permission)
}

/// macOS权限请求命令
#[cfg(target_os = "macos")]
#[command]
pub async fn request_macos_permissions() -> Result<(), String> {
    info!("[MACOS_PERM] Requesting macOS screen recording permissions");
    macos_detection::request_screen_recording_permission()
}

/// 跨平台窗口检测（优先使用增强版本）
#[command]
pub async fn detect_window_smart(x: i32, y: i32) -> Result<Option<WindowInfo>, String> {
    log::info!("🧠 [BACKEND] === DETECT_WINDOW_SMART START ===");
    log::info!("🧠 [BACKEND] Position: ({}, {})", x, y);
    log::info!("🧠 [BACKEND] Platform: {}", std::env::consts::OS);

    #[cfg(target_os = "windows")]
    {
        debug!("[SMART_DETECT] Using Windows enhanced detection");
        match windows_detection::get_enhanced_window_info(x, y) {
            Some(enhanced) => {
                info!("[SMART_DETECT] Enhanced window info retrieved: handle={}, title={:?}, process={:?}, is_system={}, is_maximized={}, z_order={}",
                    enhanced.basic.handle,
                    enhanced.basic.title,
                    enhanced.basic.process_name,
                    enhanced.is_system,
                    enhanced.is_maximized,
                    enhanced.z_order
                );

                // 过滤系统窗口
                if enhanced.is_system {
                    info!(
                        "[SMART_DETECT] Filtering out system window: process={:?}",
                        enhanced.basic.process_name
                    );
                    Ok(None)
                } else {
                    info!(
                        "[SMART_DETECT] Valid user window detected: {}x{} at ({}, {})",
                        enhanced.basic.width,
                        enhanced.basic.height,
                        enhanced.basic.x,
                        enhanced.basic.y
                    );
                    Ok(Some(enhanced.basic))
                }
            }
            None => {
                debug!(
                    "[SMART_DETECT] No window found at coordinates ({}, {})",
                    x, y
                );
                Ok(None)
            }
        }
    }

    #[cfg(target_os = "macos")]
    {
        log::info!("🧠 [BACKEND] Using macOS detection");
        let result = macos_detection::get_window_under_cursor(x as f64, y as f64);
        if result.is_some() {
            log::info!("🧠 [BACKEND] ✅ macOS window detected");
            log::info!("🧠 [BACKEND] === DETECT_WINDOW_SMART END (SUCCESS) ===");
        } else {
            log::info!("🧠 [BACKEND] ❌ No macOS window found");
            log::info!("🧠 [BACKEND] === DETECT_WINDOW_SMART END (NO RESULT) ===");
        }
        Ok(result)
    }

    #[cfg(target_os = "linux")]
    {
        info!("[SMART_DETECT] Using Linux detection (placeholder)");
        let result = linux_detection::get_window_under_cursor(x as i16, y as i16);
        if result.is_some() {
            log::info!("🧠 [BACKEND] ✅ Linux window detected");
            log::info!("🧠 [BACKEND] === DETECT_WINDOW_SMART END (SUCCESS) ===");
        } else {
            log::info!("🧠 [BACKEND] ❌ No Linux window found");
            log::info!("🧠 [BACKEND] === DETECT_WINDOW_SMART END (NO RESULT) ===");
        }
        Ok(result)
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        log::error!("🧠 [BACKEND] ❌ Unsupported platform for window detection");
        log::info!("🧠 [BACKEND] === DETECT_WINDOW_SMART END (ERROR) ===");
        Err("Window detection not supported on this platform".to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hybrid_screenshot_manager() {
        let manager = HybridScreenshotManager::new();
        assert!(manager.is_ok());
    }

    #[test]
    fn test_window_detection_interface() {
        // 基本接口测试
        #[cfg(target_os = "windows")]
        {
            let result = windows_detection::get_window_under_cursor(100, 100);
            // 结果可能为None（如果没有窗口），但不应该panic
            println!("Windows detection result: {:?}", result);
        }
    }
}

/// 捕获选中的窗口截图
#[command]
pub async fn capture_window_by_info(
    window_info: WindowInfo,
    save_path: Option<String>,
) -> Result<ScreenshotResult, String> {
    info!(
        "[WINDOW_CAPTURE] Starting window capture for window: handle={}, title={:?}",
        window_info.handle, window_info.title
    );

    let start_time = std::time::Instant::now();

    // 获取所有窗口
    let windows = Window::all().map_err(|e| {
        let error_msg = format!("Failed to get window list: {}", e);
        log::error!("[WINDOW_CAPTURE] {}", error_msg);
        error_msg
    })?;

    // 查找目标窗口
    let target_window = windows.into_iter().find(|w| match w.id() {
        Ok(id) => id as u64 == window_info.handle,
        Err(_) => false,
    });

    let window = match target_window {
        Some(w) => w,
        None => {
            let error_msg = format!("Window with handle {} not found", window_info.handle);
            log::error!("[WINDOW_CAPTURE] {}", error_msg);
            return Err(error_msg);
        }
    };

    // 检查窗口是否最小化
    if let Ok(is_minimized) = window.is_minimized() {
        if is_minimized {
            let error_msg = "Cannot capture minimized window";
            log::warn!("[WINDOW_CAPTURE] {}", error_msg);
            return Err(error_msg.to_string());
        }
    }

    // 获取窗口信息用于日志
    let window_title = window.title().unwrap_or_else(|_| "Unknown".to_string());
    let window_width = window.width().unwrap_or(0);
    let window_height = window.height().unwrap_or(0);

    info!(
        "[WINDOW_CAPTURE] Capturing window: '{}' ({}x{})",
        window_title, window_width, window_height
    );

    // 🔧 LOGICAL DIMENSION FIX: 获取缩放因子
    let scale_factor = {
        // 获取窗口所在显示器的缩放因子
        let monitors = Monitor::all().map_err(|e| {
            let error_msg = format!("Failed to get monitors: {}", e);
            log::error!("[WINDOW_CAPTURE] {}", error_msg);
            error_msg
        })?;

        // 查找包含窗口中心点的显示器
        let window_center_x = window_info.x + (window_info.width as i32) / 2;
        let window_center_y = window_info.y + (window_info.height as i32) / 2;

        monitors.iter()
            .find(|monitor| {
                if let (Ok(m_x), Ok(m_y), Ok(m_width), Ok(m_height)) =
                    (monitor.x(), monitor.y(), monitor.width(), monitor.height()) {
                    window_center_x >= m_x && window_center_x < m_x + m_width as i32 &&
                    window_center_y >= m_y && window_center_y < m_y + m_height as i32
                } else {
                    false
                }
            })
            .and_then(|monitor| monitor.scale_factor().ok())
            .unwrap_or(1.0)
    };

    info!("[WINDOW_CAPTURE] Window scale factor: {}", scale_factor);

    // 执行窗口截图
    let raw_image = window.capture_image().map_err(|e| {
        let error_msg = format!("Failed to capture window image: {}", e);
        log::error!("[WINDOW_CAPTURE] {}", error_msg);
        error_msg
    })?;

    // 🔧 LOGICAL DIMENSION FIX: 缩放到逻辑尺寸
    let final_image = if scale_factor > 1.0 {
        info!(
            "[WINDOW_CAPTURE] Scaling to logical dimensions: {}x{} → {}x{}",
            raw_image.width(), raw_image.height(),
            window_info.width, window_info.height
        );

        image::imageops::resize(
            &raw_image,
            window_info.width,
            window_info.height,
            image::imageops::FilterType::Lanczos3
        )
    } else {
        raw_image
    };

    let capture_time = start_time.elapsed().as_millis() as u64;
    info!(
        "[WINDOW_CAPTURE] Window captured in {}ms, final dimensions: {}x{}",
        capture_time, final_image.width(), final_image.height()
    );

    // 生成保存路径
    let timestamp = chrono::Utc::now().timestamp_millis();
    let save_path = save_path.unwrap_or_else(|| {
        let safe_title = window_title
            .chars()
            .filter(|c| c.is_alphanumeric() || *c == ' ' || *c == '-' || *c == '_')
            .collect::<String>()
            .replace(' ', "_");

        format!("mecap_window_{}_{}.png", safe_title, timestamp)
    });

    // 保存图像
    final_image.save(&save_path).map_err(|e| {
        let error_msg = format!("Failed to save window screenshot: {}", e);
        log::error!("[WINDOW_CAPTURE] {}", error_msg);
        error_msg
    })?;

    info!("[WINDOW_CAPTURE] Window screenshot saved to: {}", save_path);

    // 创建截图结果
    let result = ScreenshotResult {
        path: save_path,
        width: Some(final_image.width()),
        height: Some(final_image.height()),
        timestamp: timestamp as u64,
        region: Some(ScreenRegion {
            x: window_info.x,
            y: window_info.y,
            width: window_info.width,
            height: window_info.height,
            monitor_index: 0, // 默认主显示器，后续可以根据窗口位置智能判断
        }),
    };

    Ok(result)
}
