// 独立工具栏窗口管理模块
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Mutex;
use lazy_static::lazy_static;
use tauri::{command, AppH<PERSON>le, Emitter, Manager, WebviewUrl, WebviewWindowBuilder, PhysicalPosition, PhysicalSize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolbarConfig {
    pub layout: String, // "vertical" or "horizontal"
    pub position: String, // "right", "left", "top", "bottom"
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PreviewWindowInfo {
    pub id: String,
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScreenInfo {
    pub width: u32,
    pub height: u32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PositionResult {
    pub x: i32,
    pub y: i32,
    pub layout: String,
    pub position: String,
    pub score: f64,
    pub reason: String,
}

// 工具栏窗口跟踪 - 映射工具栏ID到预览窗口ID
lazy_static! {
    static ref ACTIVE_TOOLBARS: Mutex<HashMap<String, String>> = Mutex::new(HashMap::new());
    static ref TOOLBAR_CONFIGS: Mutex<HashMap<String, ToolbarConfig>> = Mutex::new(HashMap::new());
}

/// 创建独立工具栏窗口
/// 
/// 这个函数创建一个完全独立的工具栏窗口，与预览窗口分离
#[command]
pub async fn create_independent_toolbar_window(
    app_handle: AppHandle,
    preview_window_id: String,
    preview_bounds: PreviewWindowInfo,
    screen_info: ScreenInfo,
) -> Result<String, String> {
    log::info!("[TOOLBAR] 🔧 Creating independent toolbar window for preview: {}", preview_window_id);

    let toolbar_id = format!("toolbar_{}", uuid::Uuid::new_v4());
    
    // 计算工具栏的最佳位置
    let position_result = calculate_optimal_position(&preview_bounds, &screen_info);
    log::info!("[TOOLBAR] 📊 Calculated position: {:?}", position_result);

    // 创建工具栏配置
    let toolbar_config = ToolbarConfig {
        layout: position_result.layout.clone(),
        position: position_result.position.clone(),
        x: position_result.x,
        y: position_result.y,
        width: if position_result.layout == "vertical" { 48 } else { 320 },
        height: if position_result.layout == "vertical" { 420 } else { 48 },
    };

    // 创建工具栏窗口
    log::info!("[TOOLBAR] 🔧 Creating window with config: {}x{} at ({}, {})",
               toolbar_config.width, toolbar_config.height,
               toolbar_config.x, toolbar_config.y);

    let window = WebviewWindowBuilder::new(
        &app_handle,
        &toolbar_id,
        WebviewUrl::App("independent-toolbar.html".into()),
    )
    .title("Mecap Toolbar")
    .inner_size(toolbar_config.width as f64, toolbar_config.height as f64)
    .position((toolbar_config.x as f64) / 2.0, (toolbar_config.y as f64) / 2.0)
    .decorations(false)
    .always_on_top(true)
    .resizable(false)
    .visible(true)
    .focused(false) // 不抢夺焦点
    .skip_taskbar(true)
    .build()
    .map_err(|e| format!("Failed to create toolbar window: {}", e))?;

    log::info!("[TOOLBAR] 🔧 Window created successfully, checking visibility...");

    // 验证窗口状态
    if let Ok(is_visible) = window.is_visible() {
        log::info!("[TOOLBAR] 🔧 Window visibility: {}", is_visible);
    }

    if let Ok(position) = window.outer_position() {
        log::info!("[TOOLBAR] 🔧 Window actual position: ({}, {})", position.x, position.y);
    }

    if let Ok(size) = window.outer_size() {
        log::info!("[TOOLBAR] 🔧 Window actual size: {}x{}", size.width, size.height);
    }

    log::info!("[TOOLBAR] ✅ Independent toolbar window created: {}", toolbar_id);

    // 保存工具栏信息
    {
        let mut active_toolbars = ACTIVE_TOOLBARS.lock().unwrap();
        active_toolbars.insert(toolbar_id.clone(), preview_window_id.clone());
        
        let mut toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
        toolbar_configs.insert(toolbar_id.clone(), toolbar_config);
        
        log::info!("[TOOLBAR] 📝 Added toolbar to tracking list: {}", toolbar_id);
    }

    // 发送初始化事件到工具栏窗口
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    
    match app_handle.emit_to(&toolbar_id, "toolbar-init", serde_json::json!({
        "toolbarId": toolbar_id,
        "previewWindowId": preview_window_id,
        "config": position_result
    })) {
        Ok(_) => log::info!("[TOOLBAR] ✅ Initialization event sent to toolbar"),
        Err(e) => log::warn!("[TOOLBAR] ⚠️ Failed to send initialization event: {}", e),
    }

    Ok(toolbar_id)
}

/// 更新工具栏位置
#[command]
pub async fn update_toolbar_position(
    app_handle: AppHandle,
    toolbar_id: String,
    preview_bounds: PreviewWindowInfo,
    screen_info: ScreenInfo,
) -> Result<PositionResult, String> {
    log::info!("[TOOLBAR] 🔄 Updating toolbar position: {}", toolbar_id);

    // 计算新的最佳位置
    let position_result = calculate_optimal_position(&preview_bounds, &screen_info);

    // 获取工具栏窗口
    if let Some(toolbar_window) = app_handle.get_webview_window(&toolbar_id) {
        // 更新窗口位置
        let new_position = PhysicalPosition::new(position_result.x, position_result.y);
        toolbar_window.set_position(new_position)
            .map_err(|e| format!("Failed to update toolbar position: {}", e))?;

        // 如果布局改变，更新窗口大小
        let current_config = {
            let toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
            toolbar_configs.get(&toolbar_id).cloned()
        };

        if let Some(config) = current_config {
            if config.layout != position_result.layout {
                let new_size = if position_result.layout == "vertical" {
                    PhysicalSize::new(48, 420)
                } else {
                    PhysicalSize::new(320, 48)
                };
                
                toolbar_window.set_size(new_size)
                    .map_err(|e| format!("Failed to update toolbar size: {}", e))?;

                // 通知工具栏窗口布局改变
                match app_handle.emit_to(&toolbar_id, "layout-change", serde_json::json!({
                    "layout": position_result.layout
                })) {
                    Ok(_) => log::info!("[TOOLBAR] ✅ Layout change event sent"),
                    Err(e) => log::warn!("[TOOLBAR] ⚠️ Failed to send layout change event: {}", e),
                }
            }
        }

        // 更新配置
        {
            let mut toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
            if let Some(config) = toolbar_configs.get_mut(&toolbar_id) {
                config.layout = position_result.layout.clone();
                config.position = position_result.position.clone();
                config.x = position_result.x;
                config.y = position_result.y;
            }
        }

        log::info!("[TOOLBAR] ✅ Toolbar position updated successfully");
        Ok(position_result)
    } else {
        Err(format!("Toolbar window not found: {}", toolbar_id))
    }
}

/// 关闭工具栏窗口
#[command]
pub async fn close_toolbar_window(
    app_handle: AppHandle,
    toolbar_id: String,
) -> Result<(), String> {
    log::info!("[TOOLBAR] 🗑️ Closing toolbar window: {}", toolbar_id);

    // 获取并关闭工具栏窗口
    if let Some(toolbar_window) = app_handle.get_webview_window(&toolbar_id) {
        toolbar_window.close()
            .map_err(|e| format!("Failed to close toolbar window: {}", e))?;
    }

    // 清理跟踪信息
    {
        let mut active_toolbars = ACTIVE_TOOLBARS.lock().unwrap();
        active_toolbars.remove(&toolbar_id);
        
        let mut toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
        toolbar_configs.remove(&toolbar_id);
        
        log::info!("[TOOLBAR] 🗑️ Removed toolbar from tracking list: {}", toolbar_id);
    }

    log::info!("[TOOLBAR] ✅ Toolbar window closed successfully");
    Ok(())
}

/// 获取工具栏信息
#[command]
pub async fn get_toolbar_info(toolbar_id: String) -> Result<Option<ToolbarConfig>, String> {
    let toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
    Ok(toolbar_configs.get(&toolbar_id).cloned())
}

/// 列出所有活动的工具栏
#[command]
pub async fn list_active_toolbars() -> Result<Vec<String>, String> {
    let active_toolbars = ACTIVE_TOOLBARS.lock().unwrap();
    Ok(active_toolbars.keys().cloned().collect())
}

/// 根据预览窗口ID查找工具栏
#[command]
pub async fn find_toolbar_by_preview(preview_window_id: String) -> Result<Option<String>, String> {
    let active_toolbars = ACTIVE_TOOLBARS.lock().unwrap();
    for (toolbar_id, preview_id) in active_toolbars.iter() {
        if preview_id == &preview_window_id {
            return Ok(Some(toolbar_id.clone()));
        }
    }
    Ok(None)
}

/// 🔧 CRITICAL FIX: 关闭所有活跃的工具栏窗口
#[command]
pub async fn close_all_active_toolbars(app_handle: AppHandle) -> Result<u32, String> {
    log::info!("[TOOLBAR] 🗑️ Closing all active toolbar windows");

    let toolbar_ids: Vec<String> = {
        let active_toolbars = ACTIVE_TOOLBARS.lock().unwrap();
        active_toolbars.keys().cloned().collect()
    };

    let mut closed_count = 0;

    for toolbar_id in toolbar_ids {
        log::info!("[TOOLBAR] 🗑️ Closing toolbar: {}", toolbar_id);

        // 关闭工具栏窗口
        if let Some(toolbar_window) = app_handle.get_webview_window(&toolbar_id) {
            match toolbar_window.close() {
                Ok(_) => {
                    closed_count += 1;
                    log::info!("[TOOLBAR] ✅ Toolbar closed: {}", toolbar_id);
                }
                Err(e) => {
                    log::warn!("[TOOLBAR] ⚠️ Failed to close toolbar {}: {}", toolbar_id, e);
                }
            }
        } else {
            log::warn!("[TOOLBAR] ⚠️ Toolbar window not found: {}", toolbar_id);
        }
    }

    // 清理所有跟踪信息
    {
        let mut active_toolbars = ACTIVE_TOOLBARS.lock().unwrap();
        active_toolbars.clear();

        let mut toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
        toolbar_configs.clear();

        log::info!("[TOOLBAR] 🗑️ Cleared all toolbar tracking information");
    }

    log::info!("[TOOLBAR] ✅ Closed {} toolbar windows", closed_count);
    Ok(closed_count)
}

/// 🔧 ALIAS: 关闭所有工具栏窗口（前端调用的别名）
#[command]
pub async fn close_all_toolbar_windows(app_handle: AppHandle) -> Result<u32, String> {
    close_all_active_toolbars(app_handle).await
}

/// 🆕 为区域选择阶段创建独立工具栏
#[command]
pub async fn create_independent_toolbar_for_region(
    app_handle: AppHandle,
    region: serde_json::Value,
) -> Result<String, String> {
    log::info!("[TOOLBAR] 🔧 Creating independent toolbar for region selection phase");

    // 解析区域坐标
    let x = region["x"].as_f64().ok_or("Invalid region x coordinate")?;
    let y = region["y"].as_f64().ok_or("Invalid region y coordinate")?;
    let width = region["width"].as_f64().ok_or("Invalid region width")?;
    let height = region["height"].as_f64().ok_or("Invalid region height")?;

    log::info!("[TOOLBAR] 📊 Region coordinates: x={}, y={}, width={}, height={}", x, y, width, height);

    // 创建预览窗口信息结构
    let preview_bounds = PreviewWindowInfo {
        id: "region_selection".to_string(),
        x: x as i32,
        y: y as i32,
        width: width as u32,
        height: height as u32,
    };

    // 获取屏幕信息
    let screen_info = ScreenInfo {
        width: 3360, // TODO: 获取实际屏幕尺寸
        height: 2100,
    };

    // 使用现有的工具栏创建函数
    let toolbar_id = create_independent_toolbar_window(
        app_handle,
        "region_selection".to_string(), // 特殊ID表示区域选择阶段
        preview_bounds,
        screen_info,
    ).await?;

    log::info!("[TOOLBAR] ✅ Independent toolbar created for region selection: {}", toolbar_id);
    Ok(toolbar_id)
}

/// 🆕 智能窗口焦点管理
#[command]
pub async fn request_window_focus(
    app_handle: AppHandle,
    window_id: String,
    delay_ms: Option<u64>,
) -> Result<(), String> {
    let delay = delay_ms.unwrap_or(150); // 默认150ms延迟

    log::debug!("[TOOLBAR] 🎯 Focus request for window: {} with delay: {}ms", window_id, delay);

    // 延迟执行焦点设置，避免与鼠标移动冲突
    let app_handle_clone = app_handle.clone();
    let window_id_clone = window_id.clone();

    tokio::spawn(async move {
        tokio::time::sleep(tokio::time::Duration::from_millis(delay)).await;

        if let Some(window) = app_handle_clone.get_webview_window(&window_id_clone) {
            match window.set_focus() {
                Ok(_) => {
                    log::debug!("[TOOLBAR] 🎯 Focus set successfully for window: {}", window_id_clone);
                }
                Err(e) => {
                    log::warn!("[TOOLBAR] ⚠️ Failed to set focus for window {}: {}", window_id_clone, e);
                }
            }
        } else {
            log::warn!("[TOOLBAR] ⚠️ Window not found for focus: {}", window_id_clone);
        }
    });

    Ok(())
}

/// 🆕 检查窗口是否有焦点
#[command]
pub async fn check_window_focus(
    app_handle: AppHandle,
    window_id: String,
) -> Result<bool, String> {
    if let Some(window) = app_handle.get_webview_window(&window_id) {
        match window.is_focused() {
            Ok(focused) => {
                log::debug!("[TOOLBAR] 🎯 Window {} focus status: {}", window_id, focused);
                Ok(focused)
            }
            Err(e) => {
                log::warn!("[TOOLBAR] ⚠️ Failed to check focus for window {}: {}", window_id, e);
                Ok(false)
            }
        }
    } else {
        log::warn!("[TOOLBAR] ⚠️ Window not found for focus check: {}", window_id);
        Ok(false)
    }
}

/// 更新工具栏拖拽后的位置
#[command]
pub async fn update_toolbar_position_after_drag(
    app_handle: AppHandle,
) -> Result<(), String> {
    log::info!("[TOOLBAR] 🖱️ Updating toolbar position after drag");

    // 遍历所有工具栏窗口，找到最近移动的那个
    let active_toolbars = {
        let toolbars = ACTIVE_TOOLBARS.lock().unwrap();
        toolbars.keys().cloned().collect::<Vec<_>>()
    };

    for toolbar_id in active_toolbars {
        if let Some(window) = app_handle.get_webview_window(&toolbar_id) {
            // 获取当前窗口位置
            match window.outer_position() {
                Ok(position) => {
                    log::info!("[TOOLBAR] 📍 Checking toolbar {} position: ({}, {})", toolbar_id, position.x, position.y);

                    // 检查位置是否与配置中的位置不同（说明被拖拽了）
                    let position_changed = {
                        let toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
                        if let Some(config) = toolbar_configs.get(&toolbar_id) {
                            config.x != position.x || config.y != position.y
                        } else {
                            false
                        }
                    };

                    if position_changed {
                        log::info!("[TOOLBAR] 🖱️ Detected position change for toolbar: {}", toolbar_id);

                        // 更新配置中的位置
                        {
                            let mut toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
                            if let Some(config) = toolbar_configs.get_mut(&toolbar_id) {
                                config.x = position.x;
                                config.y = position.y;
                                log::info!("[TOOLBAR] ✅ Updated toolbar config position");
                            }
                        }

                        // 验证位置是否合理（可选：检查是否与预览窗口重叠）
                        validate_toolbar_position(&app_handle, &toolbar_id, position.x, position.y).await?;

                        return Ok(());
                    }
                }
                Err(e) => {
                    log::warn!("[TOOLBAR] ⚠️ Failed to get position for toolbar {}: {}", toolbar_id, e);
                }
            }
        }
    }

    log::info!("[TOOLBAR] ℹ️ No position changes detected");
    Ok(())
}

/// 验证工具栏位置是否合理
async fn validate_toolbar_position(
    app_handle: &AppHandle,
    toolbar_id: &str,
    x: i32,
    y: i32,
) -> Result<(), String> {
    log::info!("[TOOLBAR] 🔍 Validating toolbar position: ({}, {})", x, y);

    // 获取关联的预览窗口ID
    let preview_window_id = {
        let active_toolbars = ACTIVE_TOOLBARS.lock().unwrap();
        active_toolbars.get(toolbar_id).cloned()
    };

    if let Some(preview_id) = preview_window_id {
        // 获取预览窗口
        if let Some(preview_window) = app_handle.get_webview_window(&preview_id) {
            match preview_window.outer_position() {
                Ok(preview_pos) => {
                    match preview_window.outer_size() {
                        Ok(preview_size) => {
                            // 检查是否重叠（简单检查）
                            let toolbar_config = {
                                let toolbar_configs = TOOLBAR_CONFIGS.lock().unwrap();
                                toolbar_configs.get(toolbar_id).cloned()
                            };

                            if let Some(config) = toolbar_config {
                                let overlap = check_window_overlap(
                                    x, y, config.width as i32, config.height as i32,
                                    preview_pos.x, preview_pos.y,
                                    preview_size.width as i32, preview_size.height as i32
                                );

                                if overlap {
                                    log::warn!("[TOOLBAR] ⚠️ Toolbar overlaps with preview window");
                                    // 可以选择自动调整位置或只是警告
                                }
                            }
                        }
                        Err(e) => log::warn!("[TOOLBAR] ⚠️ Failed to get preview window size: {}", e),
                    }
                }
                Err(e) => log::warn!("[TOOLBAR] ⚠️ Failed to get preview window position: {}", e),
            }
        }
    }

    Ok(())
}

/// 检查两个窗口是否重叠
fn check_window_overlap(
    x1: i32, y1: i32, w1: i32, h1: i32,
    x2: i32, y2: i32, w2: i32, h2: i32,
) -> bool {
    !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1)
}

/// 计算工具栏的最佳位置
pub fn calculate_optimal_position(preview_bounds: &PreviewWindowInfo, screen_info: &ScreenInfo) -> PositionResult {
    let margin = 20; // 屏幕边缘边距
    let window_separation = 20; // 工具栏与预览窗口的分离距离
    let toolbar_vertical_size = (48, 420);
    let toolbar_horizontal_size = (320, 48);

    // 计算各个位置的可用空间（考虑分离距离）
    let right_space = screen_info.width as i32 - (preview_bounds.x + preview_bounds.width as i32) - window_separation - margin;
    let left_space = preview_bounds.x - window_separation - margin;
    let top_space = preview_bounds.y - window_separation - margin;
    let bottom_space = screen_info.height as i32 - (preview_bounds.y + preview_bounds.height as i32) - window_separation - margin;

    log::info!("[TOOLBAR] 📊 Available spaces - right: {}, left: {}, top: {}, bottom: {}", 
               right_space, left_space, top_space, bottom_space);

    // 策略1: 右侧垂直布局
    if right_space >= toolbar_vertical_size.0 {
        return PositionResult {
            x: preview_bounds.x + preview_bounds.width as i32 + window_separation,
            y: preview_bounds.y + (preview_bounds.height as i32 - toolbar_vertical_size.1) / 2,
            layout: "vertical".to_string(),
            position: "right".to_string(),
            score: 90.0,
            reason: "预览窗口右侧 - 最佳用户体验".to_string(),
        };
    }

    // 策略2: 左侧垂直布局
    if left_space >= toolbar_vertical_size.0 {
        return PositionResult {
            x: preview_bounds.x - toolbar_vertical_size.0 - window_separation,
            y: preview_bounds.y + (preview_bounds.height as i32 - toolbar_vertical_size.1) / 2,
            layout: "vertical".to_string(),
            position: "left".to_string(),
            score: 80.0,
            reason: "预览窗口左侧 - 避免遮挡".to_string(),
        };
    }

    // 策略3: 上方水平布局
    if top_space >= toolbar_horizontal_size.1 {
        return PositionResult {
            x: preview_bounds.x + (preview_bounds.width as i32 - toolbar_horizontal_size.0) / 2,
            y: preview_bounds.y - toolbar_horizontal_size.1 - window_separation,
            layout: "horizontal".to_string(),
            position: "top".to_string(),
            score: 85.0,
            reason: "预览窗口上方 - 优先显示位置".to_string(),
        };
    }

    // 策略4: 下方水平布局
    if bottom_space >= toolbar_horizontal_size.1 {
        return PositionResult {
            x: preview_bounds.x + (preview_bounds.width as i32 - toolbar_horizontal_size.0) / 2,
            y: preview_bounds.y + preview_bounds.height as i32 + window_separation,
            layout: "horizontal".to_string(),
            position: "bottom".to_string(),
            score: 70.0,
            reason: "预览窗口下方 - 备选位置".to_string(),
        };
    }

    // 后备策略: 屏幕顶部
    PositionResult {
        x: (screen_info.width as i32 - toolbar_horizontal_size.0) / 2,
        y: margin,
        layout: "horizontal".to_string(),
        position: "top".to_string(),
        score: 50.0,
        reason: "屏幕顶部 - 后备位置".to_string(),
    }
}
