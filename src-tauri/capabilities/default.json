{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main", "screenshot_preview_*", "toolbar_*"], "permissions": ["core:default", "opener:default", "fs:allow-write-file", "fs:allow-read-file", "fs:allow-exists", "fs:allow-create", "fs:allow-mkdir", "fs:allow-remove", "notification:default", "notification:allow-is-permission-granted", "notification:allow-request-permission", "notification:allow-show", "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-unregister", "core:window:allow-set-ignore-cursor-events", "core:window:allow-create", "core:window:allow-close", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-set-focus", "core:window:allow-outer-position", "core:window:allow-inner-position", "core:window:allow-set-position", "core:window:allow-start-dragging", {"identifier": "fs:scope", "allow": [{"path": "$PICTURE"}, {"path": "$PICTURE/**"}, {"path": "$HOME/Pictures"}, {"path": "$HOME/Pictures/**"}, {"path": "$TEMP"}, {"path": "$TEMP/**"}, {"path": "$APPDATA"}, {"path": "$APPDATA/**"}]}]}