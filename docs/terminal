[2025-07-31 23:57:01.436][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.436][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050
[2025-07-31 23:57:01.436][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows
[2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order
[2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.438][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.438][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25)
[2025-07-31 23:57:01.438][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25)
[2025-07-31 23:57:01.438][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25)
[2025-07-31 23:57:01.439][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25)
[2025-07-31 23:57:01.439][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25)
[2025-07-31 23:57:01.439][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.439][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23)
[2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0)
[2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0)
[2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0)
[2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0)
[2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0)
[2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0)
[2025-07-31 23:57:01.441][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0)
[2025-07-31 23:57:01.441][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0)
[2025-07-31 23:57:01.441][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Retrieved 19 windows from system
[2025-07-31 23:57:01.441][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 10 contains point (960, 311): Some("通知中心") - Some("Notification Center") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:01.442][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (960, 311)
[2025-07-31 23:57:01.442][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 11 contains point (960, 311): Some("Code") - Some("lib.rs — Mecap") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.442][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311)
[2025-07-31 23:57:01.443][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'lib.rs — Mecap' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.444][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 12 contains point (960, 311): Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.444][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311)
[2025-07-31 23:57:01.444][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.448][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 13 contains point (960, 311): Some("访达") - Some("下载") (821x776 at 449,78) layer=0
[2025-07-31 23:57:01.448][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311)
[2025-07-31 23:57:01.448][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '下载' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.449][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 14 contains point (960, 311): Some("备忘录") - Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:01.450][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311)
[2025-07-31 23:57:01.450][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '备忘录' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.451][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 16 contains point (960, 311): Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (437x623 at 590,133) layer=0
[2025-07-31 23:57:01.451][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311)
[2025-07-31 23:57:01.452][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'mxm_pyenv — -zsh — 61×42' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.454][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 17 contains point (960, 311): Some("访达") - Some("") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.455][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311)
[2025-07-31 23:57:01.456][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.456][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 18 contains point (960, 311): Some("程序坞") - Some("Wallpaper-") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.457][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311)
[2025-07-31 23:57:01.457][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'Wallpaper-' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.458][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (960, 311), Z-order analysis:
[2025-07-31 23:57:01.458][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST)
[2025-07-31 23:57:01.459][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心
[2025-07-31 23:57:01.460][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心)
[2025-07-31 23:57:01.460][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (960, 311): Notification Center (通知中心) layer=23 in 68ms
[2025-07-31 23:57:01.461][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected
[2025-07-31 23:57:01.493][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (823, 536)
[2025-07-31 23:57:01.494][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder)
[2025-07-31 23:57:01.494][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (823, 536)
[2025-07-31 23:57:01.496][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows
[2025-07-31 23:57:01.496][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system
[2025-07-31 23:57:01.504][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system
[2025-07-31 23:57:01.505][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25
[2025-07-31 23:57:01.506][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999
[2025-07-31 23:57:01.506][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0
[2025-07-31 23:57:01.507][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small
[2025-07-31 23:57:01.507][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25
[2025-07-31 23:57:01.508][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25
[2025-07-31 23:57:01.508][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24
[2025-07-31 23:57:01.508][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25
[2025-07-31 23:57:01.508][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25
[2025-07-31 23:57:01.509][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24
[2025-07-31 23:57:01.509][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24
[2025-07-31 23:57:01.509][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25
[2025-07-31 23:57:01.510][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25
[2025-07-31 23:57:01.510][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24
[2025-07-31 23:57:01.510][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24
[2025-07-31 23:57:01.510][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25
[2025-07-31 23:57:01.511][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25
[2025-07-31 23:57:01.511][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24
[2025-07-31 23:57:01.511][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24
[2025-07-31 23:57:01.512][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25
[2025-07-31 23:57:01.513][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25
[2025-07-31 23:57:01.513][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24
[2025-07-31 23:57:01.513][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24
[2025-07-31 23:57:01.513][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25
[2025-07-31 23:57:01.514][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25
[2025-07-31 23:57:01.514][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24
[2025-07-31 23:57:01.514][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24
[2025-07-31 23:57:01.515][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25
[2025-07-31 23:57:01.515][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25
[2025-07-31 23:57:01.515][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24
[2025-07-31 23:57:01.516][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24
[2025-07-31 23:57:01.516][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25
[2025-07-31 23:57:01.516][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25
[2025-07-31 23:57:01.516][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24
[2025-07-31 23:57:01.517][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24
[2025-07-31 23:57:01.517][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25
[2025-07-31 23:57:01.517][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25
[2025-07-31 23:57:01.517][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24
[2025-07-31 23:57:01.518][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24
[2025-07-31 23:57:01.518][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25
[2025-07-31 23:57:01.518][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25
[2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24
[2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer
[2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25
[2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25
[2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24
[2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24
[2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25
[2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25
[2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24
[2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24
[2025-07-31 23:57:01.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25
[2025-07-31 23:57:01.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24
[2025-07-31 23:57:01.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24
[2025-07-31 23:57:01.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:01.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25
[2025-07-31 23:57:01.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:01.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050
[2025-07-31 23:57:01.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050
[2025-07-31 23:57:01.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25
[2025-07-31 23:57:01.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5
[2025-07-31 23:57:01.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050
[2025-07-31 23:57:01.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25
[2025-07-31 23:57:01.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025
[2025-07-31 23:57:01.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025
[2025-07-31 23:57:01.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25
[2025-07-31 23:57:01.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025
[2025-07-31 23:57:01.526][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025
[2025-07-31 23:57:01.526][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25
[2025-07-31 23:57:01.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0
[2025-07-31 23:57:01.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776
[2025-07-31 23:57:01.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776
[2025-07-31 23:57:01.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25
[2025-07-31 23:57:01.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:01.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805
[2025-07-31 23:57:01.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805
[2025-07-31 23:57:01.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25
[2025-07-31 23:57:01.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0
[2025-07-31 23:57:01.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651
[2025-07-31 23:57:01.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651
[2025-07-31 23:57:01.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25
[2025-07-31 23:57:01.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0
[2025-07-31 23:57:01.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623
[2025-07-31 23:57:01.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623
[2025-07-31 23:57:01.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25
[2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050
[2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050
[2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25
[2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050
[2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050
[2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25
[2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050
[2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:01.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows
[2025-07-31 23:57:01.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order
[2025-07-31 23:57:01.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25)
[2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25)
[2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25)
[2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25)
[2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25)
[2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23)
[2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0)
[2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0)
[2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0)
[2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0)
[2025-07-31 23:57:01.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0)
[2025-07-31 23:57:01.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0)
[2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0)
[2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0)
[2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Retrieved 19 windows from system
[2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 10 contains point (823, 536): Some("通知中心") - Some("Notification Center") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (823, 536)
[2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 11 contains point (823, 536): Some("Code") - Some("lib.rs — Mecap") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.540][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536)
[2025-07-31 23:57:01.540][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'lib.rs — Mecap' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.540][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 12 contains point (823, 536): Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.540][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536)
[2025-07-31 23:57:01.540][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.540][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 13 contains point (823, 536): Some("访达") - Some("下载") (821x776 at 449,78) layer=0
[2025-07-31 23:57:01.541][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536)
[2025-07-31 23:57:01.541][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '下载' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.541][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 14 contains point (823, 536): Some("备忘录") - Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:01.542][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536)
[2025-07-31 23:57:01.542][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '备忘录' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.542][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 16 contains point (823, 536): Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (437x623 at 590,133) layer=0
[2025-07-31 23:57:01.542][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536)
[2025-07-31 23:57:01.543][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'mxm_pyenv — -zsh — 61×42' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.543][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 17 contains point (823, 536): Some("访达") - Some("") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.543][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536)
[2025-07-31 23:57:01.543][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.543][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 18 contains point (823, 536): Some("程序坞") - Some("Wallpaper-") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.543][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536)
[2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'Wallpaper-' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (823, 536), Z-order analysis:
[2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST)
[2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心
[2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心)
[2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (823, 536): Notification Center (通知中心) layer=23 in 49ms
[2025-07-31 23:57:01.545][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected
[2025-07-31 23:57:01.671][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (808, 550)
[2025-07-31 23:57:01.671][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder)
[2025-07-31 23:57:01.671][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (808, 550)
[2025-07-31 23:57:01.672][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows
[2025-07-31 23:57:01.673][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system
[2025-07-31 23:57:01.674][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system
[2025-07-31 23:57:01.674][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25
[2025-07-31 23:57:01.674][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999
[2025-07-31 23:57:01.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0
[2025-07-31 23:57:01.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small
[2025-07-31 23:57:01.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25
[2025-07-31 23:57:01.676][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25
[2025-07-31 23:57:01.676][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24
[2025-07-31 23:57:01.677][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25
[2025-07-31 23:57:01.677][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25
[2025-07-31 23:57:01.678][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24
[2025-07-31 23:57:01.678][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24
[2025-07-31 23:57:01.678][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25
[2025-07-31 23:57:01.679][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25
[2025-07-31 23:57:01.680][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24
[2025-07-31 23:57:01.680][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24
[2025-07-31 23:57:01.681][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25
[2025-07-31 23:57:01.682][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25
[2025-07-31 23:57:01.682][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24
[2025-07-31 23:57:01.683][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24
[2025-07-31 23:57:01.684][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25
[2025-07-31 23:57:01.684][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25
[2025-07-31 23:57:01.684][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24
[2025-07-31 23:57:01.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24
[2025-07-31 23:57:01.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25
[2025-07-31 23:57:01.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25
[2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24
[2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24
[2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25
[2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25
[2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24
[2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24
[2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25
[2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25
[2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24
[2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24
[2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25
[2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25
[2025-07-31 23:57:01.714][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24
[2025-07-31 23:57:01.714][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24
[2025-07-31 23:57:01.714][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25
[2025-07-31 23:57:01.714][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25
[2025-07-31 23:57:01.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24
[2025-07-31 23:57:01.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer
[2025-07-31 23:57:01.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25
[2025-07-31 23:57:01.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25
[2025-07-31 23:57:01.716][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24
[2025-07-31 23:57:01.716][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24
[2025-07-31 23:57:01.716][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25
[2025-07-31 23:57:01.717][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25
[2025-07-31 23:57:01.717][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24
[2025-07-31 23:57:01.717][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24
[2025-07-31 23:57:01.717][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25
[2025-07-31 23:57:01.718][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24
[2025-07-31 23:57:01.718][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24
[2025-07-31 23:57:01.718][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:01.719][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25
[2025-07-31 23:57:01.719][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:01.719][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050
[2025-07-31 23:57:01.719][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050
[2025-07-31 23:57:01.720][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25
[2025-07-31 23:57:01.721][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5
[2025-07-31 23:57:01.721][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050
[2025-07-31 23:57:01.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25
[2025-07-31 23:57:01.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025
[2025-07-31 23:57:01.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025
[2025-07-31 23:57:01.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25
[2025-07-31 23:57:01.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.724][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025
[2025-07-31 23:57:01.724][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025
[2025-07-31 23:57:01.725][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25
[2025-07-31 23:57:01.725][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0
[2025-07-31 23:57:01.726][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776
[2025-07-31 23:57:01.726][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776
[2025-07-31 23:57:01.726][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25
[2025-07-31 23:57:01.727][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:01.727][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805
[2025-07-31 23:57:01.727][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805
[2025-07-31 23:57:01.728][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25
[2025-07-31 23:57:01.728][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0
[2025-07-31 23:57:01.728][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651
[2025-07-31 23:57:01.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651
[2025-07-31 23:57:01.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25
[2025-07-31 23:57:01.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0
[2025-07-31 23:57:01.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623
[2025-07-31 23:57:01.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623
[2025-07-31 23:57:01.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25
[2025-07-31 23:57:01.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050
[2025-07-31 23:57:01.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050
[2025-07-31 23:57:01.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25
[2025-07-31 23:57:01.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050
[2025-07-31 23:57:01.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050
[2025-07-31 23:57:01.734][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25
[2025-07-31 23:57:01.734][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.735][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050
[2025-07-31 23:57:01.735][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:01.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows
[2025-07-31 23:57:01.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order
[2025-07-31 23:57:01.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25)
[2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25)
[2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25)
[2025-07-31 23:57:01.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25)
[2025-07-31 23:57:01.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25)
[2025-07-31 23:57:01.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23)
[2025-07-31 23:57:01.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0)
[2025-07-31 23:57:01.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0)
[2025-07-31 23:57:01.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0)
[2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0)
[2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0)
[2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0)
[2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0)
[2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0)
[2025-07-31 23:57:01.741][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Retrieved 19 windows from system
[2025-07-31 23:57:01.741][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 10 contains point (808, 550): Some("通知中心") - Some("Notification Center") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:01.741][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (808, 550)
[2025-07-31 23:57:01.742][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 11 contains point (808, 550): Some("Code") - Some("lib.rs — Mecap") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.742][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550)
[2025-07-31 23:57:01.742][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'lib.rs — Mecap' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.742][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 12 contains point (808, 550): Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.743][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550)
[2025-07-31 23:57:01.743][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.744][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 13 contains point (808, 550): Some("访达") - Some("下载") (821x776 at 449,78) layer=0
[2025-07-31 23:57:01.744][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550)
[2025-07-31 23:57:01.744][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '下载' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.744][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 14 contains point (808, 550): Some("备忘录") - Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:01.745][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550)
[2025-07-31 23:57:01.745][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '备忘录' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.745][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 16 contains point (808, 550): Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (437x623 at 590,133) layer=0
[2025-07-31 23:57:01.745][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550)
[2025-07-31 23:57:01.746][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'mxm_pyenv — -zsh — 61×42' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.746][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 17 contains point (808, 550): Some("访达") - Some("") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.746][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550)
[2025-07-31 23:57:01.746][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.746][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 18 contains point (808, 550): Some("程序坞") - Some("Wallpaper-") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.746][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550)
[2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'Wallpaper-' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (808, 550), Z-order analysis:
[2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST)
[2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心
[2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心)
[2025-07-31 23:57:01.748][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (808, 550): Notification Center (通知中心) layer=23 in 75ms
[2025-07-31 23:57:01.748][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected
[2025-07-31 23:57:01.866][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (808, 552)
[2025-07-31 23:57:01.866][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder)
[2025-07-31 23:57:01.866][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (808, 552)
[2025-07-31 23:57:01.868][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows
[2025-07-31 23:57:01.868][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system
[2025-07-31 23:57:01.870][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system
[2025-07-31 23:57:01.870][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25
[2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999
[2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0
[2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small
[2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25
[2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25
[2025-07-31 23:57:01.872][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24
[2025-07-31 23:57:01.873][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25
[2025-07-31 23:57:01.874][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25
[2025-07-31 23:57:01.875][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24
[2025-07-31 23:57:01.875][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24
[2025-07-31 23:57:01.876][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25
[2025-07-31 23:57:01.876][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25
[2025-07-31 23:57:01.876][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24
[2025-07-31 23:57:01.878][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24
[2025-07-31 23:57:01.879][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25
[2025-07-31 23:57:01.879][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25
[2025-07-31 23:57:01.881][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24
[2025-07-31 23:57:01.883][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24
[2025-07-31 23:57:01.883][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25
[2025-07-31 23:57:01.883][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25
[2025-07-31 23:57:01.884][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24
[2025-07-31 23:57:01.884][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24
[2025-07-31 23:57:01.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25
[2025-07-31 23:57:01.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25
[2025-07-31 23:57:01.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24
[2025-07-31 23:57:01.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24
[2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25
[2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25
[2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24
[2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24
[2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25
[2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25
[2025-07-31 23:57:01.887][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24
[2025-07-31 23:57:01.887][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24
[2025-07-31 23:57:01.887][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25
[2025-07-31 23:57:01.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25
[2025-07-31 23:57:01.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24
[2025-07-31 23:57:01.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24
[2025-07-31 23:57:01.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25
[2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25
[2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24
[2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer
[2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25
[2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25
[2025-07-31 23:57:01.890][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24
[2025-07-31 23:57:01.890][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24
[2025-07-31 23:57:01.890][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25
[2025-07-31 23:57:01.891][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25
[2025-07-31 23:57:01.891][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24
[2025-07-31 23:57:01.891][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24
[2025-07-31 23:57:01.892][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25
[2025-07-31 23:57:01.892][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24
[2025-07-31 23:57:01.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24
[2025-07-31 23:57:01.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:01.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25
[2025-07-31 23:57:01.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:01.894][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050
[2025-07-31 23:57:01.894][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050
[2025-07-31 23:57:01.894][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25
[2025-07-31 23:57:01.895][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5
[2025-07-31 23:57:01.895][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050
[2025-07-31 23:57:01.896][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25
[2025-07-31 23:57:01.896][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.897][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025
[2025-07-31 23:57:01.897][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025
[2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25
[2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025
[2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025
[2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25
[2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0
[2025-07-31 23:57:01.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776
[2025-07-31 23:57:01.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776
[2025-07-31 23:57:01.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25
[2025-07-31 23:57:01.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:01.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805
[2025-07-31 23:57:01.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805
[2025-07-31 23:57:01.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25
[2025-07-31 23:57:01.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0
[2025-07-31 23:57:01.901][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651
[2025-07-31 23:57:01.901][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651
[2025-07-31 23:57:01.901][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25
[2025-07-31 23:57:01.902][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0
[2025-07-31 23:57:01.902][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623
[2025-07-31 23:57:01.902][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623
[2025-07-31 23:57:01.903][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25
[2025-07-31 23:57:01.903][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.903][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050
[2025-07-31 23:57:01.904][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050
[2025-07-31 23:57:01.905][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25
[2025-07-31 23:57:01.905][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.906][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050
[2025-07-31 23:57:01.906][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050
[2025-07-31 23:57:01.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25
[2025-07-31 23:57:01.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050
[2025-07-31 23:57:01.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:01.908][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows
[2025-07-31 23:57:01.911][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order
[2025-07-31 23:57:01.916][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.917][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.919][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.921][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.925][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25)
[2025-07-31 23:57:01.926][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25)
[2025-07-31 23:57:01.926][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25)
[2025-07-31 23:57:01.927][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25)
[2025-07-31 23:57:01.928][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25)
[2025-07-31 23:57:01.929][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25)
[2025-07-31 23:57:01.929][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23)
[2025-07-31 23:57:01.930][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0)
[2025-07-31 23:57:01.931][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0)
[2025-07-31 23:57:01.932][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0)
[2025-07-31 23:57:01.933][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0)
[2025-07-31 23:57:01.933][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0)
[2025-07-31 23:57:01.934][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0)
[2025-07-31 23:57:01.934][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0)
[2025-07-31 23:57:01.935][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0)
[2025-07-31 23:57:01.935][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Retrieved 19 windows from system
[2025-07-31 23:57:01.938][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 10 contains point (808, 552): Some("通知中心") - Some("Notification Center") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:01.938][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (808, 552)
[2025-07-31 23:57:01.938][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 11 contains point (808, 552): Some("Code") - Some("lib.rs — Mecap") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.939][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552)
[2025-07-31 23:57:01.939][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'lib.rs — Mecap' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.939][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 12 contains point (808, 552): Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:01.939][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552)
[2025-07-31 23:57:01.939][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.940][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 13 contains point (808, 552): Some("访达") - Some("下载") (821x776 at 449,78) layer=0
[2025-07-31 23:57:01.941][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552)
[2025-07-31 23:57:01.942][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '下载' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.943][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 14 contains point (808, 552): Some("备忘录") - Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:01.944][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552)
[2025-07-31 23:57:01.946][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '备忘录' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.947][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 16 contains point (808, 552): Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (437x623 at 590,133) layer=0
[2025-07-31 23:57:01.948][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552)
[2025-07-31 23:57:01.948][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'mxm_pyenv — -zsh — 61×42' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.948][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 17 contains point (808, 552): Some("访达") - Some("") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.949][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552)
[2025-07-31 23:57:01.949][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.950][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 18 contains point (808, 552): Some("程序坞") - Some("Wallpaper-") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:01.950][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552)
[2025-07-31 23:57:01.950][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'Wallpaper-' contains point but is obscured by higher layer windows
[2025-07-31 23:57:01.950][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (808, 552), Z-order analysis:
[2025-07-31 23:57:01.951][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST)
[2025-07-31 23:57:01.951][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心
[2025-07-31 23:57:01.952][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心)
[2025-07-31 23:57:01.952][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (808, 552): Notification Center (通知中心) layer=23 in 83ms
[2025-07-31 23:57:01.953][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected
[2025-07-31 23:57:01.994][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (801, 573)
[2025-07-31 23:57:01.994][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder)
[2025-07-31 23:57:01.994][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (801, 573)
[2025-07-31 23:57:01.996][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows
[2025-07-31 23:57:01.996][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system
[2025-07-31 23:57:01.997][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system
[2025-07-31 23:57:01.998][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25
[2025-07-31 23:57:01.998][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999
[2025-07-31 23:57:01.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0
[2025-07-31 23:57:01.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small
[2025-07-31 23:57:01.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25
[2025-07-31 23:57:02.000][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25
[2025-07-31 23:57:02.000][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24
[2025-07-31 23:57:02.001][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25
[2025-07-31 23:57:02.001][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25
[2025-07-31 23:57:02.001][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24
[2025-07-31 23:57:02.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24
[2025-07-31 23:57:02.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25
[2025-07-31 23:57:02.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25
[2025-07-31 23:57:02.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24
[2025-07-31 23:57:02.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24
[2025-07-31 23:57:02.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25
[2025-07-31 23:57:02.004][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25
[2025-07-31 23:57:02.005][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24
[2025-07-31 23:57:02.007][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24
[2025-07-31 23:57:02.007][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25
[2025-07-31 23:57:02.007][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25
[2025-07-31 23:57:02.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24
[2025-07-31 23:57:02.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24
[2025-07-31 23:57:02.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25
[2025-07-31 23:57:02.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25
[2025-07-31 23:57:02.009][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24
[2025-07-31 23:57:02.009][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24
[2025-07-31 23:57:02.009][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25
[2025-07-31 23:57:02.010][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25
[2025-07-31 23:57:02.010][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24
[2025-07-31 23:57:02.010][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24
[2025-07-31 23:57:02.011][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25
[2025-07-31 23:57:02.011][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25
[2025-07-31 23:57:02.011][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24
[2025-07-31 23:57:02.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24
[2025-07-31 23:57:02.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25
[2025-07-31 23:57:02.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25
[2025-07-31 23:57:02.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24
[2025-07-31 23:57:02.013][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24
[2025-07-31 23:57:02.013][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25
[2025-07-31 23:57:02.014][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25
[2025-07-31 23:57:02.014][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24
[2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer
[2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25
[2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25
[2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24
[2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24
[2025-07-31 23:57:02.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25
[2025-07-31 23:57:02.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25
[2025-07-31 23:57:02.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24
[2025-07-31 23:57:02.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24
[2025-07-31 23:57:02.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25
[2025-07-31 23:57:02.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24
[2025-07-31 23:57:02.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24
[2025-07-31 23:57:02.018][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:02.018][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25
[2025-07-31 23:57:02.019][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:02.019][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050
[2025-07-31 23:57:02.019][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050
[2025-07-31 23:57:02.020][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25
[2025-07-31 23:57:02.021][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5
[2025-07-31 23:57:02.021][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050
[2025-07-31 23:57:02.022][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25
[2025-07-31 23:57:02.022][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:02.022][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025
[2025-07-31 23:57:02.023][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025
[2025-07-31 23:57:02.023][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25
[2025-07-31 23:57:02.023][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:02.024][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025
[2025-07-31 23:57:02.024][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025
[2025-07-31 23:57:02.025][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25
[2025-07-31 23:57:02.025][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0
[2025-07-31 23:57:02.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776
[2025-07-31 23:57:02.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776
[2025-07-31 23:57:02.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25
[2025-07-31 23:57:02.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:02.027][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805
[2025-07-31 23:57:02.027][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805
[2025-07-31 23:57:02.028][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25
[2025-07-31 23:57:02.029][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0
[2025-07-31 23:57:02.029][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651
[2025-07-31 23:57:02.029][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651
[2025-07-31 23:57:02.030][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25
[2025-07-31 23:57:02.030][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0
[2025-07-31 23:57:02.030][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623
[2025-07-31 23:57:02.030][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623
[2025-07-31 23:57:02.031][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25
[2025-07-31 23:57:02.031][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:02.031][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050
[2025-07-31 23:57:02.031][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050
[2025-07-31 23:57:02.032][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25
[2025-07-31 23:57:02.032][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:02.032][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050
[2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050
[2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25
[2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050
[2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server
[2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows
[2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order
[2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25)
[2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25)
[2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25)
[2025-07-31 23:57:02.035][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25)
[2025-07-31 23:57:02.035][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25)
[2025-07-31 23:57:02.035][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25)
[2025-07-31 23:57:02.035][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25)
[2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25)
[2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25)
[2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25)
[2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23)
[2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0)
[2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0)
[2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0)
[2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0)
[2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0)
[2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0)
[2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0)
[2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0)
[2025-07-31 23:57:02.038][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Retrieved 19 windows from system
[2025-07-31 23:57:02.038][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 10 contains point (801, 573): Some("通知中心") - Some("Notification Center") (1680x1050 at 0,0) layer=23
[2025-07-31 23:57:02.038][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (801, 573)
[2025-07-31 23:57:02.038][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 11 contains point (801, 573): Some("Code") - Some("lib.rs — Mecap") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:02.038][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573)
[2025-07-31 23:57:02.038][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'lib.rs — Mecap' contains point but is obscured by higher layer windows
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 12 contains point (801, 573): Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (1680x1025 at 0,25) layer=0
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573)
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' contains point but is obscured by higher layer windows
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 13 contains point (801, 573): Some("访达") - Some("下载") (821x776 at 449,78) layer=0
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573)
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '下载' contains point but is obscured by higher layer windows
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 14 contains point (801, 573): Some("备忘录") - Some("备忘录") (1212x805 at 378,126) layer=0
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573)
[2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '备忘录' contains point but is obscured by higher layer windows
[2025-07-31 23:57:02.040][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 16 contains point (801, 573): Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (437x623 at 590,133) layer=0
[2025-07-31 23:57:02.040][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573)
[2025-07-31 23:57:02.040][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'mxm_pyenv — -zsh — 61×42' contains point but is obscured by higher layer windows
[2025-07-31 23:57:02.040][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 17 contains point (801, 573): Some("访达") - Some("") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:02.040][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573)
[2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window '' contains point but is obscured by higher layer windows
[2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 18 contains point (801, 573): Some("程序坞") - Some("Wallpaper-") (1680x1050 at 0,0) layer=0
[2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573)
[2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][DEBUG] [MACOS] Window 'Wallpaper-' contains point but is obscured by higher layer windows
[2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (801, 573), Z-order analysis:
[2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST)
[2025-07-31 23:57:02.042][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心
[2025-07-31 23:57:02.042][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心)
[2025-07-31 23:57:02.042][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (801, 573): Notification Center (通知中心) layer=23 in 47ms
[2025-07-31 23:57:02.043][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected
[WINDOW] Cache expired, fetching fresh window list
[2025-07-31 23:57:03.575][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Returning cached window list (21 windows)
[2025-07-31 23:57:05.357][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Cache expired, fetching fresh window list
[2025-07-31 23:57:07.555][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Returning cached window list (21 windows)
[2025-07-31 23:57:09.358][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Cache expired, fetching fresh window list
[2025-07-31 23:57:11.547][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Returning cached window list (21 windows)
[2025-07-31 23:57:13.359][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Cache expired, fetching fresh window list
[2025-07-31 23:57:15.519][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Returning cached window list (21 windows)
[2025-07-31 23:57:17.360][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Cache expired, fetching fresh window list
[2025-07-31 23:57:19.536][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Returning cached window list (21 windows)
[2025-07-31 23:57:21.361][webview][INFO] [FRONTEND] Loaded windows: 21
[WINDOW] Cache expired, fetching fresh window list
[2025-07-31 23:57:23.503][webview][INFO] [FRONTEND] Loaded windows: 21
[GLOBAL-SHORTCUT] 🎯 ESC key pressed globally - triggering exit
[UX] 🚪 Direct exit capture functionality triggered
[UX] 🚪 Step 1: Hiding all overlays
[OVERLAY] 🔧 Closing all overlays (real-time creation mode)
[OVERLAY] 🔧 Closing overlay: window_highlight_1753977389090
[OVERLAY] 🔧 Closed 1 overlays
[UX] 🚪 Successfully hidden 1 overlays
[UX] 🚪 Step 1.5: Closing all active toolbar windows
[2025-07-31 23:57:23.520][tao::platform_impl::platform::window][TRACE] Dropping `UnownedWindow` (0x7f8b4ddb0b30)
[2025-07-31 23:57:23.520][mecap_lib::modules::independent_toolbar][INFO] [TOOLBAR] 🗑️ Closing all active toolbar windows
[2025-07-31 23:57:23.520][tao::platform_impl::platform::window_delegate][TRACE] Triggered `windowWillClose:`
[2025-07-31 23:57:23.521][mecap_lib::modules::independent_toolbar][INFO] [TOOLBAR] 🗑️ Cleared all toolbar tracking information
[2025-07-31 23:57:23.521][mecap_lib::modules::independent_toolbar][INFO] [TOOLBAR] ✅ Closed 0 toolbar windows
[2025-07-31 23:57:23.521][tao::platform_impl::platform::window_delegate][TRACE] Completed `windowWillClose:`
[UX] 🚪 Successfully closed 0 toolbar windows
[UX] 🚪 Step 1.6: Closing all preview windows
[2025-07-31 23:57:23.522][mecap_lib::modules::ux][INFO] [UX] ✅ Closed 0 preview windows
[UX] 🚪 Successfully closed 0 preview windows
[UX] 🚪 Step 2: Stopping window detection
[WINDOW] Smart window detection stop requested
[WINDOW] Smart window detection flag set to false
[UX] 🚪 Step 3: Resetting all UX states
[UX] 🚪 Cleared all interaction modes
[UX] 🚪 Step 4: Pre-created editor cleanup skipped (using file-based approach)
[UX] 🚪 Step 5: Completing capture session
[STATE] Capture session completed
[UX] 🚪 Step 6: Unregistering global ESC shortcut
[GLOBAL-SHORTCUT] 🎯 Unregistering global ESC shortcut
[GLOBAL-SHORTCUT] ✅ Global ESC shortcut unregistered successfully
[UX] 🚪 Step 7: Showing main window
[UX] 🚪 Direct exit capture functionality completed successfully
[GLOBAL-SHORTCUT] ✅ Successfully exited capture via global shortcut
[2025-07-31 23:57:23.559][tao::platform_impl::platform::window_delegate][TRACE] Triggered `windowDidBecomeKey:`
[2025-07-31 23:57:23.559][tao::platform_impl::platform::window_delegate][TRACE] Completed `windowDidBecomeKey:`
[WINDOW] Smart window detection stopped
[2025-07-31 23:57:25.734][tao::platform_impl::platform::window_delegate][TRACE] Triggered `windowDidResignKey:`
[2025-07-31 23:57:25.734][tao::platform_impl::platform::window_delegate][TRACE] Completed `windowDidResignKey:`

