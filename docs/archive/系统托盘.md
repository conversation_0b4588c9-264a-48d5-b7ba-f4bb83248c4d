The tray API is available in both JavaScript and Rust.

Create a Tray Icon
JavaScript
Rust
use tauri::tray::TrayIconBuilder;

tauri::Builder::default()
    .setup(|app| {
        let tray = TrayIconBuilder::new().build(app)?;
        Ok(())
    })

See TrayIconBuilder for more information on customization options.

Change the Tray Icon

let tray = TrayIconBuilder::new()
  .icon(app.default_window_icon().unwrap().clone())
  .build(app)?;

# Add a Menu
To attach a menu that is displayed when the tray is clicked, you can use the menu option.

Note

By default the menu is displayed on both left and right clicks.

To prevent the menu from popping up on left click, call the menu_on_left_click(false) Rust function or set the menuOnLeftClick JavaScript option to false.

use tauri::{
  menu::{Menu, MenuItem},
  tray::TrayIconBuilder,
};

let quit_i = MenuItem::with_id(app, "quit", "Quit", true, None::<&str>)?;
let menu = Menu::with_items(app, &[&quit_i])?;

let tray = TrayIconBuilder::new()
  .menu(&menu)
  .menu_on_left_click(true)
  .build(app)?;


# Listen to Menu Events
Use the TrayIconBuilder::on_menu_event method to attach a tray menu click event listener:


use tauri::tray::TrayIconBuilder;

TrayIconBuilder::new()
  .on_menu_event(|app, event| match event.id.as_ref() {
    "quit" => {
      println!("quit menu item was clicked");
      app.exit(0);
    }
    _ => {
      println!("menu item {:?} not handled", event.id);
    }
  })



# listen to events
The tray icon emits events for the following mouse events:

click: triggered when the cursor receives a single left, right or middle click, including information on whether the mouse press was released or not
Double click: triggered when the cursor receives a double left, right or middle click
Enter: triggered when the cursor enters the tray icon area
Move: triggered when the cursor moves around the tray icon area
Leave: triggered when the cursor leaves the tray icon area

Rust
use tauri::{
    Manager,
    tray::{MouseButton, MouseButtonState, TrayIconBuilder, TrayIconEvent}
};

TrayIconBuilder::new()
  .on_tray_icon_event(|tray, event| match event {
    TrayIconEvent::Click {
      button: MouseButton::Left,
      button_state: MouseButtonState::Up,
      ..
    } => {
      println!("left click pressed and released");
      // in this example, let's show and focus the main window when the tray is clicked
      let app = tray.app_handle();
      if let Some(window) = app.get_webview_window("main") {
        let _ = window.show();
        let _ = window.set_focus();
      }
    }
    _ => {
      println!("unhandled event {event:?}");
    }
  })
