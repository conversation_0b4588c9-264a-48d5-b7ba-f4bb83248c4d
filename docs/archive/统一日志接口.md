
 日志文件已经正常创建并保存在正确位置，这个日志会在程序重启后重写，而不是追加：
📁 macOS开发模式路径：~/Library/Logs/com.mecap.app/mecap.log

# 查看日志文件
ls -la ~/Library/Logs/com.mecap.app/

# 实时监控日志
tail -f ~/Library/Logs/com.mecap.app/mecap.log

'~/' 替换成本地用户就是：
/Users/<USER>/Library/Logs/com.mecap.app/mecap.log

## public/overlay-window-highlight.html 里这个日志接口是有效的，可以成功和后端日志一起输出到终端
        // 🔧 LOG REFACTOR: 统一日志系统，使用Tauri log插件
        let logAPI = null;
        let logInitialized = false;

        // 初始化日志API
        async function initializeLogging() {
            try {
                if (typeof window.__TAURI__ !== 'undefined' && window.__TAURI__.log) {
                    logAPI = window.__TAURI__.log;
                    // 附加控制台输出
                    await logAPI.attachConsole();
                    logInitialized = true;
                    log.info('[FRONTEND] 🔧 Tauri log plugin initialized successfully');
                } else {
                    console.warn('[FRONTEND] ⚠️ Tauri log plugin not available, falling back to console');
                    logInitialized = true; // 标记为已初始化，使用控制台fallback
                }
            } catch (e) {
                console.warn('[FRONTEND] ⚠️ Failed to initialize Tauri log plugin:', e);
                logInitialized = true; // 标记为已初始化，使用控制台fallback
            }
        }


        // 统一日志接口
        const log = {
            trace: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.trace(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-TRACE] ${fullMessage}`);
                }
            },
            debug: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.debug(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-DEBUG] ${fullMessage}`);
                }
            },
            info: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.info(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-INFO] ${fullMessage}`);
                }
            },
            warn: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.warn(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.warn(`[FRONTEND-WARN] ${fullMessage}`);
                }
            },
            error: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.error(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.error(`[FRONTEND-ERROR] ${fullMessage}`);
                }
            }
        };
