在 Tauri 中，无边框窗口（设置 `decorations: false`）默认无法通过标题栏拖动，但可以通过以下方式实现窗体内长按拖拽位置：

1. **配置无边框窗口**：在 `tauri.conf.json` 中，设置窗口的 `decorations: false` 以去除窗口边框和标题栏 。
2. **添加拖拽区域**：在需要可拖动的 HTML 元素上添加 `data-tauri-drag-region` 属性，例如：
   ```html
   <div data-tauri-drag-region class="titlebar">可拖动区域</div>
   ```
   或者通过 JavaScript 手动实现拖拽功能：
   ```javascript
   document.getElementById('titlebar')?.addEventListener('mousedown', (e) => {
     if (e.buttons === 1) {
       appWindow.startDragging(); // 开始拖拽窗口
     }
   });
   ```
   。

注意，拖拽区域不应影响其他交互功能，且确保配置文件中启用了相关窗口权限（如 `"window": {"all": true}`） 。
