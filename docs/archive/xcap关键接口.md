根据你的请求，我将为你提供有关 Rust `xcap` 库对外提供的接口信息。`xcap` 是一个跨平台的屏幕捕获库，支持 Linux（X11、Wayland）、MacOS 和 Windows，主要用于截图和视频录制（视频录制功能仍在开发中）。以下内容基于 `xcap` 库的官方文档和相关代码示例（如 GitHub 和 Docs.rs）。[](https://github.com/nashaofu/xcap)[](https://docs.rs/crate/xcap/latest)[](https://docs.rs/xcap/latest/xcap/struct.Window.html)

### `xcap` 库对外提供的核心接口
`xcap` 库主要通过两个核心结构体 `Monitor` 和 `Window` 提供屏幕捕获和窗口捕获功能。以下是对外暴露的主要接口的详细说明：

#### 1. **Monitor 结构体**
`Monitor` 结构体用于操作显示器（屏幕），提供屏幕截图和视频录制相关功能。

- **方法**：
  - `Monitor::all() -> Result<Vec<Monitor>, Box<dyn std::error::Error>>`
    - **作用**：获取系统中所有显示器的列表。
    - **返回值**：返回一个 `Result`，包含所有 `Monitor` 实例的向量，或错误。
    - **示例**：
      ```rust
      let monitors = Monitor::all()?;
      for monitor in monitors {
          println!("Monitor: {:?}", monitor.name()?);
      }
      ```
  - `Monitor::from_point(x: i32, y: i32) -> Result<Monitor, Box<dyn std::error::Error>>`
    - **作用**：根据屏幕坐标 `(x, y)` 获取包含该点的显示器。
    - **参数**：
      - `x`: 屏幕坐标的 x 值。
      - `y`: 屏幕坐标的 y 值。
    - **返回值**：返回对应的 `Monitor` 实例或错误。
    - **示例**：
      ```rust
      let monitor = Monitor::from_point(100, 100)?;
      ```
  - `Monitor::is_primary() -> Result<bool, Box<dyn std::error::Error>>`
    - **作用**：检查当前显示器是否为主显示器。
    - **返回值**：返回 `true`（主显示器）或 `false`，或错误。
  - `Monitor::name() -> Result<String, Box<dyn std::error::Error>>`
    - **作用**：获取显示器的名称。
    - **返回值**：返回显示器名称的字符串，或错误。
  - `Monitor::width() -> Result<u32, Box<dyn std::error::Error>>`
    - **作用**：获取显示器的宽度（像素）。
    - **返回值**：返回宽度值，或错误。
  - `Monitor::height() -> Result<u32, Box<dyn std::error::Error>>`
    - **作用**：获取显示器的高度（像素）。
    - **返回值**：返回高度值，或错误。
  - `Monitor::capture_region(x: i32, y: i32, width: u32, height: u32) -> Result<Image, Box<dyn std::error::Error>>`
    - **作用**：捕获指定显示器上某个区域的截图。
    - **参数**：
      - `x`: 区域起始 x 坐标。
      - `y`: 区域起始 y 坐标。
      - `width`: 区域宽度。
      - `height`: 区域高度。
    - **返回值**：返回 `Image`（图像对象），或错误。
    - **示例**：
      ```rust
      let image = monitor.capture_region(100, 100, 400, 300)?;
      image.save("region.png")?;
      ```
  - `Monitor::capture_image() -> Result<Image, Box<dyn std::error::Error>>`
    - **作用**：捕获整个显示器的截图。
    - **返回值**：返回 `Image`（整个屏幕的图像），或错误。
    - **示例**：
      ```rust
      let image = monitor.capture_image()?;
      image.save("screen.png")?;
      ```
  - `Monitor::video_recorder() -> Result<(VideoRecorder, Receiver<Frame>), Box<dyn std::error::Error>>`
    - **作用**：创建视频录制器，用于录制显示器内容（WIP，仍在开发中）。
    - **返回值**：返回一个元组，包含 `VideoRecorder`（录制器）和 `Receiver<Frame>`（帧接收器），或错误。
    - **示例**：
      ```rust
      let (video_recorder, sx) = monitor.video_recorder()?;
      thread::spawn(move || {
          while let Ok(frame) = sx.recv() {
              println!("Frame width: {:?}", frame.width);
          }
      });
      video_recorder.start()?;
      thread::sleep(Duration::from_secs(2));
      video_recorder.stop()?;
      ```

#### 2. **Window 结构体**
`Window` 结构体用于操作窗口，提供窗口截图功能。

- **方法**：
  - `Window::all() -> Result<Vec<Window>, Box<dyn std::error::Error>>`
    - **作用**：获取系统中所有窗口的列表。
    - **返回值**：返回一个 `Result`，包含所有 `Window` 实例的向量，或错误。
    - **示例**：
      ```rust
      let windows = Window::all()?;
      for window in windows {
          println!("Window: {:?}", window.title()?);
      }
      ```
  - `Window::title() -> Result<String, Box<dyn std::error::Error>>`
    - **作用**：获取窗口的标题。
    - **返回值**：返回窗口标题的字符串，或错误。
  - `Window::x() -> Result<i32, Box<dyn std::error::Error>>`
    - **作用**：获取窗口的 x 坐标。
    - **返回值**：返回 x 坐标，或错误。
  - `Window::y() -> Result<i32, Box<dyn std::error::Error>>`
    - **作用**：获取窗口的 y 坐标。
    - **返回值**：返回 y 坐标，或错误。
  - `Window::width() -> Result<u32, Box<dyn std::error::Error>>`
    - **作用**：获取窗口的宽度（像素）。
    - **返回值**：返回宽度值，或错误。
  - `Window::height() -> Result<u32, Box<dyn std::error::Error>>`
    - **作用**：获取窗口的高度（像素）。
    - **返回值**：返回高度值，或错误。
  - `Window::is_minimized() -> Result<bool, Box<dyn std::error::Error>>`
    - **作用**：检查窗口是否被最小化。
    - **返回值**：返回 `true`（最小化）或 `false`，或错误。
  - `Window::is_maximized() -> Result<bool, Box<dyn std::error::Error>>`
    - **作用**：检查窗口是否被最大化。
    - **返回值**：返回 `true`（最大化）或 `false`，或错误。
  - `Window::capture_image() -> Result<Image, Box<dyn std::error::Error>>`
    - **作用**：捕获指定窗口的截图。
    - **返回值**：返回 `Image`（窗口的图像），或错误。
    - **示例**：
      ```rust
      let windows = Window::all()?;
      for window in windows {
          if !window.is_minimized()? {
              let image = window.capture_image()?;
              image.save(format!("window-{}.png", window.title()?))?;
          }
      }
      ```

#### 3. **Image 结构体**
`Image` 结构体表示捕获的图像，提供保存和获取图像信息的功能。

- **方法**：
  - `Image::save<P: AsRef<Path>>(path: P) -> Result<(), Box<dyn std::error::Error>>`
    - **作用**：将图像保存到指定路径（支持 PNG 格式）。
    - **参数**：
      - `path`: 文件路径。
    - **返回值**：成功返回 `()`，失败返回错误。
  - `Image::width() -> u32`
    - **作用**：获取图像的宽度（像素）。
    - **返回值**：返回宽度值。
  - `Image::height() -> u32`
    - **作用**：获取图像的高度（像素）。
    - **返回值**：返回高度值。

#### 4. **VideoRecorder 结构体（WIP）**
`VideoRecorder` 用于录制视频，目前为实验性功能（Work in Progress）。

- **方法**：
  - `VideoRecorder::start() -> Result<(), Box<dyn std::error::Error>>`
    - **作用**：开始录制视频。
    - **返回值**：成功返回 `()`，失败返回错误。
  - `VideoRecorder::stop() -> Result<(), Box<dyn std::error::Error>>`
    - **作用**：停止录制视频。
    - **返回值**：成功返回 `()`，失败返回错误。

#### 5. **Frame 结构体（WIP）**
`Frame` 表示视频录制中的一帧，配合 `VideoRecorder` 使用。

- **方法**：
  - `Frame::width() -> u32`
    - **作用**：获取帧的宽度（像素）。
    - **返回值**：返回宽度值。

### 依赖和平台支持
- **依赖**（来自 `Cargo.toml`）：
  - 跨平台依赖：`image`（用于图像处理）、`log`、`scopeguard`、`thiserror`。
  - macOS 特定依赖：`objc2`、`dispatch2` 等，用于调用 AppKit 和 AVFoundation。
  - Windows 特定依赖：`windows`（包括 `Win32_Graphics_Gdi`、`Win32_Devices_Display` 等）。
  - Linux 特定依赖：`dbus`、`xcb`（X11）、`libwayshot`（Wayland）。
- **平台支持**：
  - Linux（X11 和 Wayland）
  - macOS
  - Windows
- **注意**：在 Linux 上编译需要安装相关依赖（如 `libxcb-randr`），具体见文档。[](https://github.com/nashaofu/xcap/blob/master/Cargo.toml)

### 使用注意事项
- **最小化窗口**：`Window::capture_image` 无法捕获最小化的窗口，需通过 `is_minimized` 检查。
- **视频录制**：视频录制功能仍在开发中（WIP），可能不稳定，建议谨慎使用。
- **错误处理**：所有方法返回 `Result`，需要妥善处理错误。
- **性能**：捕获大分辨率屏幕或频繁录制视频可能影响性能，建议优化使用场景。

### 示例代码
以下是一个综合示例，展示如何使用 `Monitor` 和 `Window` 接口捕获屏幕和窗口：
```rust
use xcap::{Monitor, Window};
use std::fs;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 捕获所有显示器
    let monitors = Monitor::all()?;
    fs::create_dir_all("screenshots")?;
    for monitor in monitors {
        let image = monitor.capture_image()?;
        image.save(format!("screenshots/monitor-{}.png", monitor.name()?))?;
    }

    // 捕获所有窗口
    let windows = Window::all()?;
    for (i, window) in windows.iter().enumerate() {
        if !window.is_minimized()? {
            let image = window.capture_image()?;
            image.save(format!("screenshots/window-{}-{}.png", i, window.title()?))?;
        }
    }

    Ok(())
}
```

### 参考资料
- 官方文档：https://docs.rs/xcap[](https://docs.rs/xcap/latest/xcap/)
- GitHub 仓库：https://github.com/nashaofu/xcap[](https://github.com/nashaofu/xcap)
- Crates.io：https://crates.io/crates/xcap[](https://docs.rs/crate/xcap/latest/source/)
