# 关键技术

## 窗口截图流程
### 用户操作流程
1. 用户选择'Capture Screen'：系统进入窗口检测模式，鼠标下面的顶层窗口被高亮，用户按ESC键退出窗口检测模式
2. 用户左键点击选中高亮窗口：系统进行截图并保存图片，然后创建预览窗口展示截图图片，预览窗口默认顶置，预览窗口边上有快捷工具栏，工具栏也顶置且位于预览窗口的上边
3. 预览状态下的用户选择：
  - 用户按'ESC'键：预览窗口退出，工具栏退出
  - 用户按回车：预览窗口退出，图片拷贝到剪贴板，提示用户图片已保存到剪贴板
  - 用户选择工具栏中具有编辑功能的按钮：预览窗口进入编辑状态，工具栏中具有编辑功能的按钮高亮，工具栏的’保存/退出‘按钮高亮
4. 编辑状态下用户选择：
  - 用户选择工具栏中具有编辑功能的按钮：可对图片进行编辑
  - 用户完成编辑点保存或回车后保存编辑后的图片，打上编辑的tag，预览窗口由编辑状态回到预览状态

### 软件流程应该是：
用户触发截图流程 -> 进入窗口检查模式 -> 鼠标移动时检测高亮顶层窗口 -> 用户点击高亮窗口时触发窗口截图 -> 截图保存到文件夹 -> 创建预览窗口并设置为顶置 -> 预览窗口边上显示快捷工具栏并且工具栏顶置 -> 预览窗口可以拖拽

### 关键技术
1. 窗口检测：
  - 需要一个overlay层，把鼠标当前坐标传递给后端，后端找到top层窗口，然后前端根据top窗口的尺寸和位置进行高亮
  - 需要缓存高亮窗口去重，当鼠标移动后端返回的top窗口是同一个窗口时不需要反复绘制高亮
2. 截图预览：
  - 效果：无边框、顶置、可拖拽、显示图片
  - 支持快捷标注：
    > 截图展示	<img> + <canvas>	将截图绘制到 canvas 上
    > 标注绘制	支持图形、文字、自由画笔对截图进行标注、支持撤销、支持保存等

## 截图标注流程
### 用户操作流程
1. 用户点击高亮窗口：系统进行截图保存图片，并在预览窗口展示，预览窗口默认顶置，窗口边上有快捷工具栏
2. 用户在快捷工具栏点击标注类型工具：预览窗口进入编辑状态，预览窗口中显示对应的截图，预览窗口边框变为虚线
3. 用户鼠标移动到编辑窗口进行标注操作：
4. 用户可以在工具栏切换新的标注类型：由于编辑窗口已经存在，不需要触发预览变编辑，用户只要挪动鼠标到编辑窗口即可进行新的标注
5. 完成所有标注后，用户在快捷工具栏点击保存或回车：编辑后的图片保存到文件夹，预览窗口回到预览状态，预览窗口中的图片是标注后的图片

### 统一的截图流程
启动截图 - 从托盘菜单或快捷键启动
选择模式 - 窗口检测模式自动切换到区域选择
区域选择 - 拖拽选择截图区域
自动预览 - 截图后重用screenshot-preview窗口和工具栏
标注编辑 - 使用完整的标注工具集
保存分享 - 保存标注后的图片
