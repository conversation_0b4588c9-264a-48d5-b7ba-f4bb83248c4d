[Log] [FRONTEND] 🔧   - overlay-container pointer-events: – "auto" (overlay-window-highlight.html, line 3766)
[Log] [FRONTEND] 🔧   - event-capture-layer pointer-events: – "auto" (overlay-window-highlight.html, line 3767)
[Log] [FRONTEND] ✅ All pointer-events settings are correct! (overlay-window-highlight.html, line 3785)
[Log] [FRONTEND-INFO] 🔧 EMERGENCY: Original event listeners re-enabled for window detection and region selection (overlay-window-highlight.html, line 550)
[Log] [FRONTEND] 🔧 Testing cursor events availability... (overlay-window-highlight.html, line 3812)
[Log] [FRONTEND] 🎯 Event listeners setup completed on capture layer (overlay-window-highlight.html, line 3849)
[Log] [FRONTEND] Event listeners setup completed (overlay-window-highlight.html, line 3901)
[Log] [FRONTEND] 🔍 Checking overlay initialization state... (overlay-window-highlight.html, line 3904)
[Log] [FRONTEND] 🔍 document.hidden: – false (overlay-window-highlight.html, line 3905)
[Log] [FRONTEND] 🔍 document.hasFocus(): – true (overlay-window-highlight.html, line 3906)
[Log] [FRONTEND] 🔍 document.visibilityState: – "visible" (overlay-window-highlight.html, line 3907)
[Log] [FRONTEND] 🎯 Overlay visible, starting immediate initialization... (overlay-window-highlight.html, line 3957)
[Log] [FRONTEND-INFO] 🚀 Initializing overlay after DOM ready (overlay-window-highlight.html, line 550)
[Log] [FRONTEND] 🎯 Window highlight overlay loaded (overlay-window-highlight.html, line 6331)
[Log] [FRONTEND] 🎯 Body background: – "rgba(0, 0, 0, 0)" (overlay-window-highlight.html, line 6332)
[Log] [FRONTEND] 🎯 HTML background: – "rgba(0, 0, 0, 0)" (overlay-window-highlight.html, line 6333)
[Log] [FRONTEND] 🎯 Document has focus: – true (overlay-window-highlight.html, line 6343)
[Log] [FRONTEND] 🎯 Active element: – "BODY" (overlay-window-highlight.html, line 6344)
[Log] [FRONTEND] 🎯 Tauri available: – true (overlay-window-highlight.html, line 6345)
[Log] [FRONTEND] 🎯 Testing keyboard event listener... (overlay-window-highlight.html, line 6355)
[Log] [FRONTEND] 🎯 Event listeners attached to document (overlay-window-highlight.html, line 6356)
[Log] [FRONTEND-INFO] 🧪 Test functions loaded. Test shortcuts: (overlay-window-highlight.html, line 550)
[Log] [FRONTEND-INFO] 🧪   Ctrl+Shift+T - Basic toolbar test (overlay-window-highlight.html, line 550)
[Log] [FRONTEND-INFO] 🧪   Ctrl+Shift+P - Positioning test (overlay-window-highlight.html, line 550)
[Log] [FRONTEND-INFO] 🧪   Ctrl+Shift+I - Interaction test (overlay-window-highlight.html, line 550)
[Log] [FRONTEND-INFO] 🧪   Ctrl+Shift+S - State transition test (overlay-window-highlight.html, line 550)
[Log] [FRONTEND-INFO] 🧪   Ctrl+Shift+U - UI consistency test (overlay-window-highlight.html, line 550)
[Log] [FRONTEND-INFO] 🧪   Ctrl+Shift+A - Complete test suite (overlay-window-highlight.html, line 550)
[Log] [FRONTEND-INFO] 🧪   Ctrl+Shift+B - Backend integration tests (overlay-window-highlight.html, line 550)
[Log] [FRONTEND-INFO] 🚀 Initializing overlay after DOM ready (overlay-window-highlight.html, line 550)
[Log] 🚀 setupEventListeners called (overlay-window-highlight.html, line 3049)
[Log] 🚀 About to call setupToolbarEventListeners (overlay-window-highlight.html, line 3057)
[Log] 🛠️ setupToolbarEventListeners called (overlay-window-highlight.html, line 2774)
[Log] 🛠️ Tauri API available, setting up event listeners (overlay-window-highlight.html, line 2776)
[Log] 🚀 setupEventListeners called (overlay-window-highlight.html, line 3049)
[Log] 🚀 About to call setupToolbarEventListeners (overlay-window-highlight.html, line 3057)
[Log] 🛠️ setupToolbarEventListeners called (overlay-window-highlight.html, line 2774)
[Log] 🛠️ Tauri API available, setting up event listeners (overlay-window-highlight.html, line 2776)
[Log] [LOG] Console redirection disabled to prevent infinite loops (overlay-window-highlight.html, line 3141)
[Log] [FRONTEND] 🔍 Checking macOS permissions... (overlay-window-highlight.html, line 6265)
[Log] [2025-07-31 23:56:29.483][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [FRONTEND] ✅ macOS screen recording permission granted (overlay-window-highlight.html, line 6272)
[Log] [FRONTEND-INFO] 🔧 Status update: API = Smart – "" (overlay-window-highlight.html, line 3191)
[Log] 🛠️ Notified preview window: – Object (toolbar-functions.js, line 221)
Object
[Log] 🛠️ Preview connection established (toolbar-functions.js, line 252)
[Log] 🛠️ Shared toolbar initialized: – Object (toolbar-functions.js, line 78)
Object
[Log] 🛠️ Notified preview window: – Object (toolbar-functions.js, line 221)
Object
[Log] 🛠️ Preview connection established (toolbar-functions.js, line 252)
[Log] 🛠️ Shared toolbar initialized: – Object (toolbar-functions.js, line 78)
Object
[Log] [OVERLAY] ✅ Semi-transparent mode with keyboard event support (alpha=0.01) (overlay-window-highlight.html, line 9)
[Log] [DIAGNOSTIC] Simple test - not interfering with HTML JavaScript (overlay-window-highlight.html, line 2)
[Log] [FRONTEND] 🎯 Please press ESC key to test... (overlay-window-highlight.html, line 6364)
[Log] [FRONTEND] 🎯 Current focus element: –  (overlay-window-highlight.html, line 6365)
<body style="background-color: rgba(0, 0, 0, 0.01) !important; pointer-events: auto !important;">…</body>

<body style="background-color: rgba(0, 0, 0, 0.01) !important; pointer-events: auto !important;">…</body>
[Log] [FRONTEND] 🎯 Document visibility: – "visible" (overlay-window-highlight.html, line 6366)
[Log] [FRONTEND] 🎯 Mouse entered event capture layer (overlay-window-highlight.html, line 3853)
[Log] [FRONTEND] 🔧 MOUSE ENTER on event capture layer (overlay-window-highlight.html, line 3858)
[Log] [2025-07-31 23:56:32.292][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (989, 31) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.293][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.293][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (989, 31) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.294][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.294][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.296][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.297][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.297][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.297][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.298][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.298][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.299][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.299][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.301][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.301][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.302][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.302][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.303][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.304][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.304][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.305][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.305][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.305][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.305][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.305][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.307][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.306][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.307][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.308][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.309][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.310][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.311][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.311][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.311][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.311][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.312][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.312][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.312][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.313][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.313][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.314][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.315][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.316][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.316][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.317][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.319][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.320][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.320][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.320][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.322][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.322][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.323][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.324][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.324][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.325][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.327][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.328][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.329][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.329][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.329][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.330][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.330][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.331][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.331][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.332][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.333][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.333][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.334][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.334][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.335][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.335][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.335][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.335][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.335][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.336][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.336][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.336][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.336][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.337][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.338][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.338][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.338][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.339][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.340][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.341][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.341][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.342][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.342][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.342][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.343][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.343][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.344][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.344][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.345][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.345][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.345][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.345][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.346][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.346][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.347][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.347][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.348][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.348][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.349][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.350][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.351][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.351][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.351][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.351][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.352][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.352][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.352][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.353][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.353][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.354][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.355][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.355][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.356][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.356][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.358][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (989, 31) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.359][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (989, 31) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.360][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (989, 31) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.362][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (989, 31) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.364][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (989, 31) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.365][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (989, 31), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.365][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.366][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.367][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.368][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (989, 31): Notification Center (通知中心) layer=23 in 71ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.368][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:56:32.520][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1005, 38) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.521][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1005, 38) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.526][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.526][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.540][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.540][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.540][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.540][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.541][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.541][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.541][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.541][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.541][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.541][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.542][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.542][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.542][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.542][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.543][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.543][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.543][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.543][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.545][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.545][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.545][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.545][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.547][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.546][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.547][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.547][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.548][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.547][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.548][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.548][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.548][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.548][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.549][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.549][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.549][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.549][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.549][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.549][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.549][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.549][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.550][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.550][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.550][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.550][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.551][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.551][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.551][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.551][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.552][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1005, 38) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.553][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1005, 38) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.554][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1005, 38) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.555][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1005, 38) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.556][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1005, 38) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.557][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1005, 38), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.558][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.558][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.558][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.559][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1005, 38): Notification Center (通知中心) layer=23 in 36ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.559][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:56:32.664][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1037, 64) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.664][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.664][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1037, 64) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.666][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.666][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.667][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.667][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.667][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.667][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.667][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.668][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.668][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.668][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.668][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.668][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.669][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.669][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.669][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.670][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.670][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.670][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.671][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.672][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.673][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.673][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.674][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.676][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.676][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.676][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.677][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.677][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.677][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.677][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.678][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.680][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.681][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.684][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.685][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.683][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.685][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.685][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.687][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.688][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.689][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.689][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.689][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.689][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.690][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.690][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.692][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.692][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.693][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.694][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.694][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.694][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.695][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.696][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.696][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.697][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.698][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.699][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.701][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.700][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.701][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.702][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.702][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.703][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.706][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.703][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.707][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.707][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.708][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.708][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.709][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.709][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.709][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.710][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.710][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.710][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.710][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.710][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.716][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.718][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.720][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.720][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.721][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.725][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.725][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.725][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.726][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.726][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.727][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.727][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.728][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.728][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.731][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.734][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.735][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1037, 64) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1037, 64) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.742][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1037, 64) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.744][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1037, 64) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1037, 64) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1037, 64), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.748][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.748][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.749][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.749][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1037, 64): Notification Center (通知中心) layer=23 in 83ms (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:56:32.749][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.790][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1041, 67) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.790][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.791][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1041, 67) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.794][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.794][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.795][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.795][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.795][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.795][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.796][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.796][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.796][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.796][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.796][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.797][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.797][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.797][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.797][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.797][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.798][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.798][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.798][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.799][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.799][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.800][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.800][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.800][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.801][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.801][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.802][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.802][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.803][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.803][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.803][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.803][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.804][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.804][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.804][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.805][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.805][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.805][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.805][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.805][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.806][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.806][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.806][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.807][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.807][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.807][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.807][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.807][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.808][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.808][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.808][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.808][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.809][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.809][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.810][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.810][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.810][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.811][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.811][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.811][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.811][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.812][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.812][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.812][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.812][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.813][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.813][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.813][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.813][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.814][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.814][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.815][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.815][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.815][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.815][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.816][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.816][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.816][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.816][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.817][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.817][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.817][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.817][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.818][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.818][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.818][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.818][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.819][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.819][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.819][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.820][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.820][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.820][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.820][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.820][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.820][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.821][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.821][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.821][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.821][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.822][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.822][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.822][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.822][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.822][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.822][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.823][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.823][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.823][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.824][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.824][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.824][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.824][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.824][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.825][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.825][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.825][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.825][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.825][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.825][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.826][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.826][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.826][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1041, 67) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.827][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1041, 67) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.827][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1041, 67) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.828][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1041, 67) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.829][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1041, 67) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.829][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1041, 67), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.829][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.830][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.830][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.830][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1041, 67): Notification Center (通知中心) layer=23 in 38ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.830][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:56:32.946][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1042, 69) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.964][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.964][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1042, 69) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.966][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.967][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.968][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.968][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.969][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.969][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.969][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.969][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.970][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.970][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.970][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.971][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.972][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.973][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.974][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.975][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.975][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.976][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.976][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.977][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.977][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.978][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.978][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.979][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.982][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.982][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.983][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.983][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.983][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.984][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.984][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.985][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.985][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.986][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.987][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.988][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.988][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.988][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.989][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.990][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.991][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.991][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.992][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.992][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.993][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.994][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.994][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.995][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.995][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.996][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.996][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.998][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.998][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:32.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.000][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.000][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.001][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.005][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.013][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.013][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.014][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.018][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.018][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.018][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.019][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.021][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.024][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.025][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.025][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.027][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.027][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.028][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.028][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.028][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.029][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.032][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.045][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.042][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.045][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.047][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.045][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.048][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.048][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.048][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.048][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.048][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.049][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.049][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.050][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.050][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.051][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.051][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.053][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.053][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.056][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.054][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.055][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.055][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.056][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.056][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1042, 69) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.057][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 69) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.052][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.062][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 69) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.063][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 69) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.064][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 69) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.064][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1042, 69), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.067][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.068][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.068][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.069][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.078][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1042, 69): Notification Center (通知中心) layer=23 in 99ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.072][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.069][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.078][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.079][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:56:33.080][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.082][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.083][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.085][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.086][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.086][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.086][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.087][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.087][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.087][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.087][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.089][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.089][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.089][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.089][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.089][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.090][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.090][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.093][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.094][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.094][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.094][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.095][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.096][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.098][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.100][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.101][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.102][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.102][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.103][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.104][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.105][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.105][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.105][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.107][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.108][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.109][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.110][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.111][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.112][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.113][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.113][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.114][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.115][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.115][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.116][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.117][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.117][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.118][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.118][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.119][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.119][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.120][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.120][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.124][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.123][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.121][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.123][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.124][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.124][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.125][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.126][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.126][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.126][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.127][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.127][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.128][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.128][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.129][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.129][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.130][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.130][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.132][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.133][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.133][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.134][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.134][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.135][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.135][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.136][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.136][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.136][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.137][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.137][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.138][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.138][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.138][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.138][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.139][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.139][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.139][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.139][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.139][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.139][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.140][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.140][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.140][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.140][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.140][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.141][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.141][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.142][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.142][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.142][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.143][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.143][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.144][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.144][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.145][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.146][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.146][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.147][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.147][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.148][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.148][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.149][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.150][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.151][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.152][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.153][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.154][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.155][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1043, 71), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.155][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.157][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.157][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.158][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1043, 71): Notification Center (通知中心) layer=23 in 86ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.158][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:56:33.245][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.245][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.245][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.248][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.248][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.250][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.250][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.250][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.252][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.253][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.253][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.255][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.255][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.256][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.257][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.258][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.260][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.261][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.262][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.262][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.263][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.263][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.264][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.264][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.266][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.266][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.266][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.267][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.267][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.269][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.264][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.270][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.273][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.265][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.267][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.274][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.276][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.273][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.276][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.278][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.279][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.278][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.279][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.280][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.281][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.277][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.282][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.279][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.282][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.283][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.280][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.283][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.283][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.284][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.284][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.285][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.285][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.286][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.287][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.286][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.287][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.288][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.288][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.288][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.289][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.290][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.290][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.291][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.291][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.295][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.296][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.298][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.297][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.298][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.299][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.299][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.299][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.300][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.300][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.300][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.301][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.302][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.302][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.301][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.302][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.303][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.303][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.304][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.306][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.306][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.307][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.307][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.308][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.309][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.309][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.310][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.310][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.310][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.311][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.312][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.312][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.313][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.314][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.314][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.314][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.315][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.315][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.317][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.318][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.319][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.320][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.322][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.326][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.327][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.329][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.331][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.330][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.331][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.331][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.332][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.333][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.334][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.336][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.336][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.338][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.339][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.340][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.343][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1043, 71), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.344][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.344][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.345][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.345][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1043, 71): Notification Center (通知中心) layer=23 in 98ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:33.345][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:56:34.721][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.721][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.731][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.734][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.735][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.735][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.742][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.742][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.743][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.743][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.744][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.746][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.746][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.748][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.749][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.749][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.750][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.750][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.750][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.751][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.751][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.752][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.752][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.753][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.754][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.754][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.755][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.756][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.756][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.757][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.757][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.758][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.759][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.759][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.759][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.760][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.760][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.761][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.762][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.762][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.763][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.763][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.763][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.764][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.765][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.766][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.766][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.767][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.767][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.767][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.768][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.768][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.769][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.769][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.770][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.770][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.771][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.771][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.771][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.772][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.772][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.773][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.774][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.774][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.775][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.775][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.774][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.775][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.776][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.776][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.777][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.777][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.777][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.778][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.778][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.778][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.779][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.779][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.780][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.780][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.780][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.780][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.781][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.781][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.781][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.782][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.782][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.783][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.784][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.785][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.785][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.786][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.787][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.787][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.787][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.788][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.788][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.788][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.788][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.789][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.789][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.789][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.792][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.793][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.795][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.797][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.798][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1043, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.799][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1043, 71), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.799][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.800][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.800][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.801][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1043, 71): Notification Center (通知中心) layer=23 in 77ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.801][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:56:34.884][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1042, 70) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.884][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1042, 70) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.890][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.891][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.892][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.892][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.894][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.895][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.895][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.896][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.897][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.897][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.901][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.902][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.903][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.903][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.905][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.905][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.906][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.906][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.908][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.908][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.909][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.909][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.910][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.910][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.910][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.910][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.911][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.911][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.911][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.912][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.913][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.913][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.914][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.914][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.914][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.915][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.915][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.915][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.916][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.917][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.917][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.918][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.918][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.919][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.919][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.920][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.920][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.920][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.921][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.921][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.921][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.922][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.922][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.922][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.922][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.923][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.924][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.925][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.926][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.926][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.927][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.928][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.928][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.929][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.929][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.930][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.930][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.931][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.931][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.931][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.932][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.933][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.934][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.935][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.936][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.936][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.937][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.937][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.937][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.938][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.938][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.938][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.939][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.939][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.939][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.940][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.942][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.942][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.943][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.943][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.943][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.944][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.945][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.945][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.946][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.946][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.946][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.947][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.948][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.948][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.948][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.949][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.949][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.950][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.950][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.952][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.953][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1042, 70) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.954][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 70) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.956][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 70) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.957][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 70) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.959][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 70) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.960][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1042, 70), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.961][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.962][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.962][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.963][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1042, 70): Notification Center (通知中心) layer=23 in 75ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:56:34.963][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:57:01.061][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1042, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.061][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.061][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1042, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.063][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.063][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.065][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.065][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.065][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.065][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.066][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.066][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.068][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.068][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.068][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.069][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.070][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.070][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.071][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.071][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.071][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.072][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.073][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.074][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.074][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.075][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.075][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.076][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.080][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.083][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.082][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.083][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.085][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.085][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.086][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.087][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.088][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.089][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.089][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.089][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.090][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.090][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.090][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.091][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.093][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.095][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.095][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.096][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.096][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.097][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.098][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.101][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.102][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.104][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.103][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.106][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.107][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.108][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.108][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.109][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.109][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.110][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.110][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.110][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.111][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.111][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.111][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.112][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.115][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.115][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.112][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.117][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.117][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.116][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.118][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.120][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.121][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.122][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.122][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.122][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.123][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.123][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.124][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.125][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.127][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.127][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.128][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.130][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.131][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.131][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.131][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.132][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.132][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.132][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.133][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.134][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.135][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.136][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.136][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.137][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.138][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.139][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.140][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.141][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.141][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.144][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.144][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.144][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.147][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.148][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.148][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.148][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.149][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.149][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.149][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.151][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.151][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.151][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.151][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.152][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.152][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.152][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.155][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1042, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.158][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.159][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.161][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.165][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1042, 71) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.166][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1042, 71), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.166][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.167][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.168][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.169][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1042, 71): Notification Center (通知中心) layer=23 in 105ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.172][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:57:01.265][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (1055, 106) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.265][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.266][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (1055, 106) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.267][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.269][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.268][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.270][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.270][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.271][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.272][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.273][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.273][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.274][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.274][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.274][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.275][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.275][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.275][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.276][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.276][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.277][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.277][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.278][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.279][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.280][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.280][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.280][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.281][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.281][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.282][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.282][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.283][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.283][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.283][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.284][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.284][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.284][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.285][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.288][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.292][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.292][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.296][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.297][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.297][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.298][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.300][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.301][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.302][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.303][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.303][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.304][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.304][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.305][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.305][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.307][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.306][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.306][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.308][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.308][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.309][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.310][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.310][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.313][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.314][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.316][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.318][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.319][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.320][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.321][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.322][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.323][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.324][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.325][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.326][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.326][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.327][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.328][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.327][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.329][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.329][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.329][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.330][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.330][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.330][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.331][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.331][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.332][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.332][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.332][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.331][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.332][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.334][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.335][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.336][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.338][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.339][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.340][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.341][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.342][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.341][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.342][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.342][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.343][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.343][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.344][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.344][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.345][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.346][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.347][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.347][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.348][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.348][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.349][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.350][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.350][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.350][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.351][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.351][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.350][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.350][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.350][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.351][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.352][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.353][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.352][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.354][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (1055, 106) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.358][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1055, 106) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.360][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1055, 106) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.361][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1055, 106) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.362][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1055, 106) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.363][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (1055, 106) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.363][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (1055, 106), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.363][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.364][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.365][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (1055, 106): Notification Center (通知中心) layer=23 in 97ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.364][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:57:01.365][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.388][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.389][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.389][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.391][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.392][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.394][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.394][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.394][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.395][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.395][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.396][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.398][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.397][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.398][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.398][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.398][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.399][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.400][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.401][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.401][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.401][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.402][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.402][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.402][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.403][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.403][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.404][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.403][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.404][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.404][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.405][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.406][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.406][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.407][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.407][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.408][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.408][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.408][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.409][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.409][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.410][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.410][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.410][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.411][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.412][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.413][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.413][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.414][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.414][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.414][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.414][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.415][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.415][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.416][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.417][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.417][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.417][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.417][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.418][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.418][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.418][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.419][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.418][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.419][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.419][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.421][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.422][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.422][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.422][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.422][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.423][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.423][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.424][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.424][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.425][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.425][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.425][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.426][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.426][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.426][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.427][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.427][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.427][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.428][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.430][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.430][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.430][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.431][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.432][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.432][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.432][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.433][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.433][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.433][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.434][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.434][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.434][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.435][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.435][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.436][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.436][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.436][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.436][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.436][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.437][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.438][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.438][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.438][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.438][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.439][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.439][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.439][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.439][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.440][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.441][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.441][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.442][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.442][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.444][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.448][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.450][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.451][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.455][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.458][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (960, 311), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.457][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (960, 311) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.458][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.459][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.460][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.460][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (960, 311): Notification Center (通知中心) layer=23 in 68ms (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:57:01.461][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.493][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.494][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.494][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.496][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.496][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.504][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.505][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.506][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.506][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.507][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.507][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.508][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.508][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.508][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.508][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.509][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.509][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.509][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.510][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.510][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.510][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.510][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.511][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.511][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.511][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.512][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.513][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.513][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.513][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.513][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.514][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.514][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.514][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.515][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.515][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.515][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.516][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.516][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.516][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.516][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.517][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.517][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.517][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.517][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.518][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.518][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.518][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.519][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.520][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.521][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.522][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.523][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.524][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.525][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.526][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.526][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.527][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.528][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.529][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.530][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.531][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.532][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.533][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.534][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.535][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.536][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.537][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.538][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.539][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.540][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.540][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.541][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.542][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.542][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.543][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.543][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (823, 536) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (823, 536), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.544][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (823, 536): Notification Center (通知中心) layer=23 in 49ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.545][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:57:01.671][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.671][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.671][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.672][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.673][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.674][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.674][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.674][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.675][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.676][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.676][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.677][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.677][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.678][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.678][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.678][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.679][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.680][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.680][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.681][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.682][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.682][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.683][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.684][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.684][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.684][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.711][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.712][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.713][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.714][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.714][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.714][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.714][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.716][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.715][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.716][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.716][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.717][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.717][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.717][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.717][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.718][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.718][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.718][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.719][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.719][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.719][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.719][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.720][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.721][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.721][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.722][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.723][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.724][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.724][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.725][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.725][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.726][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.726][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.726][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.727][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.727][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.727][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.728][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.728][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.728][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.729][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.730][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.732][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.733][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.734][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.734][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.735][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.735][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.736][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.737][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.738][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.739][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.740][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.741][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.742][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.743][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.744][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.745][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.745][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (808, 550), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.746][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.746][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 550) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.747][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.748][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (808, 550): Notification Center (通知中心) layer=23 in 75ms (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.748][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:57:01.866][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.866][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.866][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.868][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.868][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.870][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.870][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.871][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.872][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.873][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.874][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.875][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.875][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.876][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.876][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.876][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.878][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.879][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.879][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.881][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.883][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.883][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.883][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.884][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.884][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.885][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.886][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.887][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.887][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.887][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.888][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.889][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.890][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.890][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.890][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.891][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.891][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.891][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.892][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.892][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.893][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.894][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.894][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.894][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.895][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.895][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.896][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.896][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.897][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.897][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.898][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.899][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.900][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.901][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.901][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.901][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.902][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.902][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.902][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.903][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.903][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.903][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.905][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.905][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.904][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.906][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.906][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.907][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.908][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.911][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.916][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.917][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.919][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.921][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.925][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.926][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.926][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.927][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.928][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.929][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.929][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.930][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.931][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.933][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.933][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.934][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.934][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.935][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.932][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.938][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.939][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.939][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552) (user-script:37, line 1, x2)
[Log] [FRONTEND] 🔧 MOUSE LEAVE on event capture layer (overlay-window-highlight.html, line 3862)
[Log] [2025-07-31 23:57:01.941][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.944][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.948][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.950][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.949][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (808, 552) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.950][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (808, 552), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.951][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.951][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.952][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.952][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (808, 552): Notification Center (通知中心) layer=23 in 83ms (user-script:37, line 1, x2)
[Log] [FRONTEND] Highlighting window: – "Notification Center" – "ID:" – undefined (overlay-window-highlight.html, line 4227)
[Log] [2025-07-31 23:57:01.953][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] macOS window detected (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.994][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Starting smart window detection at coordinates (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.994][mecap_lib::modules::hybrid_screenshot][TRACE] [SMART_DETECT] Using macOS detection (placeholder) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.994][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting window detection at coordinates (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.996][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Screen recording permission check passed - found 25 windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.996][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Starting to get window list from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.997][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Retrieved 25 total windows from system (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.998][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 1/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.998][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (0x0 at 640,663) layer=9999 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("小旺AI截图"), size=0x0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: window too small (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:01.999][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 2/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.000][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (34x24 at 966,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.000][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("mecap"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.001][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 3/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.001][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("ClashX Pro") (82x24 at 1133,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.001][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("ClashX Pro"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("ClashX Pro"), title=Some("Item-0"), size=82x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 4/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.002][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("搜狗输入法") (133x24 at 1000,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("搜狗输入法"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("搜狗输入法"), title=Some("Item-0"), size=133x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.003][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 5/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.004][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("小旺AI截图") (34x24 at 1215,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.005][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("小旺AI截图"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.007][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("小旺AI截图"), title=Some("Item-0"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.007][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 6/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.007][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("TextInputMenuAgent") (44x24 at 1249,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("TextInputMenuAgent"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("TextInputMenuAgent"), title=Some("Item-0"), size=44x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 7/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.008][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (42x24 at 1293,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.009][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Battery"), process=Some("控制中心"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.009][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Battery"), size=42x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.009][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 8/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.010][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (38x24 at 1335,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.010][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("WiFi"), process=Some("控制中心"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.010][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("WiFi"), size=38x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.011][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 9/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.011][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (33x24 at 1373,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.011][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("UserSwitcher"), process=Some("控制中心"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("UserSwitcher"), size=33x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 10/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (34x24 at 1438,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.012][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("BentoBox"), process=Some("控制中心"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.013][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("BentoBox"), size=34x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.013][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 11/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.014][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("SystemUIServer") (30x24 at 1472,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.014][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Siri"), process=Some("SystemUIServer"), size=30x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process SystemUIServer (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 12/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("控制中心") (178x24 at 1502,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Clock"), process=Some("控制中心"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.015][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("控制中心"), title=Some("Clock"), size=178x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 13/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("聚焦") (32x24 at 1406,0) layer=25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Item-0"), process=Some("聚焦"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.016][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("聚焦"), title=Some("Item-0"), size=32x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 14/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x24 at 0,0) layer=24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.017][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Menubar"), process=Some("Window Server"), size=1680x24 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.018][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.018][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 15/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.019][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("通知中心") (1680x1050 at 0,0) layer=23 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.019][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Notification Center"), process=Some("通知中心"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.019][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("通知中心"), title=Some("Notification Center"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.020][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 16/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.021][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("mecap") (1680x1050 at 0,0) layer=5 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.022][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 17/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.021][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Mecap Window Highlight"), process=Some("mecap"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.022][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Code") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.022][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("lib.rs — Mecap"), process=Some("Code"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.023][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Code"), title=Some("lib.rs — Mecap"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.023][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 18/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.023][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Google Chrome") (1680x1025 at 0,25) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.024][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), process=Some("Google Chrome"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.024][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("Google Chrome"), title=Some("GLM-4.1V-Thinking-Flash - Bigmodel"), size=1680x1025 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.025][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 19/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.025][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (821x776 at 449,78) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("下载"), process=Some("访达"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some("下载"), size=821x776 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 20/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.026][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("备忘录") (1212x805 at 378,126) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.027][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("备忘录"), process=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.027][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("备忘录"), title=Some("备忘录"), size=1212x805 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.028][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 21/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.029][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (626x651 at 1101,138) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.029][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("thirdpart — -zsh — 88×44"), process=Some("终端"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.029][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("thirdpart — -zsh — 88×44"), size=626x651 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.030][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 22/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.030][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("终端") (437x623 at 590,133) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.030][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("mxm_pyenv — -zsh — 61×42"), process=Some("终端"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.030][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("终端"), title=Some("mxm_pyenv — -zsh — 61×42"), size=437x623 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.031][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 23/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.031][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("访达") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.031][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some(""), process=Some("访达"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.031][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("访达"), title=Some(""), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.032][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 24/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.032][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("程序坞") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.032][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Wallpaper-"), process=Some("程序坞"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] ✅ Window passed all filters: process=Some("程序坞"), title=Some("Wallpaper-"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Processing window 25/25 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Parsed window: Some("Window Server") (1680x1050 at 0,0) layer=0 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Checking window: title=Some("Desktop"), process=Some("Window Server"), size=1680x1050 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.033][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FILTER] Filtered out: system process Window Server (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Filtered out 6 windows, keeping 19 valid windows (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Final window list has 19 windows, sorted by Z-order (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 0: Some("ClashX Pro") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 1: Some("搜狗输入法") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.034][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 2: Some("小旺AI截图") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.035][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 3: Some("TextInputMenuAgent") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.035][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 4: Some("控制中心") - Some("Battery") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.035][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 5: Some("控制中心") - Some("WiFi") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.035][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 6: Some("控制中心") - Some("UserSwitcher") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 7: Some("控制中心") - Some("BentoBox") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 8: Some("控制中心") - Some("Clock") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 9: Some("聚焦") - Some("Item-0") (layer=25) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 10: Some("通知中心") - Some("Notification Center") (layer=23) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 11: Some("Code") - Some("lib.rs — Mecap") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.036][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 12: Some("Google Chrome") - Some("GLM-4.1V-Thinking-Flash - Bigmodel") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 13: Some("访达") - Some("下载") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 14: Some("备忘录") - Some("备忘录") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 15: Some("终端") - Some("thirdpart — -zsh — 88×44") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 16: Some("终端") - Some("mxm_pyenv — -zsh — 61×42") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 17: Some("访达") - Some("") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.037][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Window 18: Some("程序坞") - Some("Wallpaper-") (layer=0) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.038][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Notification Center' (layer=23) is visible at point (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.038][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'lib.rs — Mecap' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'GLM-4.1V-Thinking-Flash - Bigmodel' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '下载' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.039][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '备忘录' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.040][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'mxm_pyenv — -zsh — 61×42' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.040][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window '' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_VISIBILITY] Window 'Wallpaper-' (layer=0) is obscured by 'Notification Center' (layer=23) at point (801, 573) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] Found 1 candidate windows at (801, 573), Z-order analysis: (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.041][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS]   1. Notification Center (通知中心) - layer=23 (TOPMOST) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.042][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] Inferred focused application from top window: 通知中心 (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.042][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS_FOCUS] ✅ Selected topmost window (also from focused app): Notification Center (通知中心) (user-script:37, line 1, x2)
[Log] [2025-07-31 23:57:02.042][mecap_lib::modules::hybrid_screenshot::macos_detection][TRACE] [MACOS] ✅ Selected window at (801, 573): Notification Center (通知中心) layer=23 in 47ms (user-script:37, line 1, x2)