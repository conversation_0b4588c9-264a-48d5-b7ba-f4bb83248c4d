<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Region Selector</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.3);
            cursor: crosshair;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
        }

        .overlay-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .selection-area {
            position: absolute;
            border: 2px dashed #00FF00;
            background: rgba(0, 255, 0, 0.1);
            display: none;
            pointer-events: none;
        }

        .selection-info {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: monospace;
            display: none;
            pointer-events: none;
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.3;
        }

        .grid-line {
            position: absolute;
            background: rgba(255, 255, 255, 0.2);
        }

        .grid-line.vertical {
            width: 1px;
            height: 100%;
        }

        .grid-line.horizontal {
            width: 100%;
            height: 1px;
        }

        .instructions {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 14px;
        }

        .instructions h3 {
            margin-bottom: 8px;
            color: #00FF00;
        }

        .mode-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 255, 0, 0.9);
            color: black;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
        }

        .coordinates-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 11px;
            min-width: 200px;
        }

        .coordinates-panel .label {
            color: #00FF00;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="overlay-container">
        <div class="grid-overlay" id="gridOverlay"></div>

        <div class="instructions">
            <h3>🎯 Region Selector</h3>
            <p>Click and drag to select a region</p>
            <p>Press <strong>G</strong> to toggle grid | <strong>ESC</strong> to exit</p>
        </div>

        <div class="mode-indicator">
            REGION SELECT
        </div>

        <div class="selection-area" id="selectionArea"></div>
        <div class="selection-info" id="selectionInfo">
            Region: 0×0
        </div>

        <div class="coordinates-panel">
            <div><span class="label">Mouse:</span> <span id="mousePos">(0, 0)</span></div>
            <div><span class="label">Start:</span> <span id="startPos">(0, 0)</span></div>
            <div><span class="label">End:</span> <span id="endPos">(0, 0)</span></div>
            <div><span class="label">Size:</span> <span id="sizeInfo">0×0</span></div>
            <div><span class="label">Area:</span> <span id="areaInfo">0 px²</span></div>
        </div>
    </div>

    <script>
        let isSelecting = false;
        let startX = 0, startY = 0;
        let currentX = 0, currentY = 0;
        let gridVisible = true;

        const selectionArea = document.getElementById('selectionArea');
        const selectionInfo = document.getElementById('selectionInfo');
        const gridOverlay = document.getElementById('gridOverlay');
        const mousePos = document.getElementById('mousePos');
        const startPos = document.getElementById('startPos');
        const endPos = document.getElementById('endPos');
        const sizeInfo = document.getElementById('sizeInfo');
        const areaInfo = document.getElementById('areaInfo');

        // 初始化网格
        createGrid();

        // 事件监听
        document.addEventListener('mousedown', startSelection);
        document.addEventListener('mousemove', updateMouse);
        document.addEventListener('mouseup', endSelection);
        document.addEventListener('keydown', handleKeyboard);

        function createGrid() {
            const gridSize = 50; // 网格大小
            const width = window.innerWidth;
            const height = window.innerHeight;

            // 创建垂直线
            for (let x = 0; x <= width; x += gridSize) {
                const line = document.createElement('div');
                line.className = 'grid-line vertical';
                line.style.left = x + 'px';
                gridOverlay.appendChild(line);
            }

            // 创建水平线
            for (let y = 0; y <= height; y += gridSize) {
                const line = document.createElement('div');
                line.className = 'grid-line horizontal';
                line.style.top = y + 'px';
                gridOverlay.appendChild(line);
            }
        }

        function toggleGrid() {
            gridVisible = !gridVisible;
            gridOverlay.style.display = gridVisible ? 'block' : 'none';
        }

        function startSelection(event) {
            isSelecting = true;
            startX = event.clientX;
            startY = event.clientY;
            currentX = startX;
            currentY = startY;

            startPos.textContent = `(${startX}, ${startY})`;
            updateSelectionArea();
        }

        function updateMouse(event) {
            currentX = event.clientX;
            currentY = event.clientY;
            mousePos.textContent = `(${currentX}, ${currentY})`;

            if (isSelecting) {
                updateSelectionArea();
                updateInfo();
            }
        }

        function endSelection(event) {
            if (!isSelecting) return;

            isSelecting = false;
            endPos.textContent = `(${currentX}, ${currentY})`;

            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            if (width > 5 && height > 5) {
                // 选择完成，可以进行后续操作
                console.log('Region selected:', {
                    x: Math.min(startX, currentX),
                    y: Math.min(startY, currentY),
                    width: width,
                    height: height
                });
            }
        }

        function updateSelectionArea() {
            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            selectionArea.style.left = left + 'px';
            selectionArea.style.top = top + 'px';
            selectionArea.style.width = width + 'px';
            selectionArea.style.height = height + 'px';
            selectionArea.style.display = 'block';

            // 更新信息显示位置
            selectionInfo.style.left = (left + width + 10) + 'px';
            selectionInfo.style.top = top + 'px';
            selectionInfo.style.display = 'block';
        }

        function updateInfo() {
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);
            const area = width * height;

            sizeInfo.textContent = `${width}×${height}`;
            areaInfo.textContent = `${area.toLocaleString()} px²`;
            selectionInfo.textContent = `Region: ${width}×${height}`;
        }

        function handleKeyboard(event) {
            switch (event.key.toLowerCase()) {
                case 'escape':
                    closeOverlay();
                    break;
                case 'g':
                    toggleGrid();
                    break;
                case 'enter':
                    if (selectionArea.style.display === 'block') {
                        confirmSelection();
                    }
                    break;
            }
        }

        function confirmSelection() {
            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            const region = { x: left, y: top, width, height };

            // 发送选择结果到后端
            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('region_selected', { region })
                    .then(() => {
                        console.log('Region selection confirmed');
                        closeOverlay();
                    })
                    .catch(error => {
                        console.error('Failed to confirm region selection:', error);
                    });
            }
        }

        function closeOverlay() {
            if (window.__TAURI__) {
                window.__TAURI__.window.getCurrent().close();
            }
        }

        // 初始化
        console.log('Region selector overlay loaded');
    </script>
</body>
</html>
