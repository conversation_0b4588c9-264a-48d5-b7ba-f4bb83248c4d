<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Screenshot Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
            -webkit-user-select: none;
            margin: 0;
            padding: 0;
        }

        .preview-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: transparent;
        }

        .preview-wrapper {
            position: relative;
            /* 🔧 优化：使用outline边框，不占用内容空间，确保截图尺寸不变 */
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            /* 🔧 预览状态：橙色outline边框，符合用户偏好 */
            outline: 3px solid rgba(255, 107, 53, 0.8);  /* 橙色实线边框 */
            outline-offset: 2px; /* 边框与内容的间距 */
            box-shadow:
                0 8px 32px rgba(255, 107, 53, 0.2), /* 橙色外阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.2); /* 内高光 */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: outline-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* 🔧 CRITICAL FIX: 编辑模式样式 - 简单1px边框，无调整手柄 */
        .preview-wrapper.editing-mode {
            /* 🔧 使用简单的1px边框替代复杂的outline，避免调整手柄和保存时的边框问题 */
            outline: 1px solid #ff6b35 !important; /* 简单的1px橙色边框 */
            outline-offset: 0px; /* 无偏移，紧贴内容 */
            box-shadow:
                0 4px 16px rgba(255, 107, 53, 0.2), /* 减少阴影强度 */
                inset 0 0 0 1px rgba(255, 255, 255, 0.1);
            background: rgba(255, 107, 53, 0.02); /* 减少背景色强度 */
            /* 🔧 移除动画，避免干扰保存操作和调整手柄显示 */
        }

        .preview-wrapper.editing-mode::before {
            content: '🎨 编辑模式';
            position: absolute;
            top: -35px;
            left: 0;
            background: linear-gradient(135deg, #ff6b35, #f7931e); /* 橙色渐变 */
            color: white;
            padding: 6px 14px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3); /* 橙色阴影 */
            animation: editingModeIndicator 0.4s ease;
        }

        @keyframes editingModeIndicator {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes editingModePulse {
            0%, 100% {
                outline-color: rgba(255, 107, 53, 0.8); /* 橙色outline动画 */
                box-shadow:
                    0 8px 32px rgba(255, 107, 53, 0.3),
                    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
            }
            50% {
                outline-color: rgba(255, 107, 53, 1.0); /* 更亮的橙色 */
                box-shadow:
                    0 8px 40px rgba(255, 107, 53, 0.4),
                    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
            }
        }

        .preview-canvas {
            display: block;
            border-radius: 4px;
            cursor: default;
            user-select: none;
            -webkit-user-select: none;
            /* 关键：确保canvas不被浏览器额外缩放 */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: -moz-crisp-edges;
            image-rendering: pixelated;
            image-rendering: crisp-edges;
            /* 移除所有动态效果，确保预览与原始截图完全一致 */
        }

        /* 🎨 标注层样式 - 修复坐标偏移问题 */
        .annotation-layer {
            position: absolute;
            top: 0px;
            left: 0px;
            right: 0px;
            bottom: 0px;
            border-radius: 4px;
            pointer-events: none;
            z-index: 100;
        }

        .annotation-layer.active {
            pointer-events: all;
        }

        /* 工具光标样式 */
        .annotation-layer.tool-text {
            cursor: text !important;
        }

        .annotation-layer.tool-arrow,
        .annotation-layer.tool-rectangle,
        .annotation-layer.tool-circle,
        .annotation-layer.tool-ellipse,
        .annotation-layer.tool-line,
        .annotation-layer.tool-brush,
        .annotation-layer.tool-pen {
            cursor: crosshair !important;
        }

        .annotation-layer.tool-eraser {
            cursor: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24'><path fill='%23ff6b35' d='M16.24 3.56l4.95 4.94c.78.79.78 2.05 0 2.84L12 20.53a4.008 4.008 0 0 1-5.66 0L2.81 17c-.78-.79-.78-2.05 0-2.84l10.6-10.6c.79-.78 2.05-.78 2.83 0M4.22 15.58l3.54 3.53c.78.79 2.04.79 2.83 0l3.53-3.53l-4.95-4.95l-4.95 4.95Z'/></svg>") 10 10, auto !important;
        }

        /* 标注层样式 - 修复坐标偏移问题 */
        .annotation-layer {
            position: absolute;
            top: 0px; /* 修复：完美对齐，消除坐标偏移 */
            left: 0px;
            right: 0px;
            bottom: 0px;
            pointer-events: none;
            z-index: 5;
            border-radius: 4px;
            overflow: hidden;
        }

        .annotation-layer.active {
            pointer-events: all;
        }

        .annotation-layer.active.tool-text {
            cursor: text;
        }

        .annotation-layer.active.tool-arrow,
        .annotation-layer.active.tool-rectangle,
        .annotation-layer.active.tool-circle,
        .annotation-layer.active.tool-brush {
            cursor: crosshair;
        }

        .annotation-layer.active.tool-eraser {
            cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="%23ff6b35" d="M16.24 3.56l4.95 4.94c.78.79.78 2.05 0 2.84L12 20.53a4.008 4.008 0 0 1-5.66 0L2.81 17c-.78-.79-.78-2.05 0-2.84l10.6-10.6c.79-.78 2.05-.78 2.83 0M4.22 15.58l3.54 3.53c.78.79 2.04.79 2.83 0l3.53-3.53l-4.95-4.95l-4.95 4.95Z"/></svg>') 10 10, auto;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hint-text {
            position: absolute;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            text-align: center;
            background: rgba(0, 0, 0, 0.6);
            padding: 8px 16px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            opacity: 0;
            animation: fadeInUp 0.5s ease 1s forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #ff6b6b;
            font-size: 16px;
            text-align: center;
            padding: 20px;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        /* 🔧 智能边缘吸附工具栏样式：与预览窗口橙色边框保持一致 */
        .toolbar-container {
            position: fixed;
            /* 初始位置将通过JavaScript动态计算 */
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 8px;
            box-shadow:
                0 0 0 1px rgba(255, 107, 53, 0.2),  /* 橙色边框与预览窗口呼应 */
                0 8px 32px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            display: flex;
            gap: 4px;
            opacity: 1;
            transition: opacity 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
            z-index: 1000;
            /* 支持水平和垂直布局 */
        }

        .toolbar-container.vertical {
            flex-direction: column;
        }

        /* 🔧 编辑模式下工具栏样式变化：保持橙色主题，通过透明度区分状态 */
        .editing-mode ~ .toolbar-container,
        .toolbar-container.editing-mode {
            box-shadow:
                0 0 0 1px rgba(255, 107, 53, 0.4),  /* 更亮的橙色边框与编辑模式呼应 */
                0 8px 32px rgba(0, 0, 0, 0.15);
        }

        .toolbar-container.horizontal {
            flex-direction: row;
        }

        .toolbar-container.hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* 不同位置的进入/退出动画 */
        .toolbar-container.position-right.hidden {
            transform: translateX(20px);
        }

        .toolbar-container.position-left.hidden {
            transform: translateX(-20px);
        }

        .toolbar-container.position-top.hidden {
            transform: translateY(-20px);
        }

        .toolbar-container.position-bottom.hidden {
            transform: translateY(20px);
        }

        .toolbar-group {
            display: flex;
            gap: 4px;
            padding: 4px 0;
        }

        /* 垂直布局时的工具组样式 */
        .toolbar-container.vertical .toolbar-group {
            flex-direction: column;
        }

        .toolbar-container.vertical .toolbar-group:not(:last-child) {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding-bottom: 8px;
            margin-bottom: 4px;
        }

        /* 水平布局时的工具组样式 */
        .toolbar-container.horizontal .toolbar-group {
            flex-direction: row;
        }

        .toolbar-container.horizontal .toolbar-group:not(:last-child) {
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            padding-right: 8px;
            margin-right: 4px;
        }

        .toolbar-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.8);
            color: #333;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            user-select: none;
            -webkit-user-select: none;
        }

        .toolbar-button:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            transform: scale(1.05);
        }

        .toolbar-button.active {
            background: #3b82f6;
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .color-picker-container {
            display: flex;
            flex-direction: column;
            gap: 4px;
            align-items: center;
        }

        .color-picker {
            width: 32px;
            height: 32px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            cursor: pointer;
            background: #ff0000;
            transition: transform 0.2s ease;
        }

        .color-picker:hover {
            transform: scale(1.1);
        }

        .color-palette {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            width: 40px;
            margin-top: 4px;
        }

        .color-option {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            cursor: pointer;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .color-option:hover {
            transform: scale(1.2);
        }

        .toolbar-label {
            font-size: 10px;
            color: #666;
            text-align: center;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="loading-container" id="loadingContainer">
            <div class="loading-spinner"></div>
            <div>Loading screenshot preview...</div>
        </div>

        <!-- 🔧 CRITICAL FIX: Remove data-tauri-drag-region completely to eliminate 8 resize handles -->
        <div class="preview-wrapper" id="previewWrapper" style="display: none;">
            <canvas class="preview-canvas" id="previewCanvas"></canvas>
        </div>

        <div class="error-container" id="errorContainer" style="display: none;">
            <div class="error-icon">⚠️</div>
            <div>Failed to load screenshot</div>
        </div>
    </div>

    <!-- 快捷工具栏 (仅在独立工具栏不可用时显示) -->
    <div class="toolbar-container hidden" id="toolbarContainer" style="display: none;">
        <!-- 绘图工具组 -->
        <div class="toolbar-group">
            <button class="toolbar-button" data-tool="text" title="文字标注 (T)">
                T
            </button>
            <button class="toolbar-button" data-tool="arrow" title="箭头 (A)">
                ↗
            </button>
            <button class="toolbar-button" data-tool="rectangle" title="矩形 (R)">
                ▢
            </button>
            <button class="toolbar-button" data-tool="circle" title="圆形 (C)">
                ○
            </button>
            <button class="toolbar-button" data-tool="brush" title="画笔 (B)">
                ✏
            </button>
        </div>

        <!-- 颜色选择组 -->
        <div class="toolbar-group">
            <div class="color-picker-container">
                <div class="color-picker" id="colorPicker" title="选择颜色"></div>
                <div class="color-palette">
                    <div class="color-option" style="background: #ff0000;" data-color="#ff0000"></div>
                    <div class="color-option" style="background: #00ff00;" data-color="#00ff00"></div>
                    <div class="color-option" style="background: #0000ff;" data-color="#0000ff"></div>
                    <div class="color-option" style="background: #ffff00;" data-color="#ffff00"></div>
                    <div class="color-option" style="background: #ff00ff;" data-color="#ff00ff"></div>
                    <div class="color-option" style="background: #00ffff;" data-color="#00ffff"></div>
                    <div class="color-option" style="background: #000000;" data-color="#000000"></div>
                    <div class="color-option" style="background: #ffffff;" data-color="#ffffff"></div>
                </div>
            </div>
        </div>

        <!-- 操作按钮组 -->
        <div class="toolbar-group">
            <button class="toolbar-button" data-action="save" title="保存 (Ctrl+S)">
                💾
            </button>
            <button class="toolbar-button" data-action="close" title="关闭 (ESC)">
                ✕
            </button>
        </div>
    </div>

    <script>
        console.log('[PREVIEW] Screenshot preview page loaded');

        let screenshotData = null;

        // 窗口状态管理
        let windowState = 'preview'; // 'preview' | 'editing'
        let isEditingMode = false;
        let annotationCanvas = null;
        let annotationLayer = null;
        let annotationContext = null;

        // 🎨 Canvas绘图系统
        let shapes = []; // 存储所有绘制的形状
        let currentShape = null; // 当前正在绘制的形状
        let canvasScale = 1; // Canvas缩放比例

        // 🔧 标注质量控制
        const MIN_SHAPE_SIZE = 5; // 最小有效形状尺寸（像素）
        const MIN_BRUSH_STROKE_LENGTH = 10; // 最小有效画笔笔画长度

        // 📚 撤销重做系统
        let annotationHistory = []; // 操作历史
        let historyIndex = -1; // 当前历史位置
        const MAX_HISTORY_SIZE = 50; // 最大历史记录数

        // 🧽 橡皮擦系统
        let isErasing = false;
        let eraserStartPos = null;

        // 🔢 序号标注系统
        let numberCounter = 1; // 序号计数器

        // 🚀 性能优化系统
        let renderRequestId = null; // 渲染请求ID，用于防抖
        let lastRenderTime = 0; // 上次渲染时间
        const RENDER_THROTTLE_MS = 16; // 渲染节流时间（约60FPS）
        let isDirty = false; // Canvas是否需要重绘
        let visibleShapes = []; // 可见形状缓存
        const VIEWPORT_PADDING = 50; // 视口边距

        // 拖拽功能相关变量（使用Tauri原生拖拽）
        let isDragging = false;

        // 🆕 独立工具栏通信相关变量
        let independentToolbarId = null;
        let isIndependentToolbarMode = false;
        let previewCommunicator = null;
        let currentAnnotationTool = null;

        // 🆕 撤销重做管理器
        let undoRedoManager = null;
        let crossWindowSync = null;

        // 工具栏相关变量（保留用于降级）
        let toolbarHideTimer = null;
        let currentTool = null;
        let currentColor = '#ff0000';
        let isToolbarVisible = false;
        let toolbarPosition = 'right'; // 当前工具栏位置
        let previewBounds = null; // 预览窗口边界信息

        // 从URL参数获取截图信息
        function getScreenshotInfoFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const path = urlParams.get('path');
            const width = urlParams.get('width');
            const height = urlParams.get('height');

            if (path && width && height) {
                return {
                    path: decodeURIComponent(path),
                    width: parseInt(width),
                    height: parseInt(height)
                };
            }
            return null;
        }

        // 高质量Canvas渲染函数
        // 🚀 性能优化的图像渲染函数
        function renderImageToCanvas(canvas, image, originalWidth, originalHeight) {
            const ctx = canvas.getContext('2d');
            const dpr = window.devicePixelRatio || 1;

            console.log('[PREVIEW] 🎨 Rendering with DPR:', dpr);
            console.log('[PREVIEW] 📏 Original dimensions:', originalWidth, 'x', originalHeight);

            // 🚀 性能优化：检查图像尺寸，对大图进行预处理
            const isLargeImage = originalWidth > 2048 || originalHeight > 2048;
            if (isLargeImage) {
                console.log('[PREVIEW] 🚀 Large image detected, applying optimizations');
            }

            // 🔧 修复：确保截图以原始尺寸1:1显示，不进行缩放
            // 由于使用outline边框不占用空间，窗口尺寸已与截图尺寸匹配，无需减去边距
            const containerWidth = window.innerWidth;
            const containerHeight = window.innerHeight;

            // 🔧 关键修复：直接使用原始尺寸，确保1:1显示
            const displayWidth = originalWidth;
            const displayHeight = originalHeight;
            const scale = 1.0; // 始终保持原始尺寸

            console.log('[PREVIEW] 📐 Display dimensions:', displayWidth, 'x', displayHeight, 'scale:', scale);

            // 设置Canvas的实际像素尺寸（考虑DPR）
            canvas.width = displayWidth * dpr;
            canvas.height = displayHeight * dpr;

            // 设置Canvas的CSS显示尺寸
            canvas.style.width = displayWidth + 'px';
            canvas.style.height = displayHeight + 'px';

            // 缩放绘图上下文以匹配DPR
            ctx.scale(dpr, dpr);

            // 🚀 性能优化：根据图像大小选择渲染策略
            if (isLargeImage) {
                // 大图使用高质量但较慢的渲染
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';
                console.log('[PREVIEW] 🚀 Using high-quality rendering for large image');
            } else {
                // 小图禁用平滑以保持清晰度和性能
                ctx.imageSmoothingEnabled = false;
                ctx.imageSmoothingQuality = 'low';
            }

            // 🚀 性能优化：使用分块渲染大图像
            if (isLargeImage && scale < 0.5) {
                renderImageInChunks(ctx, image, displayWidth, displayHeight, originalWidth, originalHeight);
            } else {
                // 标准渲染
                ctx.drawImage(image, 0, 0, displayWidth, displayHeight);
            }

            console.log('[PREVIEW] ✅ Canvas rendering completed');
            console.log('[PREVIEW] 🔍 Final canvas size - CSS:', canvas.style.width, 'x', canvas.style.height);
            console.log('[PREVIEW] 🔍 Final canvas size - Actual:', canvas.width, 'x', canvas.height);
            console.log('[PREVIEW] 🔍 Image smoothing:', ctx.imageSmoothingEnabled ? 'enabled' : 'disabled');
        }

        // 🚀 分块渲染大图像（性能优化）
        function renderImageInChunks(ctx, image, displayWidth, displayHeight, originalWidth, originalHeight) {
            const chunkSize = 512; // 每块512像素
            const chunksX = Math.ceil(originalWidth / chunkSize);
            const chunksY = Math.ceil(originalHeight / chunkSize);

            console.log('[PREVIEW] 🚀 Rendering large image in', chunksX * chunksY, 'chunks');

            for (let y = 0; y < chunksY; y++) {
                for (let x = 0; x < chunksX; x++) {
                    const sourceX = x * chunkSize;
                    const sourceY = y * chunkSize;
                    const sourceWidth = Math.min(chunkSize, originalWidth - sourceX);
                    const sourceHeight = Math.min(chunkSize, originalHeight - sourceY);

                    const destX = (sourceX / originalWidth) * displayWidth;
                    const destY = (sourceY / originalHeight) * displayHeight;
                    const destWidth = (sourceWidth / originalWidth) * displayWidth;
                    const destHeight = (sourceHeight / originalHeight) * displayHeight;

                    ctx.drawImage(
                        image,
                        sourceX, sourceY, sourceWidth, sourceHeight,
                        destX, destY, destWidth, destHeight
                    );
                }
            }
        }

        // 显示截图
        function displayScreenshot(data) {
            console.log('[PREVIEW] Displaying screenshot:', data);

            const loadingContainer = document.getElementById('loadingContainer');
            const previewWrapper = document.getElementById('previewWrapper');
            const previewCanvas = document.getElementById('previewCanvas');
            const errorContainer = document.getElementById('errorContainer');

            screenshotData = data;

            // 转换文件路径为可访问的URL
            if (window.__TAURI__ && window.__TAURI__.core) {
                try {
                    // convertFileSrc 是同步函数，不是 Promise
                    const imageSrc = window.__TAURI__.core.convertFileSrc(data.path);
                    console.log('[PREVIEW] Converted file src:', imageSrc);

                    // 创建临时图像对象用于加载
                    const tempImage = new Image();

                    // 🔧 关键修复：设置crossOrigin以避免Canvas污染
                    tempImage.crossOrigin = 'anonymous';

                    tempImage.onload = function() {
                        console.log('[PREVIEW] ✅ Image loaded successfully');
                        console.log('[PREVIEW] 📊 Image natural size:', this.naturalWidth, 'x', this.naturalHeight);
                        console.log('[PREVIEW] 📊 Expected dimensions:', data.width, 'x', data.height);

                        // 🔧 关键修复：将图片转换为dataURL并保存到screenshotData
                        const tempCanvas = document.createElement('canvas');
                        tempCanvas.width = this.naturalWidth;
                        tempCanvas.height = this.naturalHeight;
                        const tempContext = tempCanvas.getContext('2d');
                        tempContext.drawImage(this, 0, 0);

                        // 保存dataURL到screenshotData，供后续保存使用
                        screenshotData.dataUrl = tempCanvas.toDataURL('image/png');
                        console.log('[PREVIEW] 🔧 Image dataURL saved to screenshotData for export');

                        // 使用高质量Canvas渲染
                        renderImageToCanvas(previewCanvas, this, data.width || this.naturalWidth, data.height || this.naturalHeight);

                        // 隐藏加载提示，显示预览
                        loadingContainer.style.display = 'none';
                        errorContainer.style.display = 'none';
                        previewWrapper.style.display = 'block';

                        console.log('[PREVIEW] 🖼️ Preview displayed successfully with Canvas rendering');
                    };

                    tempImage.onerror = function() {
                        console.error('[PREVIEW] ❌ Failed to load image');
                        showError();
                    };

                    tempImage.src = imageSrc;
                } catch (error) {
                    console.error('[PREVIEW] Failed to convert file src:', error);
                    showError();
                }
            } else {
                console.error('[PREVIEW] Tauri API not available');
                showError();
            }
        }

        // 显示错误
        function showError() {
            const loadingContainer = document.getElementById('loadingContainer');
            const previewWrapper = document.getElementById('previewWrapper');
            const errorContainer = document.getElementById('errorContainer');

            loadingContainer.style.display = 'none';
            previewWrapper.style.display = 'none';
            errorContainer.style.display = 'flex';
        }



        // 键盘快捷键处理
        function handleKeyboardShortcuts(event) {
            const { key, ctrlKey, metaKey, shiftKey } = event;
            const isModifierPressed = ctrlKey || metaKey;

            console.log('[PREVIEW] ⌨️ Key pressed:', { key, ctrlKey, metaKey, shiftKey, isEditingMode });

            // ESC键处理
            if (key === 'Escape' || event.keyCode === 27) {
                event.preventDefault();

                if (isEditingMode) {
                    // 如果在编辑模式，先退出编辑模式
                    console.log('[PREVIEW] 🔑 ESC key pressed, exiting editing mode');
                    switchToPreviewMode();
                } else {
                    // 如果在预览模式，关闭窗口
                    console.log('[PREVIEW] 🔑 ESC key pressed, closing preview window and associated toolbar');

                    if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
                        // 🔧 CRITICAL FIX: 先关闭独立工具栏
                        if (independentToolbarId) {
                            console.log('[PREVIEW] 🔑 Closing associated toolbar:', independentToolbarId);

                            // 使用Promise处理异步调用
                            window.__TAURI__.core.invoke('close_toolbar_window', {
                                toolbarId: independentToolbarId
                            }).then(() => {
                                console.log('[PREVIEW] ✅ Toolbar closed via ESC key');

                                // 然后关闭预览窗口
                                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                                return currentWindow.close();
                            }).then(() => {
                                console.log('[PREVIEW] ✅ Preview window closed via ESC key');
                            }).catch(error => {
                                console.error('[PREVIEW] ❌ Failed to close windows via ESC:', error);

                                // 如果关闭工具栏失败，仍然尝试关闭预览窗口
                                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                                currentWindow.close().catch(err => {
                                    console.error('[PREVIEW] ❌ Failed to close preview window:', err);
                                });
                            });
                        } else {
                            // 没有工具栏，直接关闭预览窗口
                            const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                            currentWindow.close().then(() => {
                                console.log('[PREVIEW] ✅ Preview window closed via ESC key');
                            }).catch(error => {
                                console.error('[PREVIEW] ❌ Failed to close preview window via ESC:', error);
                            });
                        }
                    } else {
                        console.error('[PREVIEW] ❌ WebviewWindow API not available for ESC close');
                    }
                }
                return;
            }

            // 修饰键快捷键
            if (isModifierPressed) {
                switch (key.toLowerCase()) {
                    case 's':
                        event.preventDefault();
                        console.log('[PREVIEW] 💾 Save shortcut triggered');
                        handleAnnotationSave();
                        break;
                    case 'c':
                        event.preventDefault();
                        console.log('[PREVIEW] 📋 Copy shortcut triggered');
                        handleAnnotationCopy();
                        break;
                    case 'z':
                        event.preventDefault();
                        if (shiftKey) {
                            console.log('[PREVIEW] ↷ Redo shortcut triggered');
                            handleAnnotationRedo();
                        } else {
                            console.log('[PREVIEW] ↶ Undo shortcut triggered');
                            handleAnnotationUndo();
                        }
                        break;
                    case 'y':
                        event.preventDefault();
                        console.log('[PREVIEW] ↷ Redo shortcut triggered');
                        handleAnnotationRedo();
                        break;
                }
            } else if (isEditingMode) {
                // 编辑模式下的工具快捷键
                let toolName = null;
                switch (key.toLowerCase()) {
                    case 't':
                        toolName = 'text';
                        break;
                    case 'a':
                        toolName = 'arrow';
                        break;
                    case 'r':
                        toolName = 'rectangle';
                        break;
                    case 'c':
                        toolName = 'circle';
                        break;
                    case 'b':
                        toolName = 'brush';
                        break;
                    case 'e':
                        toolName = 'eraser';
                        break;
                    case 'l':
                        toolName = 'line';
                        break;
                    case 'm':
                        toolName = 'mosaic';
                        break;
                    case 'n':
                        toolName = 'number';
                        break;
                }

                if (toolName) {
                    event.preventDefault();
                    console.log('[PREVIEW] 🔧 Tool shortcut triggered:', toolName);
                    currentAnnotationTool = toolName;
                    updateCanvasToolMode(toolName);
                    sendToolFeedback();
                }
            }
        }

        // 拖拽功能事件处理（使用Tauri原生拖拽）
        function handleMouseDown(event) {
            // 只响应左键点击
            if (event.button !== 0) return;

            // 🔧 关键修复：在编辑模式下禁用窗口拖拽
            if (isEditingMode) {
                console.log('[PREVIEW] 🎨 Editing mode active - window dragging disabled');
                return; // 直接返回，不处理拖拽
            }

            console.log('[PREVIEW] 🖱️ Mouse down detected, starting Tauri native drag');

            // 使用Tauri原生拖拽API
            if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                try {
                    currentWindow.startDragging().then(() => {
                        console.log('[PREVIEW] ✅ Tauri drag completed successfully');
                    }).catch(error => {
                        console.warn('[PREVIEW] ⚠️ Tauri drag failed:', error);
                    });
                } catch (error) {
                    console.warn('[PREVIEW] ⚠️ Failed to start Tauri drag:', error);
                }
            }

            // 添加视觉反馈
            const previewImage = document.getElementById('previewImage');
            const previewWrapper = document.getElementById('previewWrapper');
            if (previewImage) previewImage.classList.add('dragging');
            if (previewWrapper) previewWrapper.classList.add('dragging');

            // 阻止默认行为和事件冒泡
            event.preventDefault();
            event.stopPropagation();
        }

        function handleMouseMove(event) {
            // Tauri原生拖拽不需要手动处理鼠标移动
            // 这个函数保留用于其他可能的鼠标移动逻辑
        }

        function handleMouseUp(event) {
            console.log('[PREVIEW] 🖱️ Mouse up detected, ending drag operation');

            isDragging = false;

            // 移除视觉反馈
            const previewImage = document.getElementById('previewImage');
            const previewWrapper = document.getElementById('previewWrapper');
            if (previewImage) previewImage.classList.remove('dragging');
            if (previewWrapper) previewWrapper.classList.remove('dragging');

            event.preventDefault();
            event.stopPropagation();
        }

        // 初始化
        async function initialize() {
            console.log('[PREVIEW] 🚀 Initializing preview window');

            // 添加键盘快捷键事件监听
            document.addEventListener('keydown', handleKeyboardShortcuts);
            console.log('[PREVIEW] ✅ Keyboard shortcuts event listener added');

            // 🆕 添加调试快捷键
            addDebugKeyboardShortcuts();
            console.log('[PREVIEW] 🐛 Debug keyboard shortcuts added (Ctrl+Shift+D for history debug)');

            // 🆕 设置智能焦点管理
            setupSmartFocusManagement();
            console.log('[PREVIEW] 🎯 Smart focus management initialized');

            // 添加拖拽事件监听
            document.addEventListener('mousedown', handleMouseDown);
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            // 也监听全局鼠标事件，确保在窗口外释放鼠标时也能结束拖拽
            window.addEventListener('mouseup', handleMouseUp);
            console.log('[PREVIEW] ✅ Drag event listeners added');

            // 确保窗口可以接收键盘事件
            window.focus();
            document.body.tabIndex = 0;
            document.body.focus();
            console.log('[PREVIEW] ✅ Window focus set for keyboard events');

            // 🆕 初始化撤销重做管理器
            initializeUndoRedoManager();

            // 🆕 初始化独立工具栏通信
            await initializeIndependentToolbarCommunication();

            // 只有在独立工具栏模式失败时才初始化嵌入式工具栏
            if (!isIndependentToolbarMode) {
                // 显示嵌入式工具栏
                const embeddedToolbar = document.getElementById('toolbarContainer');
                if (embeddedToolbar) {
                    embeddedToolbar.style.display = 'block';
                }
                initializeToolbar();
                console.log('[PREVIEW] ✅ Embedded toolbar initialized as fallback');
            } else {
                // 确保嵌入式工具栏隐藏
                const embeddedToolbar = document.getElementById('toolbarContainer');
                if (embeddedToolbar) {
                    embeddedToolbar.style.display = 'none';
                }
                console.log('[PREVIEW] ✅ Independent toolbar mode active, embedded toolbar disabled');
            }

            // 尝试从URL参数获取截图信息
            const urlData = getScreenshotInfoFromURL();
            if (urlData) {
                console.log('[PREVIEW] 📊 Got screenshot data from URL:', urlData);
                displayScreenshot(urlData);
                return;
            }

            // 监听来自后端的截图数据事件
            if (window.__TAURI__ && window.__TAURI__.event) {
                window.__TAURI__.event.listen('screenshot-preview-data', (event) => {
                    console.log('[PREVIEW] 📨 Received screenshot data event:', event.payload);
                    displayScreenshot(event.payload);
                });
            }

            // 如果5秒后还没有数据，显示错误
            setTimeout(() => {
                if (!screenshotData) {
                    console.error('[PREVIEW] ⏰ Timeout waiting for screenshot data');
                    showError();
                }
            }, 5000);
        }

        // ==================== 🆕 撤销重做管理器 ====================

        // 初始化撤销重做管理器
        function initializeUndoRedoManager() {
            console.log('[PREVIEW] 🔧 Initializing undo/redo manager');

            try {
                // 创建撤销重做管理器
                undoRedoManager = new UndoRedoManager(100); // 最多保存100个操作

                // 创建跨窗口同步器
                crossWindowSync = new CrossWindowUndoRedoSync(undoRedoManager, 'screenshot-preview');

                // 设置同步回调
                crossWindowSync.setOnSyncRequired((state, history, redoStack) => {
                    // 发送同步数据到工具栏窗口
                    sendUndoRedoSyncToToolbar(state, history, redoStack);
                });

                console.log('[PREVIEW] ✅ Undo/redo manager initialized');
            } catch (error) {
                console.error('[PREVIEW] ❌ Failed to initialize undo/redo manager:', error);
            }
        }

        // 发送撤销重做同步数据到工具栏
        async function sendUndoRedoSyncToToolbar(state, history, redoStack) {
            if (!isIndependentToolbarMode) return;

            try {
                await sendPreviewEvent('undo-redo-sync', {
                    state: state,
                    history: history,
                    redoStack: redoStack,
                    timestamp: Date.now()
                });

                console.log('[PREVIEW] 📤 Undo/redo sync sent to toolbar');
            } catch (error) {
                console.error('[PREVIEW] ❌ Failed to send undo/redo sync:', error);
            }
        }

        // 添加标注操作到历史记录
        function addAnnotationToHistory(annotationType, data) {
            if (!undoRedoManager) return;

            const action = AnnotationActionFactory.createAddAction(annotationType, data);
            undoRedoManager.addAction(action);

            console.log('[PREVIEW] 📝 Added annotation to history:', action);
        }

        // 移除标注操作到历史记录
        function addRemoveAnnotationToHistory(annotationType, data) {
            if (!undoRedoManager) return;

            const action = AnnotationActionFactory.createRemoveAction(annotationType, data);
            undoRedoManager.addAction(action);

            console.log('[PREVIEW] 🗑️ Added remove annotation to history:', action);
        }

        // 修改标注操作到历史记录
        function addModifyAnnotationToHistory(annotationType, newData, previousData) {
            if (!undoRedoManager) return;

            const action = AnnotationActionFactory.createModifyAction(annotationType, newData, previousData);
            undoRedoManager.addAction(action);

            console.log('[PREVIEW] ✏️ Added modify annotation to history:', action);
        }

        // 简化的撤销重做管理器实现（内嵌版本）
        class UndoRedoManager {
            constructor(maxHistorySize = 100) {
                this.history = [];
                this.redoStack = [];
                this.maxHistorySize = maxHistorySize;
                this.onStateChange = null;
            }

            setOnStateChange(callback) {
                this.onStateChange = callback;
                this.notifyStateChange();
            }

            addAction(action) {
                this.history.push(action);
                this.redoStack = [];

                if (this.history.length > this.maxHistorySize) {
                    this.history.shift();
                }

                this.notifyStateChange();
            }

            undo() {
                if (!this.canUndo()) return null;

                const action = this.history.pop();
                this.redoStack.push(action);
                this.notifyStateChange();

                return action;
            }

            redo() {
                if (!this.canRedo()) return null;

                const action = this.redoStack.pop();
                this.history.push(action);
                this.notifyStateChange();

                return action;
            }

            canUndo() {
                return this.history.length > 0;
            }

            canRedo() {
                return this.redoStack.length > 0;
            }

            getState() {
                return {
                    canUndo: this.canUndo(),
                    canRedo: this.canRedo(),
                    historyLength: this.history.length,
                    redoStackLength: this.redoStack.length,
                    currentPosition: this.history.length
                };
            }

            getHistory() {
                return [...this.history];
            }

            getRedoStack() {
                return [...this.redoStack];
            }

            restoreFromState(history, redoStack) {
                this.history = [...history];
                this.redoStack = [...redoStack];
                this.notifyStateChange();
            }

            clear() {
                this.history = [];
                this.redoStack = [];
                this.notifyStateChange();
            }

            notifyStateChange() {
                if (this.onStateChange) {
                    this.onStateChange(this.getState());
                }
            }
        }

        // 跨窗口同步器
        class CrossWindowUndoRedoSync {
            constructor(undoRedoManager, windowId) {
                this.undoRedoManager = undoRedoManager;
                this.windowId = windowId;
                this.onSyncRequired = null;

                this.undoRedoManager.setOnStateChange((state) => {
                    this.triggerSync(state);
                });
            }

            setOnSyncRequired(callback) {
                this.onSyncRequired = callback;
            }

            triggerSync(state) {
                if (this.onSyncRequired) {
                    const history = this.undoRedoManager.getHistory();
                    const redoStack = this.undoRedoManager.getRedoStack();
                    this.onSyncRequired(state, history, redoStack);
                }
            }

            receiveSync(history, redoStack) {
                this.undoRedoManager.restoreFromState(history, redoStack);
            }

            sendSync() {
                return {
                    state: this.undoRedoManager.getState(),
                    history: this.undoRedoManager.getHistory(),
                    redoStack: this.undoRedoManager.getRedoStack()
                };
            }
        }

        // 标注操作工厂
        class AnnotationActionFactory {
            static createAddAction(annotationType, data) {
                return {
                    id: `add_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    type: 'add',
                    annotationType,
                    timestamp: Date.now(),
                    data
                };
            }

            static createRemoveAction(annotationType, data) {
                return {
                    id: `remove_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    type: 'remove',
                    annotationType,
                    timestamp: Date.now(),
                    data
                };
            }

            static createModifyAction(annotationType, newData, previousData) {
                return {
                    id: `modify_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    type: 'modify',
                    annotationType,
                    timestamp: Date.now(),
                    data: newData,
                    previousData
                };
            }
        }

        // ==================== 🆕 独立工具栏通信功能 ====================

        // 初始化独立工具栏通信
        async function initializeIndependentToolbarCommunication() {
            console.log('[PREVIEW] 🔧 Initializing independent toolbar communication');

            try {
                // 检查是否支持Tauri事件系统
                if (!window.__TAURI__ || !window.__TAURI__.event) {
                    console.warn('[PREVIEW] ⚠️ Tauri event API not available, using embedded toolbar');
                    return;
                }

                // 创建预览窗口通信器
                previewCommunicator = new PreviewCommunicator('screenshot-preview');
                await previewCommunicator.initialize();

                // 监听工具栏事件
                await window.__TAURI__.event.listen('toolbar-event', (event) => {
                    handleToolbarEvent(event.payload);
                });

                // 监听工具栏初始化事件
                await window.__TAURI__.event.listen('toolbar-init', (event) => {
                    handleToolbarInit(event.payload);
                });

                // 🆕 监听来自后端的工具选择事件
                await window.__TAURI__.event.listen('preview-tool-selection', (event) => {
                    handleBackendToolSelection(event.payload);
                });

                isIndependentToolbarMode = true;
                console.log('[PREVIEW] ✅ Independent toolbar communication initialized');

                // 注意：独立工具栏将由后端在预览窗口创建时自动创建
                // 这里不需要手动创建，避免重复创建

            } catch (error) {
                console.error('[PREVIEW] ❌ Failed to initialize independent toolbar communication:', error);
                console.log('[PREVIEW] 🔄 Falling back to embedded toolbar mode');
                isIndependentToolbarMode = false;
            }
        }

        // 创建独立工具栏窗口
        async function createIndependentToolbarWindow() {
            try {
                // 获取当前预览窗口的位置和尺寸
                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                const position = await currentWindow.outerPosition();
                const size = await currentWindow.outerSize();
                const monitor = await currentWindow.currentMonitor();

                const previewBounds = {
                    id: 'screenshot-preview',
                    x: position.x,
                    y: position.y,
                    width: size.width,
                    height: size.height
                };

                const screenInfo = {
                    width: monitor.size.width,
                    height: monitor.size.height
                };

                console.log('[PREVIEW] 📊 Preview bounds:', previewBounds);
                console.log('[PREVIEW] 📊 Screen info:', screenInfo);

                // 调用后端创建独立工具栏
                const toolbarId = await window.__TAURI__.core.invoke('create_independent_toolbar_window', {
                    previewWindowId: 'screenshot-preview',
                    previewBounds: previewBounds,
                    screenInfo: screenInfo
                });

                independentToolbarId = toolbarId;
                console.log('[PREVIEW] ✅ Independent toolbar created:', toolbarId);

                // 隐藏嵌入式工具栏
                const embeddedToolbar = document.getElementById('toolbarContainer');
                if (embeddedToolbar) {
                    embeddedToolbar.style.display = 'none';
                }

            } catch (error) {
                console.error('[PREVIEW] ❌ Failed to create independent toolbar:', error);
                isIndependentToolbarMode = false;
            }
        }

        // 处理工具栏事件
        function handleToolbarEvent(event) {
            console.log('[PREVIEW] 📨 Received toolbar event:', event);

            switch (event.type) {
                case 'toolbar-connected':
                    handleToolbarConnected(event);
                    break;
                case 'toolbar-disconnected':
                    handleToolbarDisconnected(event);
                    break;
                case 'tool-selected':
                    handleToolSelected(event.data.tool);
                    break;
                case 'drawing-tool-selected':
                    // 🔧 CRITICAL FIX: Handle drawing tool selection from independent toolbar
                    console.log('[PREVIEW] 🎨 Received drawing tool selection:', event.data);
                    handleToolSelected(event.data.tool);
                    break;
                case 'annotation-mode-start':
                    handleAnnotationModeStart(event.data);
                    break;
                case 'action-undo':
                    handleAnnotationUndo();
                    break;
                case 'action-redo':
                    handleAnnotationRedo();
                    break;
                case 'action-save':
                    handleAnnotationSave();
                    break;
                case 'action-copy':
                    handleAnnotationCopy();
                    break;
                case 'action-close':
                    handleAnnotationClose();
                    break;
            }
        }

        // 处理工具栏初始化事件
        function handleToolbarInit(event) {
            console.log('[PREVIEW] 🔧 Toolbar initialization event:', event);
            independentToolbarId = event.toolbarId;
        }

        // 处理工具栏连接
        function handleToolbarConnected(event) {
            console.log('[PREVIEW] ✅ Toolbar connected:', event.toolbarId);

            // 发送连接确认
            sendPreviewEvent('connection-ack', {
                windowId: 'screenshot-preview',
                capabilities: ['annotation', 'undo', 'redo', 'save', 'copy'],
                timestamp: Date.now()
            });
        }

        // 处理工具栏断开连接
        function handleToolbarDisconnected(event) {
            console.log('[PREVIEW] 🔌 Toolbar disconnected:', event.toolbarId);
            independentToolbarId = null;
        }

        // 🆕 处理来自后端的工具选择事件
        function handleBackendToolSelection(eventData) {
            console.log('[PREVIEW] 🎨 Received tool selection from backend:', eventData);

            const selectedTool = eventData.selectedTool;

            if (!selectedTool) {
                console.warn('[PREVIEW] ⚠️ No tool specified in backend tool selection event');
                return;
            }

            console.log('[PREVIEW] 🎨 Auto-starting annotation mode with tool:', selectedTool);

            // 自动启动注解模式
            handleAnnotationModeStart({
                tool: selectedTool,
                style: {
                    color: '#ff0000',
                    strokeWidth: 2,
                    fontSize: 16,
                    fontFamily: 'Arial'
                },
                autoStart: true,
                source: 'backend'
            });
        }

        // 🆕 处理注解模式启动
        async function handleAnnotationModeStart(eventData) {
            console.log('[PREVIEW] 🎨 Annotation mode start requested:', eventData);

            try {
                // 检查是否已经在编辑模式
                if (isEditingMode) {
                    console.log('[PREVIEW] 🎨 Already in editing mode, switching tool to:', eventData.tool);
                    handleToolSelected(eventData.tool);
                    return;
                }

                // 进入编辑模式
                console.log('[PREVIEW] 🎨 Switching to annotation editing mode');
                switchToEditingMode();

                // 设置选中的工具
                if (eventData.tool) {
                    currentAnnotationTool = eventData.tool;
                    updateCanvasToolMode(eventData.tool);
                }

                // 应用工具样式
                if (eventData.style) {
                    applyAnnotationStyle(eventData.style);
                }

                // 发送注解准备就绪事件
                sendPreviewEvent('annotation-ready', {
                    windowId: 'screenshot-preview',
                    capabilities: {
                        supportedTools: ['text', 'arrow', 'rectangle', 'circle', 'brush'],
                        maxAnnotations: 100,
                        supportsPressure: false,
                        supportsLayers: false,
                        supportsUndo: true,
                        supportsExport: ['png', 'jpg']
                    },
                    currentTool: currentAnnotationTool,
                    timestamp: Date.now()
                });

                console.log('[PREVIEW] 🎨 Annotation mode started successfully');

            } catch (error) {
                console.error('[PREVIEW] 🎨 Failed to start annotation mode:', error);

                // 发送错误事件
                sendPreviewEvent('annotation-mode-failed', {
                    error: error.message,
                    timestamp: Date.now()
                });
            }
        }

        // 🆕 应用注解样式
        function applyAnnotationStyle(style) {
            if (!style) return;

            console.log('[PREVIEW] 🎨 Applying annotation style:', style);

            // 更新全局样式变量
            if (style.strokeColor) {
                currentColor = style.strokeColor;
            }
            if (style.strokeWidth) {
                currentLineWidth = style.strokeWidth;
            }
            if (style.fontSize) {
                currentFontSize = style.fontSize;
            }
            if (style.fontFamily) {
                currentFontFamily = style.fontFamily;
            }
        }

        // 🚀 处理工具选择（无缝切换优化）
        function handleToolSelected(toolName) {
            console.log('[PREVIEW] 🔧 Tool selected:', toolName);

            const previousTool = currentAnnotationTool;
            currentAnnotationTool = toolName;

            // 🔧 CRITICAL FIX: 同步更新currentTool变量，确保一致性
            currentTool = toolName;

            console.log('[PREVIEW] 🎨 Tool synchronized:', {
                previousTool: previousTool,
                newTool: toolName,
                currentAnnotationTool: currentAnnotationTool,
                currentTool: currentTool
            });

            // 🆕 立即更新光标
            updateCursorForAnnotationTool();

            // 检查是否是标注工具（非操作工具）
            const annotationTools = ['text', 'arrow', 'rectangle', 'circle', 'brush', 'pen', 'line', 'ellipse', 'mosaic', 'number', 'eraser'];

            if (annotationTools.includes(toolName)) {
                // 🚀 无缝切换：只在需要时切换到编辑模式
                if (!isEditingMode) {
                    console.log('[PREVIEW] 🎨 Switching to editing mode for tool:', toolName);
                    switchToEditingMode();
                } else {
                    console.log('[PREVIEW] 🔄 Seamless tool switch from', previousTool, 'to', toolName);
                    // 已在编辑模式，只需更新工具状态
                    updateToolStateInEditingMode(toolName, previousTool);
                }
            }

            // 更新Canvas工具模式
            updateCanvasToolMode(toolName);

            // 发送工具反馈
            sendToolFeedback();
        }

        // 🚀 在编辑模式下更新工具状态（无缝切换）
        function updateToolStateInEditingMode(newTool, previousTool) {
            // 清除当前正在进行的标注操作
            if (currentShape) {
                console.log('[PREVIEW] 🔄 Clearing current shape for tool switch');
                currentShape = null;
                annotationStartPos = null;

                // 重绘Canvas以清除预览形状
                markDirty();
                redrawAnnotationCanvas();
            }

            // 重置工具特定的状态
            resetToolSpecificState(previousTool);

            // 更新光标样式
            updateCursorForTool(newTool);

            // 提供用户反馈
            showAnnotationFeedback(`🔄 已切换到 ${getToolDisplayName(newTool)} 工具`);
        }

        // 重置工具特定状态
        function resetToolSpecificState(tool) {
            switch (tool) {
                case 'brush':
                case 'pen':
                    // 重置画笔状态
                    if (currentShape && currentShape.points) {
                        currentShape.points = [];
                    }
                    break;
                case 'text':
                    // 重置文字输入状态
                    const textInput = document.getElementById('textInput');
                    if (textInput) {
                        textInput.style.display = 'none';
                    }
                    break;
                case 'eraser':
                    // 重置橡皮擦状态
                    isErasing = false;
                    eraserStartPos = null;
                    break;
                default:
                    // 其他工具无需特殊重置
                    break;
            }
        }

        // 获取工具显示名称
        function getToolDisplayName(tool) {
            const toolNames = {
                'rectangle': '矩形',
                'circle': '圆形',
                'ellipse': '椭圆',
                'line': '直线',
                'arrow': '箭头',
                'brush': '画笔',
                'pen': '钢笔',
                'text': '文字',
                'number': '序号',
                'eraser': '橡皮擦',
                'mosaic': '马赛克'
            };
            return toolNames[tool] || tool;
        }

        // 切换到编辑模式
        function switchToEditingMode() {
            if (isEditingMode) {
                console.log('[PREVIEW] ⚠️ Already in editing mode, skipping transition');
                return;
            }

            console.log('[PREVIEW] 🎨 Switching to editing mode');

            windowState = 'editing';
            isEditingMode = true;

            // 更新UI视觉反馈
            const previewWrapper = document.getElementById('previewWrapper');
            if (previewWrapper) {
                previewWrapper.classList.add('editing-mode');

                // 🔧 关键修复：在编辑模式下移除拖拽区域属性
                previewWrapper.removeAttribute('data-tauri-drag-region');
                console.log('[PREVIEW] 🔒 Window dragging disabled for editing mode');
            }

            // 创建或激活标注层
            createAnnotationLayer();

            // 🔧 记录初始状态到撤销历史（如果还没有记录）
            if (annotationHistory.length === 0) {
                recordAnnotationAction('init', { message: 'Initial state' });
                console.log('[PREVIEW] 📚 Initial state recorded for undo system');
            }

            // 发送状态变更事件
            sendPreviewEvent('mode-changed', {
                mode: 'editing',
                tool: currentAnnotationTool,
                timestamp: Date.now()
            });

            console.log('[PREVIEW] ✅ Switched to editing mode successfully');
        }

        // 切换到预览模式
        function switchToPreviewMode() {
            if (!isEditingMode) {
                console.log('[PREVIEW] ⚠️ Already in preview mode, skipping transition');
                return;
            }

            console.log('[PREVIEW] 👁️ Switching to preview mode');

            windowState = 'preview';
            isEditingMode = false;
            currentAnnotationTool = null;

            // 更新UI视觉反馈
            const previewWrapper = document.getElementById('previewWrapper');
            if (previewWrapper) {
                previewWrapper.classList.remove('editing-mode');

                // 🔧 关键修复：在预览模式下恢复拖拽区域属性
                previewWrapper.setAttribute('data-tauri-drag-region', '');
                console.log('[PREVIEW] 🔓 Window dragging enabled for preview mode');
            }

            // 停用标注层
            deactivateAnnotationLayer();

            // 发送状态变更事件
            sendPreviewEvent('mode-changed', {
                mode: 'preview',
                timestamp: Date.now()
            });

            console.log('[PREVIEW] ✅ Switched to preview mode successfully');
        }

        // 创建标注层
        function createAnnotationLayer() {
            const previewWrapper = document.getElementById('previewWrapper');
            if (!previewWrapper) {
                console.error('[PREVIEW] ❌ Preview wrapper not found');
                return;
            }

            // 检查是否已存在标注层
            let annotationLayer = document.getElementById('annotationLayer');
            if (!annotationLayer) {
                annotationLayer = document.createElement('div');
                annotationLayer.id = 'annotationLayer';
                annotationLayer.className = 'annotation-layer';
                previewWrapper.appendChild(annotationLayer);
                console.log('[PREVIEW] ✅ Annotation layer created');
            }

            // 🎨 创建Canvas绘图层
            createAnnotationCanvas();

            // 激活标注层
            annotationLayer.classList.add('active');

            // 根据工具设置光标样式
            updateAnnotationLayerCursor(currentAnnotationTool);

            // 绑定鼠标事件
            bindAnnotationEvents(annotationLayer);
        }

        // 🎨 创建Canvas绘图层
        function createAnnotationCanvas() {
            const previewWrapper = document.getElementById('previewWrapper');
            const previewCanvas = document.getElementById('previewCanvas');

            if (!previewWrapper || !previewCanvas) {
                console.error('[PREVIEW] ❌ Required elements not found for annotation canvas');
                return;
            }

            // 检查是否已存在绘图Canvas
            let annotationCanvas = document.getElementById('annotationCanvas');
            if (!annotationCanvas) {
                annotationCanvas = document.createElement('canvas');
                annotationCanvas.id = 'annotationCanvas';
                annotationCanvas.style.cssText = `
                    position: absolute;
                    top: 0px;
                    left: 0px;
                    pointer-events: none;
                    z-index: 10;
                    border-radius: 4px;
                `;
                previewWrapper.appendChild(annotationCanvas);
                console.log('[PREVIEW] 🎨 Annotation canvas created');
            }

            // 设置Canvas尺寸与预览Canvas一致
            const rect = previewCanvas.getBoundingClientRect();
            annotationCanvas.width = previewCanvas.width;
            annotationCanvas.height = previewCanvas.height;
            annotationCanvas.style.width = previewCanvas.style.width;
            annotationCanvas.style.height = previewCanvas.style.height;

            // 获取绘图上下文
            annotationContext = annotationCanvas.getContext('2d');
            annotationContext.lineCap = 'round';
            annotationContext.lineJoin = 'round';

            // 计算缩放比例
            canvasScale = previewCanvas.width / parseFloat(previewCanvas.style.width);

            console.log('[PREVIEW] 🎨 Annotation canvas initialized:', {
                width: annotationCanvas.width,
                height: annotationCanvas.height,
                scale: canvasScale,
                canvasActual: `${previewCanvas.width}x${previewCanvas.height}`,
                canvasCSS: `${previewCanvas.style.width}x${previewCanvas.style.height}`,
                DPR: window.devicePixelRatio
            });
        }

        // 停用标注层
        function deactivateAnnotationLayer() {
            const annotationLayer = document.getElementById('annotationLayer');
            if (annotationLayer) {
                annotationLayer.classList.remove('active');
                // 移除工具特定的样式类
                annotationLayer.className = 'annotation-layer';
                console.log('[PREVIEW] 🔒 Annotation layer deactivated');
            }

            // 🔧 关键修复：清除标注Canvas，避免预览模式下显示标注
            if (annotationContext) {
                annotationContext.clearRect(0, 0, annotationContext.canvas.width, annotationContext.canvas.height);
                console.log('[PREVIEW] 🧹 Annotation canvas cleared for preview mode');
            }
        }

        // 更新标注层光标样式
        function updateAnnotationLayerCursor(toolName) {
            const annotationLayer = document.getElementById('annotationLayer');
            if (!annotationLayer) {
                console.warn('[PREVIEW] ⚠️ Annotation layer not found for cursor update');
                return;
            }

            // 移除所有工具样式类
            annotationLayer.classList.remove('tool-text', 'tool-arrow', 'tool-rectangle', 'tool-circle', 'tool-brush', 'tool-eraser', 'tool-ellipse', 'tool-line', 'tool-pen');

            // 添加当前工具样式类
            if (toolName) {
                annotationLayer.classList.add(`tool-${toolName}`);

                // 🔧 关键修复：直接设置CSS光标样式，确保即时生效
                const cursorStyle = getCursorForTool(toolName);
                annotationLayer.style.cursor = cursorStyle;

                console.log('[PREVIEW] 🎯 Cursor updated for tool:', toolName, 'style:', cursorStyle);
            }
        }

        // 绑定标注事件
        function bindAnnotationEvents(annotationLayer) {
            // 移除旧的事件监听器
            annotationLayer.removeEventListener('mousedown', handleAnnotationMouseDown);
            annotationLayer.removeEventListener('mousemove', handleAnnotationMouseMove);
            annotationLayer.removeEventListener('mouseup', handleAnnotationMouseUp);
            annotationLayer.removeEventListener('mouseenter', handleAnnotationMouseEnter);
            annotationLayer.removeEventListener('mouseleave', handleAnnotationMouseLeave);

            // 绑定新的事件监听器，使用capture模式确保优先处理
            annotationLayer.addEventListener('mousedown', handleAnnotationMouseDown, { capture: true });
            annotationLayer.addEventListener('mousemove', handleAnnotationMouseMove, { capture: true });
            annotationLayer.addEventListener('mouseup', handleAnnotationMouseUp, { capture: true });

            // 🔧 关键修复：添加鼠标进入/离开事件来即时更新光标
            annotationLayer.addEventListener('mouseenter', handleAnnotationMouseEnter, { capture: true });
            annotationLayer.addEventListener('mouseleave', handleAnnotationMouseLeave, { capture: true });

            // 确保标注层在最高层级
            annotationLayer.style.zIndex = '1000';
            annotationLayer.style.pointerEvents = 'all';

            console.log('[PREVIEW] 🎯 Annotation events bound with capture mode and cursor triggers');
        }

        // 更新Canvas工具模式
        function updateCanvasToolMode(toolName) {
            console.log('[PREVIEW] 🎨 Updating canvas tool mode to:', toolName);

            // 更新标注层光标样式
            updateAnnotationLayerCursor(toolName);

            // 这里可以集成实际的Canvas/Fabric.js工具切换逻辑
            switch (toolName) {
                case 'text':
                    console.log('[PREVIEW] 📝 Text tool activated');
                    break;
                case 'arrow':
                    console.log('[PREVIEW] ↗️ Arrow tool activated');
                    break;
                case 'rectangle':
                    console.log('[PREVIEW] ⬜ Rectangle tool activated');
                    break;
                case 'circle':
                case 'ellipse':
                    console.log('[PREVIEW] ⭕ Circle/Ellipse tool activated');
                    break;
                case 'brush':
                case 'pen':
                    console.log('[PREVIEW] ✏️ Brush/Pen tool activated');
                    break;
                case 'eraser':
                    console.log('[PREVIEW] 🧽 Eraser tool activated');
                    break;
                default:
                    console.log('[PREVIEW] 🔧 Tool activated:', toolName);
            }
        }

        // 发送预览事件到工具栏
        async function sendPreviewEvent(type, data) {
            if (!window.__TAURI__ || !window.__TAURI__.event) {
                console.warn('[PREVIEW] ⚠️ Tauri event API not available');
                return;
            }

            try {
                await window.__TAURI__.event.emit('preview-event', {
                    type: type,
                    data: data,
                    timestamp: Date.now(),
                    windowId: 'screenshot-preview'
                });

                console.log('[PREVIEW] 📤 Event sent to toolbar:', type, data);
            } catch (error) {
                console.error('[PREVIEW] ❌ Failed to send event to toolbar:', error);
            }
        }

        // 标注事件处理
        let isAnnotating = false;
        let annotationStartPos = null;

        // 🔧 关键修复：鼠标进入标注区域时即时更新光标
        function handleAnnotationMouseEnter(event) {
            if (!isEditingMode || !currentAnnotationTool) return;

            console.log('[PREVIEW] 🎯 Mouse entered annotation area - updating cursor for tool:', currentAnnotationTool);

            // 强制更新光标样式
            updateAnnotationLayerCursor(currentAnnotationTool);

            // 确保标注层处于激活状态
            const annotationLayer = document.getElementById('annotationLayer');
            if (annotationLayer) {
                annotationLayer.classList.add('active');
                // 强制重新应用光标样式
                annotationLayer.style.cursor = getCursorForTool(currentAnnotationTool);
            }
        }

        // 鼠标离开标注区域
        function handleAnnotationMouseLeave(event) {
            if (!isEditingMode) return;

            console.log('[PREVIEW] 🎯 Mouse left annotation area');
            // 可以在这里添加离开时的处理逻辑
        }

        // 🚀 更新工具光标样式（无缝切换优化）
        function updateCursorForTool(toolName) {
            const annotationLayer = document.getElementById('annotationLayer');
            if (!annotationLayer) return;

            const cursorStyle = getCursorForTool(toolName);
            annotationLayer.style.cursor = cursorStyle;

            console.log('[PREVIEW] 🎯 Cursor updated for seamless tool switch:', toolName, 'style:', cursorStyle);
        }

        // 获取工具对应的光标样式
        function getCursorForTool(toolName) {
            switch (toolName) {
                case 'text':
                    return 'text';
                case 'arrow':
                case 'rectangle':
                case 'circle':
                case 'ellipse':
                case 'line':
                case 'brush':
                case 'pen':
                case 'number':
                case 'mosaic':
                    return 'crosshair';
                case 'eraser':
                    return 'url("data:image/svg+xml;utf8,<svg xmlns=\\"http://www.w3.org/2000/svg\\" width=\\"20\\" height=\\"20\\" viewBox=\\"0 0 24 24\\"><path fill=\\"%23ff6b35\\" d=\\"M16.24 3.56l4.95 4.94c.78.79.78 2.05 0 2.84L12 20.53a4.008 4.008 0 0 1-5.66 0L2.81 17c-.78-.79-.78-2.05 0-2.84l10.6-10.6c.79-.78 2.05-.78 2.83 0M4.22 15.58l3.54 3.53c.78.79 2.04.79 2.83 0l3.53-3.53l-4.95-4.95l-4.95 4.95Z\\"/></svg>") 10 10, auto';
                default:
                    return 'default';
            }
        }

        function handleAnnotationMouseDown(event) {
            if (!isEditingMode || !currentAnnotationTool) return;

            // 🔧 关键修复：阻止事件冒泡和默认行为
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();

            console.log('[PREVIEW] 🎯 Annotation mouse down:', currentAnnotationTool, 'at', event.offsetX, event.offsetY);

            // 🆕 即时坐标对齐验证
            const annotationLayer = document.getElementById('annotationLayer');
            const previewCanvas = document.getElementById('previewCanvas');
            if (annotationLayer && previewCanvas) {
                const layerRect = annotationLayer.getBoundingClientRect();
                const canvasRect = previewCanvas.getBoundingClientRect();
                const offsetX = layerRect.left - canvasRect.left;
                const offsetY = layerRect.top - canvasRect.top;
                console.log('[PREVIEW] 🔍 Layer alignment check:', {
                    layerOffset: `${offsetX}px, ${offsetY}px`,
                    isAligned: offsetX === 0 && offsetY === 0
                });
            }

            isAnnotating = true;
            annotationStartPos = {
                x: event.offsetX,
                y: event.offsetY
            };

            // 根据工具类型处理开始事件
            switch (currentAnnotationTool) {
                case 'text':
                    handleTextAnnotationStart(event);
                    break;
                case 'arrow':
                case 'rectangle':
                case 'circle':
                case 'ellipse':
                case 'line':
                case 'mosaic':
                    handleShapeAnnotationStart(event);
                    break;
                case 'number':
                    handleNumberAnnotationStart(event);
                    break;
                case 'brush':
                case 'pen':
                    handleBrushAnnotationStart(event);
                    break;
                case 'eraser':
                    handleEraserAnnotationStart(event);
                    break;
                default:
                    console.log('[PREVIEW] 🔧 Unhandled tool:', currentAnnotationTool);
            }
        }

        function handleAnnotationMouseMove(event) {
            if (!isAnnotating || !isEditingMode || !currentAnnotationTool) return;

            // 🔧 阻止事件冒泡
            event.preventDefault();
            event.stopPropagation();

            // 根据工具类型处理移动事件
            switch (currentAnnotationTool) {
                case 'arrow':
                case 'rectangle':
                case 'circle':
                case 'ellipse':
                case 'line':
                    handleShapeAnnotationMove(event);
                    break;
                case 'brush':
                case 'pen':
                    handleBrushAnnotationMove(event);
                    break;
                case 'eraser':
                    handleEraserAnnotationMove(event);
                    break;
            }
        }

        function handleAnnotationMouseUp(event) {
            if (!isAnnotating || !isEditingMode || !currentAnnotationTool) return;

            // 🔧 阻止事件冒泡
            event.preventDefault();
            event.stopPropagation();

            console.log('[PREVIEW] 🎯 Annotation mouse up:', currentAnnotationTool);

            isAnnotating = false;

            // 根据工具类型处理结束事件
            switch (currentAnnotationTool) {
                case 'arrow':
                case 'rectangle':
                case 'circle':
                case 'ellipse':
                case 'line':
                    handleShapeAnnotationEnd(event);
                    break;
                case 'brush':
                case 'pen':
                    handleBrushAnnotationEnd(event);
                    break;
                case 'eraser':
                    handleEraserAnnotationEnd(event);
                    break;
            }

            annotationStartPos = null;
        }

        // 🎨 实际的图形绘制实现
        function handleTextAnnotationStart(event) {
            console.log('[PREVIEW] 📝 Text annotation started at:', event.offsetX, event.offsetY);

            // 创建文字输入框
            createTextInput(event.offsetX, event.offsetY);
            showAnnotationFeedback('📝 请输入文字内容');
        }

        // 创建文字输入框
        function createTextInput(x, y) {
            // 移除已存在的输入框
            const existingInput = document.getElementById('textInput');
            if (existingInput) {
                existingInput.remove();
            }

            // 创建输入框
            const input = document.createElement('input');
            input.id = 'textInput';
            input.type = 'text';
            input.placeholder = '输入文字...';
            input.style.cssText = `
                position: absolute;
                left: ${x}px;
                top: ${y}px;
                z-index: 1001;
                padding: 4px 8px;
                border: 2px solid #ff6b35;
                border-radius: 4px;
                background: white;
                font-size: 16px;
                font-family: Arial, sans-serif;
                color: ${currentColor || '#ff0000'};
                min-width: 100px;
                outline: none;
            `;

            // 添加到标注层
            const annotationLayer = document.getElementById('annotationLayer');
            if (annotationLayer) {
                annotationLayer.appendChild(input);
                input.focus();

                // 监听回车键确认
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        confirmTextInput(input, x, y);
                    } else if (e.key === 'Escape') {
                        cancelTextInput(input);
                    }
                });

                // 监听失去焦点
                input.addEventListener('blur', () => {
                    setTimeout(() => confirmTextInput(input, x, y), 100);
                });
            }
        }

        // 确认文字输入
        function confirmTextInput(input, x, y) {
            const text = input.value.trim();
            if (text) {
                // 🔧 修复：存储CSS坐标，与形状工具保持一致
                // 创建文字标注
                const textShape = {
                    type: 'text',
                    x: x,
                    y: y,
                    text: text,
                    color: currentColor || '#ff0000',
                    fontSize: 16,
                    fontFamily: 'Arial'
                };

                // 添加到形状列表
                shapes.push(textShape);

                // 🔧 CRITICAL FIX: 使用新的撤销系统记录文本操作
                if (undoRedoManager) {
                    addAnnotationToHistory('text', textShape);
                }

                // 重绘Canvas
                redrawAnnotationCanvas();

                showAnnotationFeedback(`✅ 文字标注已完成: "${text}"`);
                console.log('[PREVIEW] 🎨 Text annotation added:', textShape);

                // 🔧 发送工具反馈更新
                sendToolFeedback();
            }

            input.remove();
        }

        // 取消文字输入
        function cancelTextInput(input) {
            input.remove();
            showAnnotationFeedback('📝 文字输入已取消');
        }

        // 🔢 序号标注处理
        function handleNumberAnnotationStart(event) {
            console.log('[PREVIEW] 🔢 Number annotation started at:', event.offsetX, event.offsetY);

            // 🔧 修复：存储CSS坐标，与形状工具保持一致
            // 创建序号标注
            const numberShape = {
                type: 'number',
                x: event.offsetX,
                y: event.offsetY,
                number: numberCounter,
                color: currentColor || '#ff0000',
                backgroundColor: '#ffffff',
                fontSize: 16,
                radius: 15
            };

            // 添加到形状列表
            shapes.push(numberShape);

            // 重绘Canvas
            redrawAnnotationCanvas();

            // 增加计数器
            numberCounter++;

            showAnnotationFeedback(`🔢 序号标注已完成: ${numberShape.number}`);
            console.log('[PREVIEW] 🎨 Number annotation added:', numberShape);

            // 记录到撤销历史
            recordAnnotationAction('add', numberShape);
        }

        function handleShapeAnnotationStart(event) {
            console.log('[PREVIEW] 🔷 Shape annotation started:', currentAnnotationTool);

            // 🆕 调试坐标系统
            debugCoordinateSystem(event, 'START');

            // 🔧 修复：直接使用CSS坐标，不进行DPR缩放转换
            // 因为标注应该基于用户看到的CSS尺寸，而不是物理像素尺寸
            currentShape = {
                type: currentAnnotationTool,
                startX: event.offsetX,
                startY: event.offsetY,
                endX: event.offsetX,
                endY: event.offsetY,
                color: currentColor || '#ff0000',
                strokeWidth: 3
            };

            console.log('[PREVIEW] 🔧 Shape start coordinates (CSS):', event.offsetX, event.offsetY);
            showAnnotationFeedback(`🔷 ${currentAnnotationTool}标注已开始 - 拖拽以绘制形状`);
        }

        function handleShapeAnnotationMove(event) {
            if (!currentShape || !annotationStartPos) return;

            // 🔧 修复：直接使用CSS坐标，保持坐标系统一致性
            currentShape.endX = event.offsetX;
            currentShape.endY = event.offsetY;

            // 🚀 修复：使用即时重绘确保流畅的预览体验
            redrawAnnotationCanvasImmediate();

            // 绘制当前正在拖拽的形状
            drawShape(currentShape, true);

            const width = Math.abs(event.offsetX - annotationStartPos.x);
            const height = Math.abs(event.offsetY - annotationStartPos.y);
            console.log('[PREVIEW] 🔷 Shape dragging:', currentAnnotationTool, 'size:', width + 'x' + height, 'coords:', event.offsetX, event.offsetY);
        }

        function handleShapeAnnotationEnd(event) {
            console.log('[PREVIEW] 🔷 Shape annotation completed:', currentAnnotationTool);

            if (currentShape && annotationStartPos) {
                // 🔧 修复：完成形状绘制，使用CSS坐标
                currentShape.endX = event.offsetX;
                currentShape.endY = event.offsetY;

                const width = Math.abs(event.offsetX - annotationStartPos.x);
                const height = Math.abs(event.offsetY - annotationStartPos.y);

                console.log('[PREVIEW] 🔧 Final shape coordinates (CSS):', {
                    start: [currentShape.startX, currentShape.startY],
                    end: [currentShape.endX, currentShape.endY],
                    size: [width, height],
                    tool: currentAnnotationTool
                });

                // 🔧 ENHANCED DEBUG: 特别调试箭头工具
                if (currentAnnotationTool === 'arrow') {
                    const length = Math.sqrt(width * width + height * height);
                    console.log('[PREVIEW] 🏹 Arrow tool debug:', {
                        width: width,
                        height: height,
                        length: length,
                        minSize: MIN_SHAPE_SIZE,
                        isValid: length >= MIN_SHAPE_SIZE
                    });
                }

                // 🔧 检查形状有效性
                if (isValidShape(currentShape, width, height)) {
                    // 创建形状副本
                    const newShape = { ...currentShape };

                    // 添加到形状列表
                    shapes.push(newShape);

                    // 🔧 CRITICAL FIX: 使用新的撤销系统记录操作
                    if (undoRedoManager) {
                        addAnnotationToHistory(currentAnnotationTool, newShape);
                    }

                    // 重绘所有形状
                    redrawAnnotationCanvas();

                    showAnnotationFeedback(`✅ ${currentAnnotationTool}标注已完成 - 尺寸: ${width}x${height}`);
                    console.log('[PREVIEW] 🎨 Shape added to canvas:', newShape);

                    // 🔧 发送工具反馈更新
                    sendToolFeedback();
                } else {
                    // 无效形状，清除预览
                    redrawAnnotationCanvas();
                    console.log('[PREVIEW] ⚠️ Shape too small, ignored:', width + 'x' + height);
                    showAnnotationFeedback(`⚠️ 标注尺寸过小，已忽略`);
                }
            }

            currentShape = null;
        }

        // 🔧 检查形状有效性
        function isValidShape(shape, width, height) {
            if (!shape) return false;

            switch (shape.type) {
                case 'rectangle':
                case 'circle':
                case 'ellipse':
                    // 矩形和圆形需要最小面积
                    return width >= MIN_SHAPE_SIZE && height >= MIN_SHAPE_SIZE;
                case 'arrow':
                case 'line':
                    // 线条需要最小长度
                    const length = Math.sqrt(width * width + height * height);
                    return length >= MIN_SHAPE_SIZE;
                case 'brush':
                case 'pen':
                    // 画笔需要检查笔画长度
                    return shape.points && shape.points.length >= MIN_BRUSH_STROKE_LENGTH;
                default:
                    return true;
            }
        }

        // 🎨 Canvas绘图函数
        // 🚀 性能优化的重绘函数（用于非实时操作）
        function redrawAnnotationCanvas() {
            if (!annotationContext) return;

            // 使用requestAnimationFrame进行渲染节流
            if (renderRequestId) {
                cancelAnimationFrame(renderRequestId);
            }

            renderRequestId = requestAnimationFrame(() => {
                const now = performance.now();
                if (now - lastRenderTime < RENDER_THROTTLE_MS && !isDirty) {
                    return;
                }

                performOptimizedRender();
                lastRenderTime = now;
                isDirty = false;
                renderRequestId = null;
            });
        }

        // 🚀 即时重绘函数（用于实时预览，无节流）
        function redrawAnnotationCanvasImmediate() {
            if (!annotationContext) return;

            // 取消任何待处理的节流渲染
            if (renderRequestId) {
                cancelAnimationFrame(renderRequestId);
                renderRequestId = null;
            }

            // 立即执行渲染
            performOptimizedRender();
            lastRenderTime = performance.now();
            isDirty = false;
        }

        // 执行优化的渲染
        function performOptimizedRender() {
            if (!annotationContext) return;

            // 🚀 性能优化：只清除需要更新的区域
            annotationContext.clearRect(0, 0, annotationContext.canvas.width, annotationContext.canvas.height);

            // 🔧 关键修复：只在编辑模式下绘制标注，避免预览模式下的重复显示
            if (!isEditingMode) {
                console.log('[PREVIEW] 🎨 Preview mode: skipping annotation rendering to avoid duplicates');
                return;
            }

            // 🚀 性能优化：视口裁剪，只绘制可见形状
            const visibleShapeCount = updateVisibleShapes();

            // 绘制可见形状
            visibleShapes.forEach(shape => drawShape(shape, false));

            console.log('[PREVIEW] 🎨 Optimized render:', visibleShapeCount, '/', shapes.length, 'shapes visible');
        }

        // 🚀 更新可见形状列表（视口裁剪优化）
        function updateVisibleShapes() {
            if (!annotationContext) return 0;

            const canvasWidth = annotationContext.canvas.width;
            const canvasHeight = annotationContext.canvas.height;

            visibleShapes = shapes.filter(shape => {
                return isShapeInViewport(shape, canvasWidth, canvasHeight);
            });

            return visibleShapes.length;
        }

        // 检查形状是否在视口内
        function isShapeInViewport(shape, viewWidth, viewHeight) {
            // 获取形状边界框
            const bounds = getShapeBounds(shape);
            if (!bounds) return true; // 如果无法计算边界，默认可见

            // 检查是否与视口相交（包含边距）
            return !(bounds.right < -VIEWPORT_PADDING ||
                    bounds.left > viewWidth + VIEWPORT_PADDING ||
                    bounds.bottom < -VIEWPORT_PADDING ||
                    bounds.top > viewHeight + VIEWPORT_PADDING);
        }

        // 标记Canvas需要重绘
        function markDirty() {
            isDirty = true;
        }

        // 获取形状边界框
        function getShapeBounds(shape) {
            switch (shape.type) {
                case 'rectangle':
                case 'mosaic':
                    return {
                        left: Math.min(shape.startX, shape.endX),
                        top: Math.min(shape.startY, shape.endY),
                        right: Math.max(shape.startX, shape.endX),
                        bottom: Math.max(shape.startY, shape.endY)
                    };
                case 'circle':
                case 'ellipse':
                    const centerX = (shape.startX + shape.endX) / 2;
                    const centerY = (shape.startY + shape.endY) / 2;
                    const radiusX = Math.abs(shape.endX - shape.startX) / 2;
                    const radiusY = Math.abs(shape.endY - shape.startY) / 2;
                    return {
                        left: centerX - radiusX,
                        top: centerY - radiusY,
                        right: centerX + radiusX,
                        bottom: centerY + radiusY
                    };
                case 'line':
                case 'arrow':
                    return {
                        left: Math.min(shape.startX, shape.endX),
                        top: Math.min(shape.startY, shape.endY),
                        right: Math.max(shape.startX, shape.endX),
                        bottom: Math.max(shape.startY, shape.endY)
                    };
                case 'brush':
                case 'pen':
                    if (!shape.points || shape.points.length < 2) return null;
                    let minX = shape.points[0], maxX = shape.points[0];
                    let minY = shape.points[1], maxY = shape.points[1];
                    for (let i = 2; i < shape.points.length; i += 2) {
                        minX = Math.min(minX, shape.points[i]);
                        maxX = Math.max(maxX, shape.points[i]);
                        minY = Math.min(minY, shape.points[i + 1]);
                        maxY = Math.max(maxY, shape.points[i + 1]);
                    }
                    return { left: minX, top: minY, right: maxX, bottom: maxY };
                case 'text':
                    const fontSize = shape.fontSize || 16;
                    const textWidth = (shape.text || '').length * fontSize * 0.6;
                    return {
                        left: shape.x,
                        top: shape.y,
                        right: shape.x + textWidth,
                        bottom: shape.y + fontSize
                    };
                case 'number':
                    const radius = shape.radius || 15;
                    return {
                        left: shape.x - radius,
                        top: shape.y - radius,
                        right: shape.x + radius,
                        bottom: shape.y + radius
                    };
                default:
                    return null;
            }
        }

        function drawShape(shape, isPreview = false) {
            if (!annotationContext || !shape) return;

            annotationContext.save();
            annotationContext.strokeStyle = shape.color;
            // 🔧 修复：缩放线宽以匹配DPR
            annotationContext.lineWidth = (shape.strokeWidth || 3) * canvasScale;

            if (isPreview) {
                annotationContext.globalAlpha = 0.7;
                annotationContext.setLineDash([5, 5]);
            }

            // 🔧 修复：正确处理DPR缩放 - 实时预览需要缩放坐标
            // 坐标存储为CSS像素，但Canvas可能有DPR缩放，需要相应调整
            const startX = shape.startX * canvasScale;
            const startY = shape.startY * canvasScale;
            const endX = shape.endX * canvasScale;
            const endY = shape.endY * canvasScale;

            console.log('[PREVIEW] 🎨 Drawing shape with DPR scaling:', {
                original: `(${shape.startX},${shape.startY}) to (${shape.endX},${shape.endY})`,
                scaled: `(${startX},${startY}) to (${endX},${endY})`,
                canvasScale: canvasScale
            });

            switch (shape.type) {
                case 'rectangle':
                    drawRectangle(startX, startY, endX, endY);
                    break;
                case 'circle':
                case 'ellipse':
                    drawEllipse(startX, startY, endX, endY);
                    break;
                case 'arrow':
                    drawArrow(startX, startY, endX, endY);
                    break;
                case 'line':
                    drawLine(startX, startY, endX, endY);
                    break;
                case 'brush':
                case 'pen':
                    drawBrushStroke(shape, isPreview);
                    break;
                case 'text':
                    drawText(shape);
                    break;
                case 'mosaic':
                    drawMosaic(shape);
                    break;
                case 'number':
                    drawNumber(shape);
                    break;
                default:
                    console.warn('[PREVIEW] ⚠️ Unknown shape type:', shape.type);
            }

            annotationContext.restore();
        }

        function drawRectangle(startX, startY, endX, endY) {
            const x = Math.min(startX, endX);
            const y = Math.min(startY, endY);
            const width = Math.abs(endX - startX);
            const height = Math.abs(endY - startY);

            annotationContext.beginPath();
            annotationContext.rect(x, y, width, height);
            annotationContext.stroke();
        }

        function drawEllipse(startX, startY, endX, endY) {
            const centerX = (startX + endX) / 2;
            const centerY = (startY + endY) / 2;
            const radiusX = Math.abs(endX - startX) / 2;
            const radiusY = Math.abs(endY - startY) / 2;

            annotationContext.beginPath();
            annotationContext.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
            annotationContext.stroke();
        }

        function drawArrow(startX, startY, endX, endY) {
            // 绘制主线
            annotationContext.beginPath();
            annotationContext.moveTo(startX, startY);
            annotationContext.lineTo(endX, endY);
            annotationContext.stroke();

            // 计算箭头
            const angle = Math.atan2(endY - startY, endX - startX);
            const arrowLength = 15;
            const arrowAngle = Math.PI / 6;

            // 绘制箭头头部
            annotationContext.beginPath();
            annotationContext.moveTo(endX, endY);
            annotationContext.lineTo(
                endX - arrowLength * Math.cos(angle - arrowAngle),
                endY - arrowLength * Math.sin(angle - arrowAngle)
            );
            annotationContext.moveTo(endX, endY);
            annotationContext.lineTo(
                endX - arrowLength * Math.cos(angle + arrowAngle),
                endY - arrowLength * Math.sin(angle + arrowAngle)
            );
            annotationContext.stroke();
        }

        function drawLine(startX, startY, endX, endY) {
            annotationContext.beginPath();
            annotationContext.moveTo(startX, startY);
            annotationContext.lineTo(endX, endY);
            annotationContext.stroke();
        }

        function drawBrushStroke(shape, isPreview = false) {
            if (!shape.points || shape.points.length < 4) return;

            // 🔧 修复：保存画布状态以便恢复
            annotationContext.save();

            // 🔧 修复：应用预览样式（橙色虚线）与其他工具保持一致
            if (isPreview) {
                annotationContext.strokeStyle = '#ff6b35'; // 橙色预览
                annotationContext.globalAlpha = 0.7;
                annotationContext.setLineDash([5, 5]);
            } else {
                annotationContext.strokeStyle = shape.color;
            }

            // 应用线宽和样式
            annotationContext.lineWidth = (shape.strokeWidth || 3) * canvasScale;
            annotationContext.lineCap = 'round';
            annotationContext.lineJoin = 'round';

            annotationContext.beginPath();
            // 🔧 修复：应用DPR缩放到CSS坐标
            annotationContext.moveTo(shape.points[0] * canvasScale, shape.points[1] * canvasScale);

            // 绘制平滑的曲线
            for (let i = 2; i < shape.points.length - 2; i += 2) {
                // 🔧 修复：应用DPR缩放到CSS坐标
                const xc = (shape.points[i] * canvasScale + shape.points[i + 2] * canvasScale) / 2;
                const yc = (shape.points[i + 1] * canvasScale + shape.points[i + 3] * canvasScale) / 2;
                annotationContext.quadraticCurveTo(
                    shape.points[i] * canvasScale,
                    shape.points[i + 1] * canvasScale,
                    xc, yc
                );
            }

            // 绘制最后一段
            if (shape.points.length >= 4) {
                const lastIndex = shape.points.length - 2;
                // 🔧 修复：应用DPR缩放到CSS坐标
                annotationContext.quadraticCurveTo(
                    shape.points[lastIndex - 2] * canvasScale,
                    shape.points[lastIndex - 1] * canvasScale,
                    shape.points[lastIndex] * canvasScale,
                    shape.points[lastIndex + 1] * canvasScale
                );
            }

            annotationContext.stroke();

            // 🔧 修复：恢复画布状态
            annotationContext.restore();
        }

        function drawText(shape) {
            if (!shape.text) return;

            annotationContext.save();
            annotationContext.fillStyle = shape.color;
            // 🔧 修复：应用DPR缩放到字体大小
            annotationContext.font = `${(shape.fontSize || 16) * canvasScale}px ${shape.fontFamily || 'Arial'}`;
            annotationContext.textBaseline = 'top';

            // 添加文字描边以提高可读性
            annotationContext.strokeStyle = 'white';
            annotationContext.lineWidth = 3 * canvasScale;
            // 🔧 修复：应用DPR缩放到文字位置
            annotationContext.strokeText(shape.text, shape.x * canvasScale, shape.y * canvasScale);

            // 绘制文字
            annotationContext.fillText(shape.text, shape.x * canvasScale, shape.y * canvasScale);

            annotationContext.restore();
        }

        function drawNumber(shape) {
            if (!shape.number) return;

            annotationContext.save();

            // 🔧 修复：应用DPR缩放到CSS坐标
            const radius = (shape.radius || 15) * canvasScale;
            const fontSize = (shape.fontSize || 16) * canvasScale;

            // 绘制圆形背景
            annotationContext.beginPath();
            annotationContext.arc(shape.x * canvasScale, shape.y * canvasScale, radius, 0, 2 * Math.PI);
            annotationContext.fillStyle = shape.backgroundColor || '#ffffff';
            annotationContext.fill();

            // 绘制边框
            annotationContext.strokeStyle = shape.color || '#ff0000';
            annotationContext.lineWidth = 2;
            annotationContext.stroke();

            // 绘制数字
            annotationContext.fillStyle = shape.color || '#ff0000';
            annotationContext.font = `bold ${fontSize}px Arial`;
            annotationContext.textAlign = 'center';
            annotationContext.textBaseline = 'middle';
            // 🔧 修复：应用DPR缩放到文字位置
            annotationContext.fillText(shape.number.toString(), shape.x * canvasScale, shape.y * canvasScale);

            annotationContext.restore();
        }

        function drawMosaic(shape) {
            const x = Math.min(shape.startX, shape.endX);
            const y = Math.min(shape.startY, shape.endY);
            const width = Math.abs(shape.endX - shape.startX);
            const height = Math.abs(shape.endY - shape.startY);

            if (width < 1 || height < 1) return;

            annotationContext.save();

            // 创建马赛克效果
            const mosaicSize = 8; // 马赛克块大小
            const cols = Math.ceil(width / mosaicSize);
            const rows = Math.ceil(height / mosaicSize);

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    const blockX = x + col * mosaicSize;
                    const blockY = y + row * mosaicSize;
                    const blockWidth = Math.min(mosaicSize, width - col * mosaicSize);
                    const blockHeight = Math.min(mosaicSize, height - row * mosaicSize);

                    // 生成随机颜色或使用灰度
                    const gray = Math.floor(Math.random() * 100) + 100; // 100-200的灰度值
                    annotationContext.fillStyle = `rgb(${gray}, ${gray}, ${gray})`;
                    annotationContext.fillRect(blockX, blockY, blockWidth, blockHeight);
                }
            }

            // 添加边框
            annotationContext.strokeStyle = shape.color || '#666666';
            annotationContext.lineWidth = 1;
            annotationContext.strokeRect(x, y, width, height);

            annotationContext.restore();
        }

        // 🧽 橡皮擦几何检测函数
        function isPointInRectangle(x, y, shape, tolerance) {
            const left = Math.min(shape.startX, shape.endX) - tolerance;
            const right = Math.max(shape.startX, shape.endX) + tolerance;
            const top = Math.min(shape.startY, shape.endY) - tolerance;
            const bottom = Math.max(shape.startY, shape.endY) + tolerance;

            return x >= left && x <= right && y >= top && y <= bottom;
        }

        function isPointInEllipse(x, y, shape, tolerance) {
            const centerX = (shape.startX + shape.endX) / 2;
            const centerY = (shape.startY + shape.endY) / 2;
            const radiusX = Math.abs(shape.endX - shape.startX) / 2 + tolerance;
            const radiusY = Math.abs(shape.endY - shape.startY) / 2 + tolerance;

            const dx = x - centerX;
            const dy = y - centerY;

            return (dx * dx) / (radiusX * radiusX) + (dy * dy) / (radiusY * radiusY) <= 1;
        }

        function isPointNearLine(x, y, shape, tolerance) {
            const distance = distanceFromPointToLine(x, y, shape.startX, shape.startY, shape.endX, shape.endY);
            return distance <= tolerance;
        }

        function isPointNearBrushStroke(x, y, shape, tolerance) {
            if (!shape.points || shape.points.length < 4) return false;

            for (let i = 0; i < shape.points.length - 2; i += 2) {
                const distance = distanceFromPointToLine(
                    x, y,
                    shape.points[i], shape.points[i + 1],
                    shape.points[i + 2], shape.points[i + 3]
                );
                if (distance <= tolerance) return true;
            }
            return false;
        }

        function isPointInText(x, y, shape, tolerance) {
            if (!shape.text) return false;

            // 估算文字边界框
            const fontSize = shape.fontSize || 16;
            const textWidth = shape.text.length * fontSize * 0.6; // 粗略估算
            const textHeight = fontSize;

            return x >= shape.x - tolerance &&
                   x <= shape.x + textWidth + tolerance &&
                   y >= shape.y - tolerance &&
                   y <= shape.y + textHeight + tolerance;
        }

        function distanceFromPointToLine(px, py, x1, y1, x2, y2) {
            const A = px - x1;
            const B = py - y1;
            const C = x2 - x1;
            const D = y2 - y1;

            const dot = A * C + B * D;
            const lenSq = C * C + D * D;

            if (lenSq === 0) {
                // 线段长度为0，返回到起点的距离
                return Math.sqrt(A * A + B * B);
            }

            let param = dot / lenSq;

            let xx, yy;
            if (param < 0) {
                xx = x1;
                yy = y1;
            } else if (param > 1) {
                xx = x2;
                yy = y2;
            } else {
                xx = x1 + param * C;
                yy = y1 + param * D;
            }

            const dx = px - xx;
            const dy = py - yy;
            return Math.sqrt(dx * dx + dy * dy);
        }

        // 📚 撤销重做系统实现 - 增强版
        function recordAnnotationAction(action, data) {
            console.log('[PREVIEW] 📚 Recording action:', action, 'Current historyIndex:', historyIndex, 'History length:', annotationHistory.length);
            console.log('[PREVIEW] 📚 Current shapes count:', shapes.length);

            // 🔧 修复：清除当前位置之后的历史记录（撤销后新增操作时）
            if (historyIndex < annotationHistory.length - 1) {
                const removedCount = annotationHistory.length - historyIndex - 1;
                annotationHistory = annotationHistory.slice(0, historyIndex + 1);
                console.log('[PREVIEW] 📚 Cleared', removedCount, 'future history records');
            }

            // 🔧 验证shapes状态一致性
            const currentShapesCount = shapes.length;

            // 添加新的操作记录
            const record = {
                action: action,
                data: data,
                timestamp: Date.now(),
                shapesSnapshot: JSON.parse(JSON.stringify(shapes)),
                shapesCount: currentShapesCount // 🆕 添加计数验证
            };

            annotationHistory.push(record);
            historyIndex++;

            // 限制历史记录大小
            if (annotationHistory.length > MAX_HISTORY_SIZE) {
                annotationHistory.shift();
                historyIndex--;
                console.log('[PREVIEW] 📚 History size limited, shifted oldest record');
            }

            console.log('[PREVIEW] 📚 Action recorded:', action, 'New history size:', annotationHistory.length, 'New historyIndex:', historyIndex);
            console.log('[PREVIEW] 📚 Recorded shapes count:', record.shapesCount);

            // 🆕 验证历史记录完整性
            validateHistoryIntegrity();
        }

        // 🆕 验证历史记录完整性
        function validateHistoryIntegrity() {
            if (annotationHistory.length === 0) return;

            const currentRecord = annotationHistory[historyIndex];
            if (currentRecord && currentRecord.shapesCount !== undefined && currentRecord.shapesCount !== shapes.length) {
                console.warn('[PREVIEW] ⚠️ History integrity warning: recorded count', currentRecord.shapesCount, 'vs current count', shapes.length);
            }
        }

        // 🆕 调试：检查坐标系统
        function debugCoordinateSystem(event, label) {
            const annotationCanvas = document.getElementById('annotationCanvas');
            const previewCanvas = document.getElementById('previewCanvas');
            const annotationLayer = document.getElementById('annotationLayer');

            const annotationRect = annotationCanvas ? annotationCanvas.getBoundingClientRect() : null;
            const previewRect = previewCanvas ? previewCanvas.getBoundingClientRect() : null;
            const layerRect = annotationLayer ? annotationLayer.getBoundingClientRect() : null;

            console.log(`[PREVIEW] 🐛 ${label} Coordinate Debug:`, {
                'event.offsetX/Y': [event.offsetX, event.offsetY],
                'event.clientX/Y': [event.clientX, event.clientY],
                'annotationCanvas.rect': annotationRect ? {
                    left: annotationRect.left,
                    top: annotationRect.top,
                    width: annotationRect.width,
                    height: annotationRect.height
                } : 'null',
                'previewCanvas.rect': previewRect ? {
                    left: previewRect.left,
                    top: previewRect.top,
                    width: previewRect.width,
                    height: previewRect.height
                } : 'null',
                'annotationLayer.rect': layerRect ? {
                    left: layerRect.left,
                    top: layerRect.top,
                    width: layerRect.width,
                    height: layerRect.height
                } : 'null',
                'alignment_check': {
                    'annotation_vs_preview_offset': annotationRect && previewRect ? {
                        left: annotationRect.left - previewRect.left,
                        top: annotationRect.top - previewRect.top
                    } : 'null',
                    'layer_vs_preview_offset': layerRect && previewRect ? {
                        left: layerRect.left - previewRect.left,
                        top: layerRect.top - previewRect.top
                    } : 'null'
                },
                'canvasScale': canvasScale
            });
        }

        // 🆕 调试：显示完整的历史记录状态
        function debugHistoryState() {
            console.log('[PREVIEW] 🐛 === HISTORY DEBUG STATE ===');
            console.log('[PREVIEW] 🐛 Current historyIndex:', historyIndex);
            console.log('[PREVIEW] 🐛 History length:', annotationHistory.length);
            console.log('[PREVIEW] 🐛 Current shapes count:', shapes.length);

            annotationHistory.forEach((record, index) => {
                const marker = index === historyIndex ? ' <-- CURRENT' : '';
                console.log(`[PREVIEW] 🐛 [${index}] ${record.action} - ${record.shapesCount} shapes - ${new Date(record.timestamp).toLocaleTimeString()}${marker}`);
            });

            console.log('[PREVIEW] 🐛 === END HISTORY DEBUG ===');
        }

        // 🆕 添加键盘快捷键用于调试
        function addDebugKeyboardShortcuts() {
            document.addEventListener('keydown', function(event) {
                if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                    event.preventDefault();
                    debugHistoryState();
                }
            });
        }

        // 🆕 智能窗口焦点管理和光标管理
        let previewFocusTimeout = null;
        let isPreviewMouseIn = false;

        function setupSmartFocusManagement() {
            if (!window.__TAURI__) return;

            // 鼠标进入窗口事件
            document.addEventListener('mouseenter', () => {
                isPreviewMouseIn = true;
                requestPreviewFocus();
                // 🆕 自动更新光标为注解工具光标
                updateCursorForAnnotationTool();
            });

            // 鼠标离开窗口事件
            document.addEventListener('mouseleave', () => {
                isPreviewMouseIn = false;
                cancelPreviewFocus();
                // 🆕 恢复默认光标
                updateCursorToDefault();
            });

            // 窗口获得焦点时取消待定的焦点请求
            window.addEventListener('focus', () => {
                cancelPreviewFocus();
                // 🆕 确保光标正确
                updateCursorForAnnotationTool();
            });

            console.log('[PREVIEW] 🎯 Smart focus and cursor management setup completed');
        }

        // 🆕 根据当前注解工具更新光标
        function updateCursorForAnnotationTool() {
            if (!isEditingMode) {
                updateCursorToDefault();
                return;
            }

            const annotationLayer = document.getElementById('annotationLayer');
            if (!annotationLayer) return;

            let cursor = 'default';

            switch (currentAnnotationTool) {
                case 'text':
                    cursor = 'text';
                    break;
                case 'rectangle':
                case 'circle':
                case 'arrow':
                    cursor = 'crosshair';
                    break;
                case 'brush':
                case 'pen':
                    cursor = 'crosshair';
                    break;
                default:
                    cursor = 'default';
            }

            annotationLayer.style.cursor = cursor;
            document.body.style.cursor = cursor;

            console.log('[PREVIEW] 🎯 Cursor updated to:', cursor, 'for tool:', currentAnnotationTool);
        }

        // 🆕 恢复默认光标
        function updateCursorToDefault() {
            const annotationLayer = document.getElementById('annotationLayer');
            if (annotationLayer) {
                annotationLayer.style.cursor = 'default';
            }
            document.body.style.cursor = 'default';

            console.log('[PREVIEW] 🎯 Cursor reset to default');
        }

        function requestPreviewFocus() {
            // 取消之前的焦点请求
            cancelPreviewFocus();

            // 设置延迟焦点请求
            previewFocusTimeout = setTimeout(async () => {
                if (!isPreviewMouseIn) return; // 鼠标已离开，不设置焦点

                try {
                    // 检查当前是否已有焦点
                    const hasFocus = await window.__TAURI__.core.invoke('check_window_focus', {
                        windowId: 'screenshot-preview'
                    });

                    if (!hasFocus && isPreviewMouseIn) {
                        console.log('[PREVIEW] 🎯 Requesting smart focus for preview window');
                        await window.__TAURI__.core.invoke('request_window_focus', {
                            windowId: 'screenshot-preview',
                            delayMs: 50
                        });
                    }
                } catch (error) {
                    console.warn('[PREVIEW] 🎯 Smart focus request failed:', error);
                }
            }, 250); // 稍长延迟，因为预览窗口通常更大，鼠标移动更频繁
        }

        function cancelPreviewFocus() {
            if (previewFocusTimeout) {
                clearTimeout(previewFocusTimeout);
                previewFocusTimeout = null;
            }
        }

        function handleAnnotationUndo() {
            console.log('[PREVIEW] ↶ Undo requested');

            // 🔧 CRITICAL FIX: Use the new UndoRedoManager system
            if (!undoRedoManager || !undoRedoManager.canUndo()) {
                console.log('[PREVIEW] ⚠️ No more actions to undo');
                showAnnotationFeedback('⚠️ 没有可撤销的操作');
                return;
            }

            // 🆕 记录撤销前的状态用于调试
            const beforeShapes = [...shapes];
            console.log('[PREVIEW] ↶ Current shapes count before undo:', beforeShapes.length);

            // 🔧 执行撤销操作
            const undoAction = undoRedoManager.undo();
            if (undoAction) {
                console.log('[PREVIEW] ↶ Executing undo action:', undoAction);

                // 🔧 根据操作类型执行撤销
                if (undoAction.type === 'add') {
                    // 撤销添加操作：移除最后添加的形状
                    const shapeToRemove = undoAction.data;
                    shapes = shapes.filter(shape => !shapesEqual(shape, shapeToRemove));
                    console.log('[PREVIEW] ↶ Removed shape from undo:', shapeToRemove.type);
                } else if (undoAction.type === 'remove') {
                    // 撤销删除操作：重新添加形状
                    shapes.push(undoAction.data);
                    console.log('[PREVIEW] ↶ Restored shape from undo:', undoAction.data.type);
                } else if (undoAction.type === 'modify') {
                    // 撤销修改操作：恢复到之前的状态
                    const index = shapes.findIndex(shape => shapesEqual(shape, undoAction.newData));
                    if (index !== -1) {
                        shapes[index] = undoAction.previousData;
                        console.log('[PREVIEW] ↶ Reverted shape modification');
                    }
                }

                // 🆕 详细的状态变化日志
                console.log('[PREVIEW] ↶ Undo state change: from', beforeShapes.length, 'to', shapes.length, 'shapes');

                redrawAnnotationCanvas();
                showAnnotationFeedback('↶ 撤销操作已完成');

                // 🔧 发送工具反馈更新
                sendToolFeedback();
            }
        }

        function handleAnnotationRedo() {
            console.log('[PREVIEW] ↷ Redo requested');

            // 🔧 CRITICAL FIX: Use the new UndoRedoManager system
            if (!undoRedoManager || !undoRedoManager.canRedo()) {
                console.log('[PREVIEW] ⚠️ No more actions to redo');
                showAnnotationFeedback('⚠️ 没有可重做的操作');
                return;
            }

            // 🆕 记录重做前的状态用于调试
            const beforeShapes = [...shapes];
            console.log('[PREVIEW] ↷ Current shapes count before redo:', beforeShapes.length);

            // 🔧 执行重做操作
            const redoAction = undoRedoManager.redo();
            if (redoAction) {
                console.log('[PREVIEW] ↷ Executing redo action:', redoAction);

                // 🔧 根据操作类型执行重做
                if (redoAction.type === 'add') {
                    // 重做添加操作：重新添加形状
                    shapes.push(redoAction.data);
                    console.log('[PREVIEW] ↷ Re-added shape from redo:', redoAction.data.type);
                } else if (redoAction.type === 'remove') {
                    // 重做删除操作：重新移除形状
                    const shapeToRemove = redoAction.data;
                    shapes = shapes.filter(shape => !shapesEqual(shape, shapeToRemove));
                    console.log('[PREVIEW] ↷ Re-removed shape from redo:', shapeToRemove.type);
                } else if (redoAction.type === 'modify') {
                    // 重做修改操作：应用新的状态
                    const index = shapes.findIndex(shape => shapesEqual(shape, redoAction.previousData));
                    if (index !== -1) {
                        shapes[index] = redoAction.newData;
                        console.log('[PREVIEW] ↷ Re-applied shape modification');
                    }
                }

                // 🆕 详细的状态变化日志
                console.log('[PREVIEW] ↷ Redo state change: from', beforeShapes.length, 'to', shapes.length, 'shapes');

                redrawAnnotationCanvas();
                showAnnotationFeedback('↷ 重做操作已完成');

                // 🔧 发送工具反馈更新
                sendToolFeedback();
            }
        }

        function handleAnnotationCopy() {
            if (shapes.length === 0) {
                showAnnotationFeedback('⚠️ 没有可复制的标注');
                return;
            }

            // 获取带标注的图片数据
            getAnnotatedImageData().then(imageData => {
                // 这里可以实现复制到剪贴板的功能
                console.log('[PREVIEW] 📋 Copy operation completed');
                showAnnotationFeedback('📋 标注已复制');
            }).catch(error => {
                console.error('[PREVIEW] ❌ Copy failed:', error);
                showAnnotationFeedback('❌ 复制失败');
            });
        }

        function clearAllAnnotations() {
            if (shapes.length === 0) {
                showAnnotationFeedback('⚠️ 没有标注需要清除');
                return;
            }

            // 记录清除操作
            recordAnnotationAction('clear', { clearedShapes: [...shapes] });

            // 清空所有形状
            shapes = [];
            redrawAnnotationCanvas();

            console.log('[PREVIEW] 🗑️ All annotations cleared');
            showAnnotationFeedback('🗑️ 所有标注已清除');
        }

        function handleBrushAnnotationStart(event) {
            console.log('[PREVIEW] 🖌️ Brush annotation started');

            // 🔧 修复：存储CSS坐标，与形状工具保持一致
            // 创建新的画笔笔画
            currentShape = {
                type: currentAnnotationTool,
                points: [event.offsetX, event.offsetY], // 存储CSS坐标
                color: currentColor || '#ff0000',
                strokeWidth: 3
            };

            console.log('[PREVIEW] 🔧 Brush coordinates stored (CSS):', event.offsetX, event.offsetY);
            showAnnotationFeedback(`🖌️ ${currentAnnotationTool}标注已开始 - 拖拽以绘制`);
        }

        function handleBrushAnnotationMove(event) {
            if (!currentShape || !isAnnotating) return;

            // 🔧 修复：存储CSS坐标，与形状工具保持一致
            // 添加新的点到笔画路径
            currentShape.points.push(event.offsetX, event.offsetY);

            // 🚀 修复：使用即时重绘确保流畅的画笔预览体验
            redrawAnnotationCanvasImmediate();

            // 绘制当前正在绘制的笔画
            drawBrushStroke(currentShape, true);
        }

        function handleBrushAnnotationEnd(event) {
            console.log('[PREVIEW] 🖌️ Brush annotation completed');

            if (currentShape && currentShape.points && currentShape.points.length >= 4) {
                // 检查笔画有效性
                if (isValidShape(currentShape)) {
                    // 创建笔画副本
                    const newBrushStroke = { ...currentShape };

                    // 添加到形状列表
                    shapes.push(newBrushStroke);

                    // 🔧 CRITICAL FIX: 使用新的撤销系统记录笔刷操作
                    if (undoRedoManager) {
                        addAnnotationToHistory('brush', newBrushStroke);
                    }

                    // 重绘所有形状
                    redrawAnnotationCanvas();

                    const pointCount = currentShape.points.length;
                    showAnnotationFeedback(`✅ ${currentAnnotationTool}标注已完成 - ${pointCount}个点`);
                    console.log('[PREVIEW] 🎨 Brush stroke added to canvas:', newBrushStroke);

                    // 🔧 发送工具反馈更新
                    sendToolFeedback();
                } else {
                    // 无效笔画，清除预览
                    redrawAnnotationCanvas();
                    console.log('[PREVIEW] ⚠️ Brush stroke too short, ignored');
                    showAnnotationFeedback(`⚠️ 笔画过短，已忽略`);
                }
            }

            currentShape = null;
        }

        function handleEraserAnnotationStart(event) {
            console.log('[PREVIEW] 🧽 Eraser annotation started at:', event.offsetX, event.offsetY);

            // 🔧 修复：存储CSS坐标，与形状工具保持一致
            // 开始橡皮擦操作
            isErasing = true;
            eraserStartPos = {
                x: event.offsetX,
                y: event.offsetY
            };

            // 检查是否有形状被擦除
            checkAndEraseShapes(event.offsetX, event.offsetY);

            showAnnotationFeedback('🧽 橡皮擦已激活 - 点击或拖拽以擦除标注');
        }

        function handleEraserAnnotationMove(event) {
            if (!isErasing) return;

            // 🔧 修复：使用CSS坐标进行擦除检查
            // 持续检查擦除
            checkAndEraseShapes(event.offsetX, event.offsetY);
        }

        function handleEraserAnnotationEnd(event) {
            console.log('[PREVIEW] 🧽 Eraser annotation completed');
            isErasing = false;
            eraserStartPos = null;

            showAnnotationFeedback('🧽 橡皮擦操作完成');
        }

        // 检查并擦除形状
        function checkAndEraseShapes(x, y) {
            const eraserRadius = 15; // 橡皮擦半径
            let erasedCount = 0;

            // 从后往前遍历，优先擦除最新的形状
            for (let i = shapes.length - 1; i >= 0; i--) {
                const shape = shapes[i];

                if (isPointInShape(x, y, shape, eraserRadius)) {
                    // 记录擦除操作到历史
                    recordAnnotationAction('erase', { ...shape, index: i });

                    // 移除形状
                    shapes.splice(i, 1);
                    erasedCount++;

                    console.log('[PREVIEW] 🧽 Erased shape:', shape.type, 'at index:', i);
                }
            }

            if (erasedCount > 0) {
                // 重绘Canvas
                redrawAnnotationCanvas();
                showAnnotationFeedback(`🧽 已擦除 ${erasedCount} 个标注`);
            }
        }

        // 检查点是否在形状内
        function isPointInShape(x, y, shape, tolerance = 5) {
            switch (shape.type) {
                case 'rectangle':
                    return isPointInRectangle(x, y, shape, tolerance);
                case 'circle':
                case 'ellipse':
                    return isPointInEllipse(x, y, shape, tolerance);
                case 'arrow':
                case 'line':
                    return isPointNearLine(x, y, shape, tolerance);
                case 'brush':
                case 'pen':
                    return isPointNearBrushStroke(x, y, shape, tolerance);
                case 'text':
                    return isPointInText(x, y, shape, tolerance);
                default:
                    return false;
            }
        }

        // 显示标注反馈
        function showAnnotationFeedback(message) {
            console.log('[PREVIEW] 💬 Annotation feedback:', message);
            // 可以在这里添加UI反馈，比如临时提示
            showTemporaryNotification(message, 'info');
        }

        // 🆕 保存前验证当前状态
        function validateStateBeforeSave() {
            console.log('[PREVIEW] 🔍 Validating state before save...');
            console.log('[PREVIEW] 🔍 Current shapes count:', shapes.length);
            console.log('[PREVIEW] 🔍 Current historyIndex:', historyIndex);
            console.log('[PREVIEW] 🔍 History length:', annotationHistory.length);

            // 🔧 修复：移除错误的状态"修复"逻辑
            // 验证当前状态与历史记录的一致性（仅用于调试，不自动修复）
            if (annotationHistory.length > 0 && historyIndex >= 0 && historyIndex < annotationHistory.length) {
                const currentRecord = annotationHistory[historyIndex];
                if (currentRecord.shapesCount !== undefined && currentRecord.shapesCount !== shapes.length) {
                    console.log('[PREVIEW] 🔍 State difference detected (normal after erase operations):');
                    console.log('[PREVIEW] 🔍 History record shapes count:', currentRecord.shapesCount);
                    console.log('[PREVIEW] 🔍 Current shapes count:', shapes.length);

                    // 🔧 修复：不再自动"修复"状态，因为擦除操作会自然减少shapes数量
                    // 这是正常行为，不是需要修复的错误
                    console.log('[PREVIEW] 🔍 State difference is normal (eraser operations reduce shape count)');
                }
            }

            // 列出所有当前的shapes用于调试
            shapes.forEach((shape, index) => {
                if (shape.type === 'brush' || shape.type === 'pen') {
                    // 画笔工具显示points信息
                    const pointCount = shape.points ? shape.points.length / 2 : 0;
                    const firstPoint = shape.points && shape.points.length >= 2 ? `(${shape.points[0]},${shape.points[1]})` : '(no points)';
                    console.log(`[PREVIEW] 🔍 Shape ${index}: ${shape.type} with ${pointCount} points, first: ${firstPoint}`);
                } else {
                    // 其他工具显示startX/startY
                    console.log(`[PREVIEW] 🔍 Shape ${index}: ${shape.type} at (${shape.startX},${shape.startY})`);
                }
            });

            console.log('[PREVIEW] ✅ State validation completed');
        }

        // 获取带标注的图片数据
        async function getAnnotatedImageData() {
            console.log('[PREVIEW] 🎨 Getting annotated image data');

            // 🔧 CRITICAL FIX: 临时移除橙色边框，避免被保存到图片中
            const previewWrapper = document.getElementById('previewWrapper');
            const hadEditingMode = previewWrapper && previewWrapper.classList.contains('editing-mode');

            if (hadEditingMode) {
                console.log('[PREVIEW] 🔧 Temporarily removing orange border for save');
                previewWrapper.classList.remove('editing-mode');
            }

            // 🆕 保存前验证状态
            validateStateBeforeSave();

            try {
                const previewCanvas = document.getElementById('previewCanvas');
                const annotationCanvas = document.getElementById('annotationCanvas');

                if (!previewCanvas) {
                    console.warn('[PREVIEW] ⚠️ Preview canvas not found');
                    // 🔧 恢复橙色边框
                    if (hadEditingMode && previewWrapper) {
                        previewWrapper.classList.add('editing-mode');
                    }
                    return screenshotData?.dataUrl || '';
                }

                // 🔧 安全检查：检查Canvas是否被污染
                try {
                    // 尝试读取一个像素来检测Canvas是否可访问
                    const testContext = previewCanvas.getContext('2d');
                    testContext.getImageData(0, 0, 1, 1);
                } catch (securityError) {
                    console.warn('[PREVIEW] ⚠️ Canvas is tainted, using fallback method');
                    // 如果Canvas被污染，使用纯标注Canvas方法
                    return await createAnnotationOnlyImage();
                }

                // 如果没有标注，需要创建逻辑尺寸的Canvas来避免DPR放大
                if (!annotationCanvas || shapes.length === 0) {
                    console.log('[PREVIEW] 📷 No annotations, creating logical size canvas');
                    return await createLogicalSizeCanvas(previewCanvas, null);
                }

                // 创建逻辑尺寸的合成Canvas
                const result = await createLogicalSizeCanvas(previewCanvas, annotationCanvas);

                // 🔧 CRITICAL FIX: 恢复橙色边框
                if (hadEditingMode && previewWrapper) {
                    console.log('[PREVIEW] 🔧 Restoring orange border after save');
                    previewWrapper.classList.add('editing-mode');
                }

                return result;

            } catch (error) {
                console.error('[PREVIEW] ❌ Critical error in getAnnotatedImageData:', error);

                // 🔧 CRITICAL FIX: 即使出错也要恢复橙色边框
                if (hadEditingMode && previewWrapper) {
                    console.log('[PREVIEW] 🔧 Restoring orange border after error');
                    previewWrapper.classList.add('editing-mode');
                }

                return screenshotData?.dataUrl || '';
            }
        }

        // 🔧 在原始尺寸Canvas上重新绘制标注（避免重复缩放问题）
        function redrawAnnotationsAtOriginalSize(context, targetWidth, targetHeight) {
            console.log('[PREVIEW] 🎨 Redrawing annotations at original size:', targetWidth, 'x', targetHeight);

            // 计算从CSS坐标到原始尺寸的缩放比例
            const cssWidth = parseFloat(document.getElementById('previewCanvas').style.width);
            const cssHeight = parseFloat(document.getElementById('previewCanvas').style.height);

            const scaleX = targetWidth / cssWidth;
            const scaleY = targetHeight / cssHeight;

            console.log('[PREVIEW] 🔧 CSS to original scale factors:', scaleX, scaleY);
            console.log('[PREVIEW] 🔧 CSS dimensions:', cssWidth, 'x', cssHeight);
            console.log('[PREVIEW] 🔧 Target dimensions:', targetWidth, 'x', targetHeight);
            console.log('[PREVIEW] 🔧 Total shapes to redraw:', shapes.length);

            // 保存当前上下文状态
            context.save();

            // 为每个标注形状重新绘制
            shapes.forEach((shape, index) => {
                console.log('[PREVIEW] 🔧 Redrawing shape', index, ':', shape.type, 'at',
                    shape.type === 'rectangle' || shape.type === 'circle' ?
                    `(${shape.startX},${shape.startY}) size: ${shape.endX-shape.startX}x${shape.endY-shape.startY}` :
                    shape.type === 'arrow' ?
                    `(${shape.startX},${shape.startY}) to (${shape.endX},${shape.endY})` :
                    'custom coordinates');

                // 🔧 修复：根据形状类型创建缩放后的形状副本
                let scaledShape;

                if (shape.type === 'brush' || shape.type === 'pen') {
                    // 🆕 画笔工具：缩放points数组
                    const scaledPoints = shape.points ? shape.points.map((coord, i) => {
                        return i % 2 === 0 ? coord * scaleX : coord * scaleY; // 偶数索引是X坐标，奇数索引是Y坐标
                    }) : [];

                    scaledShape = {
                        ...shape,
                        points: scaledPoints,
                        strokeWidth: (shape.strokeWidth || 3) * Math.min(scaleX, scaleY)
                    };

                    console.log('[PREVIEW] 🔧 Scaled brush points:', scaledPoints.length / 2, 'points');
                } else {
                    // 🔧 其他工具：缩放startX/startY/endX/endY
                    scaledShape = {
                        ...shape,
                        startX: shape.startX * scaleX,
                        startY: shape.startY * scaleY,
                        endX: shape.endX * scaleX,
                        endY: shape.endY * scaleY,
                        strokeWidth: (shape.strokeWidth || 3) * Math.min(scaleX, scaleY)
                    };

                    console.log('[PREVIEW] 🔧 Coordinate scaling:', {
                        original: `(${shape.startX},${shape.startY}) to (${shape.endX},${shape.endY})`,
                        scaled: `(${scaledShape.startX},${scaledShape.startY}) to (${scaledShape.endX},${scaledShape.endY})`,
                        scaleFactors: `${scaleX}x${scaleY}`
                    });
                }

                // 绘制缩放后的形状
                drawShapeOnContext(context, scaledShape);
            });

            // 恢复上下文状态
            context.restore();

            console.log('[PREVIEW] ✅ All annotations redrawn at original size');
        }

        // 🔧 在指定上下文上绘制形状（用于保存时的重新绘制）
        function drawShapeOnContext(ctx, shape) {
            ctx.save();
            ctx.strokeStyle = shape.color;
            // 🔧 修复：使用与实时预览相同的线宽处理
            ctx.lineWidth = shape.strokeWidth || 3;

            const startX = shape.startX;
            const startY = shape.startY;
            const endX = shape.endX;
            const endY = shape.endY;

            switch (shape.type) {
                case 'rectangle':
                    const x = Math.min(startX, endX);
                    const y = Math.min(startY, endY);
                    const width = Math.abs(endX - startX);
                    const height = Math.abs(endY - startY);
                    ctx.strokeRect(x, y, width, height);
                    break;
                case 'circle':
                case 'ellipse':
                    const centerX = (startX + endX) / 2;
                    const centerY = (startY + endY) / 2;
                    const radiusX = Math.abs(endX - startX) / 2;
                    const radiusY = Math.abs(endY - startY) / 2;
                    ctx.beginPath();
                    ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
                    ctx.stroke();
                    break;
                case 'line':
                    ctx.beginPath();
                    ctx.moveTo(startX, startY);
                    ctx.lineTo(endX, endY);
                    ctx.stroke();
                    break;
                case 'arrow':
                    // 绘制箭头线
                    ctx.beginPath();
                    ctx.moveTo(startX, startY);
                    ctx.lineTo(endX, endY);
                    ctx.stroke();

                    // 绘制箭头头部
                    const angle = Math.atan2(endY - startY, endX - startX);
                    const arrowLength = 15 * Math.min(shape.strokeWidth / 3, 1);
                    ctx.beginPath();
                    ctx.moveTo(endX, endY);
                    ctx.lineTo(endX - arrowLength * Math.cos(angle - Math.PI / 6), endY - arrowLength * Math.sin(angle - Math.PI / 6));
                    ctx.moveTo(endX, endY);
                    ctx.lineTo(endX - arrowLength * Math.cos(angle + Math.PI / 6), endY - arrowLength * Math.sin(angle + Math.PI / 6));
                    ctx.stroke();
                    break;
                case 'brush':
                case 'pen':
                    // 🆕 绘制画笔笔画
                    if (shape.points && shape.points.length >= 4) {
                        ctx.beginPath();
                        ctx.lineCap = 'round';
                        ctx.lineJoin = 'round';

                        // 移动到第一个点
                        ctx.moveTo(shape.points[0], shape.points[1]);

                        // 绘制所有点
                        for (let i = 2; i < shape.points.length; i += 2) {
                            ctx.lineTo(shape.points[i], shape.points[i + 1]);
                        }

                        ctx.stroke();
                        console.log('[PREVIEW] 🔧 Drew brush stroke with', shape.points.length / 2, 'points');
                    } else {
                        console.warn('[PREVIEW] ⚠️ Invalid brush shape - insufficient points:', shape.points?.length || 0);
                    }
                    break;
                // 其他形状类型可以根据需要添加
            }

            ctx.restore();
        }

        // 🔧 创建原始尺寸的Canvas（避免DPR放大和尺寸不匹配问题）
        async function createLogicalSizeCanvas(previewCanvas, annotationCanvas) {
            console.log('[PREVIEW] 🎨 Creating original size canvas to avoid DPR scaling');

            try {
                // 🔧 关键修复：使用原始截图的逻辑尺寸，而不是CSS显示尺寸
                const originalWidth = screenshotData?.width;
                const originalHeight = screenshotData?.height;

                console.log('[PREVIEW] 📏 Original screenshot dimensions:', originalWidth, 'x', originalHeight);
                console.log('[PREVIEW] 📏 Canvas CSS dimensions:', previewCanvas.style.width, 'x', previewCanvas.style.height);
                console.log('[PREVIEW] 📏 Canvas actual dimensions:', previewCanvas.width, 'x', previewCanvas.height);

                // 创建原始尺寸的Canvas
                const originalCanvas = document.createElement('canvas');
                originalCanvas.width = originalWidth;
                originalCanvas.height = originalHeight;
                const originalContext = originalCanvas.getContext('2d');

                // 禁用图像平滑以保持清晰度
                originalContext.imageSmoothingEnabled = false;

                // 🔧 重要：确保previewCanvas只包含原始图片，不包含标注
                // 直接从原始图片数据重新绘制，避免重复标注
                if (screenshotData?.dataUrl) {
                    const img = new Image();
                    img.crossOrigin = 'anonymous';

                    return new Promise((resolve, reject) => {
                        img.onload = function() {
                            // 绘制原始图片到原始尺寸Canvas
                            originalContext.drawImage(img, 0, 0, originalWidth, originalHeight);

                            // 🔧 修复：如果有标注层，重新绘制标注到原始尺寸Canvas
                            if (annotationCanvas && shapes.length > 0) {
                                console.log('[PREVIEW] 🔧 Redrawing annotations at original size');
                                console.log('[PREVIEW] 📏 Annotation canvas size:', annotationCanvas.width, 'x', annotationCanvas.height);
                                console.log('[PREVIEW] 📏 Target original size:', originalWidth, 'x', originalHeight);

                                // 🔧 关键修复：直接在原始尺寸Canvas上重新绘制标注
                                // 而不是缩放已有的标注Canvas，避免重复缩放问题
                                redrawAnnotationsAtOriginalSize(originalContext, originalWidth, originalHeight);

                                console.log('[PREVIEW] 🎨 Merged image with annotations at original size');
                            } else {
                                console.log('[PREVIEW] 📷 Original image at original size');
                            }

                            resolve(originalCanvas.toDataURL('image/png'));
                        };

                        img.onerror = function() {
                            console.error('[PREVIEW] ❌ Failed to load original image');
                            reject(new Error('Failed to load original image'));
                        };

                        img.src = screenshotData.dataUrl;
                    });
                } else {
                    // 🔧 Fallback：如果没有dataUrl，使用previewCanvas重新绘制
                    console.warn('[PREVIEW] ⚠️ No original image dataUrl, using previewCanvas fallback');

                    // 从previewCanvas重新绘制到原始尺寸
                    originalContext.drawImage(previewCanvas, 0, 0, originalWidth, originalHeight);

                    // 🔧 修复：Fallback情况下也使用重新绘制方法
                    if (annotationCanvas && shapes.length > 0) {
                        console.log('[PREVIEW] 🔧 Fallback: Redrawing annotations at original size');

                        // 使用相同的重新绘制方法
                        redrawAnnotationsAtOriginalSize(originalContext, originalWidth, originalHeight);

                        console.log('[PREVIEW] 🎨 Fallback: Merged image with annotations at original size');
                    } else {
                        console.log('[PREVIEW] 📷 Fallback: Original image at original size');
                    }

                    return originalCanvas.toDataURL('image/png');
                }

            } catch (error) {
                console.error('[PREVIEW] ❌ Failed to create original size canvas:', error);
                // 回退到原始数据
                return screenshotData?.dataUrl || '';
            }
        }

        // 🔧 创建纯标注图片（当原始Canvas被污染时使用）
        async function createAnnotationOnlyImage() {
            console.log('[PREVIEW] 🎨 Creating annotation-only image due to canvas taint');

            try {
                // 如果没有标注，返回原始数据
                if (shapes.length === 0) {
                    console.log('[PREVIEW] 📷 No annotations, returning original data');
                    return screenshotData?.dataUrl || '';
                }

                // 🔧 创建原始尺寸的Canvas来绘制标注（避免DPR放大和尺寸不匹配）
                const canvas = document.createElement('canvas');

                // 🔧 关键修复：使用原始截图尺寸而不是Canvas CSS尺寸
                const originalWidth = screenshotData?.width;
                const originalHeight = screenshotData?.height;

                if (originalWidth && originalHeight) {
                    canvas.width = originalWidth;
                    canvas.height = originalHeight;
                    console.log('[PREVIEW] 📏 Annotation canvas original size:', canvas.width, 'x', canvas.height);
                } else {
                    // 使用默认尺寸
                    canvas.width = 800;
                    canvas.height = 600;
                    console.log('[PREVIEW] ⚠️ Using default canvas size:', canvas.width, 'x', canvas.height);
                }

                const ctx = canvas.getContext('2d');

                // 设置白色背景
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制所有标注
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';

                // 🔧 计算缩放因子（从标注Canvas尺寸到原始尺寸）
                const previewCanvas = document.getElementById('previewCanvas');
                const annotationCanvas = document.getElementById('annotationCanvas');

                let scaleFactorX = 1;
                let scaleFactorY = 1;

                if (annotationCanvas) {
                    // 从标注Canvas尺寸缩放到原始尺寸
                    scaleFactorX = canvas.width / annotationCanvas.width;
                    scaleFactorY = canvas.height / annotationCanvas.height;
                } else if (previewCanvas) {
                    // 从预览Canvas实际尺寸缩放到原始尺寸
                    scaleFactorX = canvas.width / previewCanvas.width;
                    scaleFactorY = canvas.height / previewCanvas.height;
                }

                console.log('[PREVIEW] 🔧 Scale factors for annotation drawing:', scaleFactorX, scaleFactorY);
                console.log('[PREVIEW] 🔧 Target canvas size:', canvas.width, 'x', canvas.height);
                console.log('[PREVIEW] 🔧 Source annotation canvas size:', annotationCanvas?.width, 'x', annotationCanvas?.height);

                shapes.forEach(shape => {
                    ctx.save();
                    ctx.strokeStyle = shape.color || '#ff0000';
                    ctx.lineWidth = (shape.strokeWidth || 3) * scaleFactorX; // 缩放线宽

                    // 🔧 创建缩放后的形状副本
                    const scaledShape = scaleShapeForExport(shape, scaleFactorX, scaleFactorY);

                    switch (shape.type) {
                        case 'rectangle':
                            drawRectangleOnContext(ctx, scaledShape);
                            break;
                        case 'circle':
                        case 'ellipse':
                            drawEllipseOnContext(ctx, scaledShape);
                            break;
                        case 'arrow':
                            drawArrowOnContext(ctx, scaledShape);
                            break;
                        case 'line':
                            drawLineOnContext(ctx, scaledShape);
                            break;
                        case 'brush':
                        case 'pen':
                            drawBrushStrokeOnContext(ctx, scaledShape);
                            break;
                        case 'text':
                            drawTextOnContext(ctx, scaledShape);
                            break;
                        case 'mosaic':
                            drawMosaicOnContext(ctx, scaledShape);
                            break;
                        case 'number':
                            drawNumberOnContext(ctx, scaledShape);
                            break;
                    }

                    ctx.restore();
                });

                console.log('[PREVIEW] 🎨 Annotation-only image created successfully');
                return canvas.toDataURL('image/png');

            } catch (error) {
                console.error('[PREVIEW] ❌ Failed to create annotation-only image:', error);
                return screenshotData?.dataUrl || '';
            }
        }

        // 🔧 缩放形状坐标用于导出
        function scaleShapeForExport(shape, scaleX, scaleY) {
            const scaledShape = { ...shape };

            // 缩放基本坐标
            if (shape.startX !== undefined) scaledShape.startX = shape.startX * scaleX;
            if (shape.startY !== undefined) scaledShape.startY = shape.startY * scaleY;
            if (shape.endX !== undefined) scaledShape.endX = shape.endX * scaleX;
            if (shape.endY !== undefined) scaledShape.endY = shape.endY * scaleY;
            if (shape.x !== undefined) scaledShape.x = shape.x * scaleX;
            if (shape.y !== undefined) scaledShape.y = shape.y * scaleY;

            // 缩放画笔点数组
            if (shape.points && Array.isArray(shape.points)) {
                scaledShape.points = [];
                for (let i = 0; i < shape.points.length; i += 2) {
                    scaledShape.points.push(shape.points[i] * scaleX);     // x坐标
                    scaledShape.points.push(shape.points[i + 1] * scaleY); // y坐标
                }
            }

            // 缩放字体大小、半径和线宽
            if (shape.fontSize !== undefined) scaledShape.fontSize = shape.fontSize * scaleX;
            if (shape.radius !== undefined) scaledShape.radius = shape.radius * scaleX;
            // 🔧 修复：缩放线宽以确保与实时预览一致
            if (shape.strokeWidth !== undefined) scaledShape.strokeWidth = shape.strokeWidth * Math.min(scaleX, scaleY);

            return scaledShape;
        }

        // 🔧 在独立Context上绘制图形的辅助函数
        function drawRectangleOnContext(ctx, shape) {
            const x = Math.min(shape.startX, shape.endX);
            const y = Math.min(shape.startY, shape.endY);
            const width = Math.abs(shape.endX - shape.startX);
            const height = Math.abs(shape.endY - shape.startY);
            ctx.strokeRect(x, y, width, height);
        }

        function drawEllipseOnContext(ctx, shape) {
            const centerX = (shape.startX + shape.endX) / 2;
            const centerY = (shape.startY + shape.endY) / 2;
            const radiusX = Math.abs(shape.endX - shape.startX) / 2;
            const radiusY = Math.abs(shape.endY - shape.startY) / 2;

            ctx.beginPath();
            ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
            ctx.stroke();
        }

        function drawLineOnContext(ctx, shape) {
            ctx.beginPath();
            ctx.moveTo(shape.startX, shape.startY);
            ctx.lineTo(shape.endX, shape.endY);
            ctx.stroke();
        }

        function drawArrowOnContext(ctx, shape) {
            // 绘制主线
            ctx.beginPath();
            ctx.moveTo(shape.startX, shape.startY);
            ctx.lineTo(shape.endX, shape.endY);
            ctx.stroke();

            // 计算箭头
            const angle = Math.atan2(shape.endY - shape.startY, shape.endX - shape.startX);
            const arrowLength = 15;
            const arrowAngle = Math.PI / 6;

            // 绘制箭头头部
            ctx.beginPath();
            ctx.moveTo(shape.endX, shape.endY);
            ctx.lineTo(
                shape.endX - arrowLength * Math.cos(angle - arrowAngle),
                shape.endY - arrowLength * Math.sin(angle - arrowAngle)
            );
            ctx.moveTo(shape.endX, shape.endY);
            ctx.lineTo(
                shape.endX - arrowLength * Math.cos(angle + arrowAngle),
                shape.endY - arrowLength * Math.sin(angle + arrowAngle)
            );
            ctx.stroke();
        }

        function drawBrushStrokeOnContext(ctx, shape) {
            if (!shape.points || shape.points.length < 4) return;

            ctx.beginPath();
            ctx.moveTo(shape.points[0], shape.points[1]);

            for (let i = 2; i < shape.points.length - 2; i += 2) {
                const xc = (shape.points[i] + shape.points[i + 2]) / 2;
                const yc = (shape.points[i + 1] + shape.points[i + 3]) / 2;
                ctx.quadraticCurveTo(shape.points[i], shape.points[i + 1], xc, yc);
            }

            if (shape.points.length >= 4) {
                const lastIndex = shape.points.length - 2;
                ctx.quadraticCurveTo(
                    shape.points[lastIndex - 2],
                    shape.points[lastIndex - 1],
                    shape.points[lastIndex],
                    shape.points[lastIndex + 1]
                );
            }

            ctx.stroke();
        }

        function drawTextOnContext(ctx, shape) {
            if (!shape.text) return;

            ctx.fillStyle = shape.color;
            ctx.font = `${shape.fontSize || 16}px ${shape.fontFamily || 'Arial'}`;
            ctx.textBaseline = 'top';

            // 添加文字描边
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeText(shape.text, shape.x, shape.y);

            // 绘制文字
            ctx.fillText(shape.text, shape.x, shape.y);
        }

        function drawMosaicOnContext(ctx, shape) {
            const x = Math.min(shape.startX, shape.endX);
            const y = Math.min(shape.startY, shape.endY);
            const width = Math.abs(shape.endX - shape.startX);
            const height = Math.abs(shape.endY - shape.startY);

            if (width < 1 || height < 1) return;

            const mosaicSize = 8;
            const cols = Math.ceil(width / mosaicSize);
            const rows = Math.ceil(height / mosaicSize);

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    const blockX = x + col * mosaicSize;
                    const blockY = y + row * mosaicSize;
                    const blockWidth = Math.min(mosaicSize, width - col * mosaicSize);
                    const blockHeight = Math.min(mosaicSize, height - row * mosaicSize);

                    const gray = Math.floor(Math.random() * 100) + 100;
                    ctx.fillStyle = `rgb(${gray}, ${gray}, ${gray})`;
                    ctx.fillRect(blockX, blockY, blockWidth, blockHeight);
                }
            }

            ctx.strokeStyle = shape.color || '#666666';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, width, height);
        }

        function drawNumberOnContext(ctx, shape) {
            if (!shape.number) return;

            const radius = shape.radius || 15;
            const fontSize = shape.fontSize || 16;

            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(shape.x, shape.y, radius, 0, 2 * Math.PI);
            ctx.fillStyle = shape.backgroundColor || '#ffffff';
            ctx.fill();

            // 绘制边框
            ctx.strokeStyle = shape.color || '#ff0000';
            ctx.lineWidth = 2;
            ctx.stroke();

            // 绘制数字
            ctx.fillStyle = shape.color || '#ff0000';
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(shape.number.toString(), shape.x, shape.y);
        }

        // 🔧 修复：更新背景图片为标注后的图片，同时保留当前标注
        function updatePreviewWithAnnotatedImage(annotatedImageData) {
            console.log('[PREVIEW] 🖼️ Updating background with annotated image, preserving current annotations');

            const canvas = document.getElementById('previewCanvas');
            if (!canvas || !annotatedImageData) {
                console.warn('[PREVIEW] ⚠️ Cannot update preview - missing canvas or data');
                return;
            }

            // 🆕 保存当前的标注状态
            const currentShapes = [...shapes];
            const currentHistoryState = [...annotationHistory];
            const currentHistoryIndex = historyIndex;

            console.log('[PREVIEW] 💾 Preserving current annotation state:', {
                shapeCount: currentShapes.length,
                historySize: currentHistoryState.length,
                historyIndex: currentHistoryIndex
            });

            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = function() {
                console.log('[PREVIEW] 📏 Loaded annotated image:', img.naturalWidth, 'x', img.naturalHeight);

                // 🔧 更新背景图片（previewCanvas）
                if (screenshotData?.width && screenshotData?.height) {
                    renderImageToCanvas(canvas, img, screenshotData.width, screenshotData.height);
                    console.log('[PREVIEW] ✅ Background updated with annotated image');
                } else {
                    // Fallback：直接绘制
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    const cssWidth = parseFloat(canvas.style.width);
                    const cssHeight = parseFloat(canvas.style.height);
                    ctx.drawImage(img, 0, 0, cssWidth, cssHeight);
                    console.log('[PREVIEW] ✅ Background updated with fallback method');
                }

                // 🆕 更新screenshotData为新的合成图片
                screenshotData = {
                    ...screenshotData,
                    dataUrl: annotatedImageData,
                    lastSaved: Date.now()
                };

                // 🔧 修复：保存后清除所有已保存的标注，避免重复显示
                console.log('[PREVIEW] 🧹 Clearing saved annotations from display after background update');

                // 清除标注显示
                if (annotationContext) {
                    annotationContext.clearRect(0, 0, annotationContext.canvas.width, annotationContext.canvas.height);
                }

                // 🔧 修复：重置标注数组，因为标注已经合并到背景图片中
                const previousShapeCount = shapes.length;
                shapes.length = 0;

                // 重置历史记录，从新的背景开始
                annotationHistory.length = 0;
                historyIndex = -1;

                console.log('[PREVIEW] 🧹 Cleared', previousShapeCount, 'saved annotations from display');
                console.log('[PREVIEW] 🎨 Ready for new annotations on updated background');

                console.log('[PREVIEW] ✅ Background updated, annotation state preserved');
            };
            img.src = annotatedImageData;
        }

        // 显示临时通知
        function showTemporaryNotification(message, type = 'info') {
            console.log(`[PREVIEW] 📢 ${type.toUpperCase()}: ${message}`);

            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                animation: slideInRight 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;

            // 添加动画样式
            if (!document.getElementById('notificationStyles')) {
                const style = document.createElement('style');
                style.id = 'notificationStyles';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOutRight {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 显示保存成功通知
        function showSaveSuccessNotification(savedPath) {
            showTemporaryNotification(`保存成功！文件已保存到: ${savedPath}`, 'success');
        }

        // 显示保存错误通知
        function showSaveErrorNotification(errorMessage) {
            console.log('[PREVIEW] ❌ Save error notification:', errorMessage);
            showTemporaryNotification(`保存失败: ${errorMessage}`, 'error');
        }

        // 显示复制成功通知
        function showCopySuccessNotification() {
            console.log('[PREVIEW] ✅ Copy success notification');
            showTemporaryNotification('📋 标注已复制到剪贴板', 'success');
        }

        // 显示复制错误通知
        function showCopyErrorNotification(errorMessage) {
            console.log('[PREVIEW] ❌ Copy error notification:', errorMessage);
            showTemporaryNotification(`复制失败: ${errorMessage}`, 'error');
        }

        // 🔧 Helper function to compare shapes for undo/redo operations
        function shapesEqual(shape1, shape2) {
            if (!shape1 || !shape2) return false;
            if (shape1.type !== shape2.type) return false;

            // Compare basic properties
            if (shape1.startX !== shape2.startX || shape1.startY !== shape2.startY ||
                shape1.endX !== shape2.endX || shape1.endY !== shape2.endY ||
                shape1.color !== shape2.color || shape1.lineWidth !== shape2.lineWidth) {
                return false;
            }

            // For brush strokes, compare points array
            if (shape1.type === 'brush' && shape1.points && shape2.points) {
                if (shape1.points.length !== shape2.points.length) return false;
                for (let i = 0; i < shape1.points.length; i++) {
                    if (shape1.points[i].x !== shape2.points[i].x ||
                        shape1.points[i].y !== shape2.points[i].y) {
                        return false;
                    }
                }
            }

            // For text, compare text content and position
            if (shape1.type === 'text') {
                return shape1.text === shape2.text && shape1.x === shape2.x && shape1.y === shape2.y;
            }

            return true;
        }

        // 🔧 Helper function to compare shapes for undo/redo operations
        function shapesEqual(shape1, shape2) {
            if (!shape1 || !shape2) return false;
            if (shape1.type !== shape2.type) return false;

            // Compare basic properties
            if (shape1.startX !== shape2.startX || shape1.startY !== shape2.startY ||
                shape1.endX !== shape2.endX || shape1.endY !== shape2.endY ||
                shape1.color !== shape2.color || shape1.lineWidth !== shape2.lineWidth) {
                return false;
            }

            // For brush strokes, compare points array
            if (shape1.type === 'brush' && shape1.points && shape2.points) {
                if (shape1.points.length !== shape2.points.length) return false;
                for (let i = 0; i < shape1.points.length; i++) {
                    if (shape1.points[i].x !== shape2.points[i].x ||
                        shape1.points[i].y !== shape2.points[i].y) {
                        return false;
                    }
                }
            }

            // For text, compare text content and position
            if (shape1.type === 'text') {
                return shape1.text === shape2.text && shape1.x === shape2.x && shape1.y === shape2.y;
            }

            return true;
        }

        // 发送工具反馈
        function sendToolFeedback() {
            const undoRedoState = undoRedoManager ? undoRedoManager.getState() : { canUndo: false, canRedo: false };

            const toolStatus = {
                text: { enabled: true, active: currentAnnotationTool === 'text' },
                arrow: { enabled: true, active: currentAnnotationTool === 'arrow' },
                rectangle: { enabled: true, active: currentAnnotationTool === 'rectangle' },
                circle: { enabled: true, active: currentAnnotationTool === 'circle' },
                brush: { enabled: true, active: currentAnnotationTool === 'brush' },
                undo: { enabled: undoRedoState.canUndo, active: false, count: undoRedoState.historyLength },
                redo: { enabled: undoRedoState.canRedo, active: false, count: undoRedoState.redoStackLength }
            };

            sendPreviewEvent('tool-feedback', {
                toolStatus: toolStatus,
                undoRedoState: undoRedoState,
                currentTool: currentAnnotationTool,
                windowState: windowState,
                isEditingMode: isEditingMode,
                timestamp: Date.now()
            });
        }



        // 处理保存操作
        async function handleAnnotationSave() {
            console.log('[PREVIEW] 💾 Handling save operation');

            try {
                // 检查Tauri API可用性
                if (!window.__TAURI__ || !window.__TAURI__.core || !window.__TAURI__.core.invoke) {
                    throw new Error('Tauri API not available in this context');
                }

                // 获取带标注的图片数据
                const annotatedImageData = await getAnnotatedImageData();

                if (!annotatedImageData) {
                    throw new Error('Failed to get annotated image data');
                }

                console.log('[PREVIEW] 📤 Sending annotated image data to backend for saving');

                // 🔧 使用正确的后端函数 save_edited_screenshot
                const result = await window.__TAURI__.core.invoke('save_edited_screenshot', {
                    request: {
                        imageDataUrl: annotatedImageData,
                        finalPath: null // 让后端自动生成路径
                    }
                });

                console.log('[PREVIEW] ✅ Save completed:', result);

                // 显示保存成功的提示
                showSaveSuccessNotification(result.path);

                // 🔧 修复：保存成功后不清除标注数据，允许继续添加标注
                setTimeout(() => {
                    // 🆕 保存成功后保留标注数据，只更新预览图片
                    console.log('[PREVIEW] 🔄 Save completed, keeping annotation data for further editing');

                    // 更新预览图片为标注后的图片（作为新的背景）
                    updatePreviewWithAnnotatedImage(annotatedImageData);

                    // 🆕 重要：保持编辑模式，不切换回预览模式
                    console.log('[PREVIEW] 🎨 Staying in editing mode for continued annotation');

                    // 🆕 发送保存完成事件，但保持编辑状态
                    sendPreviewEvent('annotation-save-completed', {
                        path: result.path,
                        canContinueEditing: true,
                        currentShapeCount: shapes.length,
                        timestamp: Date.now()
                    });

                    console.log('[PREVIEW] 🔄 Save completed, ready for more annotations');
                }, 1000); // 延迟1秒让用户看到保存成功提示

            } catch (error) {
                console.error('[PREVIEW] ❌ Save failed:', error);

                // 提供更详细的错误信息
                let errorMessage = error.message || 'Unknown error';
                if (errorMessage.includes('Tauri API not available')) {
                    errorMessage = '保存功能暂时不可用，请重试';
                } else if (errorMessage.includes('Failed to get annotated image data')) {
                    errorMessage = '无法获取标注图片数据，请重试';
                }

                showSaveErrorNotification(errorMessage);
            }
        }

        // 🔧 保存成功后清除标注数据，避免重复显示
        function clearAnnotationDataAfterSave() {
            console.log('[PREVIEW] 🧹 Clearing annotation data after successful save');

            // 清除标注数组
            const previousShapeCount = shapes.length;
            shapes.length = 0;

            // 清除撤销历史
            annotationHistory.length = 0;
            currentHistoryIndex = -1;

            // 清除当前标注状态
            currentShape = null;
            annotationStartPos = null;

            // 清除标注Canvas
            if (annotationContext) {
                annotationContext.clearRect(0, 0, annotationContext.canvas.width, annotationContext.canvas.height);
            }

            console.log('[PREVIEW] ✅ Annotation data cleared:', {
                previousShapeCount,
                currentShapeCount: shapes.length,
                historyCleared: annotationHistory.length === 0
            });
        }

        // 处理复制操作
        async function handleAnnotationCopy() {
            console.log('[PREVIEW] 📋 Handling copy operation');

            try {
                // 检查Tauri API可用性
                if (!window.__TAURI__ || !window.__TAURI__.core || !window.__TAURI__.core.invoke) {
                    throw new Error('Tauri API not available in this context');
                }

                // 获取带标注的图片数据
                const annotatedImageData = await getAnnotatedImageData();

                if (!annotatedImageData) {
                    throw new Error('Failed to get annotated image data');
                }

                // 移除data URL前缀，获取base64数据
                const base64Data = annotatedImageData.split(',')[1];

                // 🔧 使用正确的后端函数 copy_image_to_clipboard
                const result = await window.__TAURI__.core.invoke('copy_image_to_clipboard', {
                    request: {
                        imageData: base64Data
                    }
                });

                console.log('[PREVIEW] ✅ Copy completed:', result);

                // 显示复制成功的提示
                showCopySuccessNotification();

            } catch (error) {
                console.error('[PREVIEW] ❌ Copy failed:', error);

                let errorMessage = error.message || 'Unknown error';
                if (errorMessage.includes('Tauri API not available')) {
                    errorMessage = '复制功能暂时不可用，请重试';
                }

                showCopyErrorNotification(errorMessage);
            }
        }

        // 处理关闭操作
        async function handleAnnotationClose() {
            console.log('[PREVIEW] ✕ Handling close operation');

            try {
                // 关闭独立工具栏
                if (independentToolbarId) {
                    await window.__TAURI__.core.invoke('close_toolbar_window', {
                        toolbarId: independentToolbarId
                    });
                }

                // 关闭预览窗口
                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                await currentWindow.close();

            } catch (error) {
                console.error('[PREVIEW] ❌ Close failed:', error);
            }
        }

        // 添加标注到Canvas
        function addAnnotationToCanvas(annotation) {
            console.log('[PREVIEW] 🎨 Adding annotation to canvas:', annotation);

            // 这里需要与具体的Canvas/Fabric.js实现集成
            // 示例实现 - 需要根据实际情况调整
            switch (annotation.type) {
                case 'text':
                    // 添加文字标注
                    break;
                case 'arrow':
                    // 添加箭头标注
                    break;
                case 'rectangle':
                    // 添加矩形标注
                    break;
                case 'circle':
                    // 添加圆形标注
                    break;
                case 'brush':
                    // 添加画笔标注
                    break;
            }
        }

        // 从Canvas移除标注
        function removeAnnotationFromCanvas(annotation) {
            console.log('[PREVIEW] 🗑️ Removing annotation from canvas:', annotation);

            // 这里需要与具体的Canvas/Fabric.js实现集成
            // 根据annotation.id找到并移除对应的Canvas对象
        }

        // 更新Canvas中的标注
        function updateAnnotationInCanvas(annotationId, newData) {
            console.log('[PREVIEW] ✏️ Updating annotation in canvas:', annotationId, newData);

            // 这里需要与具体的Canvas/Fabric.js实现集成
            // 根据annotationId找到对应的Canvas对象并更新其属性
        }



        // 显示保存成功通知
        function showSaveSuccessNotification(savedPath) {
            console.log('[PREVIEW] ✅ Save success notification:', savedPath);
            // 可以在这里显示UI通知
        }

        // 显示保存错误通知
        function showSaveErrorNotification(errorMessage) {
            console.log('[PREVIEW] ❌ Save error notification:', errorMessage);
            // 可以在这里显示错误UI通知
        }

        // 显示复制成功通知
        function showCopySuccessNotification() {
            console.log('[PREVIEW] ✅ Copy success notification');
            // 可以在这里显示UI通知
        }

        // 显示复制错误通知
        function showCopyErrorNotification(errorMessage) {
            console.log('[PREVIEW] ❌ Copy error notification:', errorMessage);
            // 可以在这里显示错误UI通知
        }

        // 创建PreviewCommunicator类的简化实现
        class PreviewCommunicator {
            constructor(windowId) {
                this.windowId = windowId;
                this.connectedToolbars = new Set();
                this.eventListeners = new Map();
            }

            async initialize() {
                console.log('[PREVIEW-COMM] 🔧 Initializing preview communicator');
                // 基本的初始化逻辑已经在外部函数中实现
                console.log('[PREVIEW-COMM] ✅ Preview communicator initialized');
            }

            async disconnect() {
                console.log('[PREVIEW-COMM] 🔌 Disconnecting preview communicator');
                this.connectedToolbars.clear();
            }
        }

        // ==================== 工具栏功能实现 ====================

        // 🆕 创建独立工具栏窗口
        async function createIndependentToolbar(previewBounds, position, isHorizontal) {
            console.log('[PREVIEW] 🪟 Attempting to create independent toolbar window');

            if (!window.__TAURI__ || !window.__TAURI__.core || !window.__TAURI__.core.invoke) {
                throw new Error('Tauri API not available');
            }

            const toolbarWidth = isHorizontal ? 300 : 60;
            const toolbarHeight = isHorizontal ? 60 : 200;
            const margin = 20;

            // 计算工具栏在屏幕上的位置
            let toolbarX, toolbarY;

            switch (position) {
                case 'right':
                    toolbarX = previewBounds.right + margin;
                    toolbarY = previewBounds.top + (previewBounds.height - toolbarHeight) / 2;
                    break;
                case 'left':
                    toolbarX = previewBounds.left - margin - toolbarWidth;
                    toolbarY = previewBounds.top + (previewBounds.height - toolbarHeight) / 2;
                    break;
                case 'bottom':
                    toolbarX = previewBounds.left + (previewBounds.width - toolbarWidth) / 2;
                    toolbarY = previewBounds.bottom + margin;
                    break;
                case 'top':
                    toolbarX = previewBounds.left + (previewBounds.width - toolbarWidth) / 2;
                    toolbarY = previewBounds.top - margin - toolbarHeight;
                    break;
                default:
                    throw new Error(`Unsupported position: ${position}`);
            }

            // 调用后端创建独立工具栏窗口
            try {
                const toolbarWindowId = await window.__TAURI__.core.invoke('create_independent_toolbar_window', {
                    x: Math.round(toolbarX),
                    y: Math.round(toolbarY),
                    width: toolbarWidth,
                    height: toolbarHeight,
                    previewWindowId: 'screenshot-preview',
                    position: position,
                    isHorizontal: isHorizontal
                });

                console.log('[PREVIEW] ✅ Independent toolbar window created:', toolbarWindowId);

                // 隐藏当前窗口中的嵌入式工具栏
                const embeddedToolbar = document.getElementById('toolbarContainer');
                if (embeddedToolbar) {
                    embeddedToolbar.style.display = 'none';
                }

                return toolbarWindowId;
            } catch (error) {
                console.error('[PREVIEW] ❌ Failed to create independent toolbar window:', error);
                throw error;
            }
        }

        // 初始化工具栏
        function initializeToolbar() {
            console.log('[PREVIEW] 🔧 Initializing toolbar functionality');

            const toolbarContainer = document.getElementById('toolbarContainer');
            const previewWrapper = document.getElementById('previewWrapper');

            if (!toolbarContainer || !previewWrapper) {
                console.warn('[PREVIEW] ⚠️ Toolbar or preview elements not found');
                return;
            }

            // 预览区域鼠标事件 - 显示工具栏
            previewWrapper.addEventListener('mouseenter', () => {
                updateToolbarPosition();
                showToolbar();
            });
            previewWrapper.addEventListener('mouseleave', startHideTimer);

            // 工具栏区域鼠标事件 - 保持显示
            toolbarContainer.addEventListener('mouseenter', cancelHideTimer);
            toolbarContainer.addEventListener('mouseleave', startHideTimer);

            // 工具按钮点击事件
            toolbarContainer.addEventListener('click', handleToolbarClick);

            // 键盘快捷键
            document.addEventListener('keydown', handleToolbarKeydown);

            // 窗口大小变化时重新计算位置
            window.addEventListener('resize', updateToolbarPosition);

            console.log('[PREVIEW] ✅ Toolbar event listeners added');
        }

        // 智能计算工具栏最佳位置 - 修复版本
        async function updateToolbarPosition() {
            console.log('[PREVIEW] 🔧 Calculating optimal toolbar position (Fixed Version)');

            const previewWrapper = document.getElementById('previewWrapper');
            const toolbarContainer = document.getElementById('toolbarContainer');
            const previewCanvas = document.getElementById('previewCanvas');

            if (!previewWrapper || !toolbarContainer || !previewCanvas) {
                console.warn('[PREVIEW] ⚠️ Required elements not found for positioning');
                return;
            }

            try {
                // 🆕 获取当前预览窗口在屏幕上的绝对位置
                const windowBounds = await window.__TAURI__.webviewWindow.getCurrentWebviewWindow().outerPosition();
                const windowSize = await window.__TAURI__.webviewWindow.getCurrentWebviewWindow().outerSize();

                console.log('[PREVIEW] 📊 Window bounds on screen:', windowBounds);
                console.log('[PREVIEW] 📊 Window size:', windowSize);

                // 获取预览内容在窗口内的相对位置
                const previewRect = previewWrapper.getBoundingClientRect();
                const canvasRect = previewCanvas.getBoundingClientRect();

                // 🆕 计算预览内容在屏幕上的绝对位置
                const screenPreviewBounds = {
                    left: windowBounds.x + previewRect.left,
                    top: windowBounds.y + previewRect.top,
                    right: windowBounds.x + previewRect.right,
                    bottom: windowBounds.y + previewRect.bottom,
                    width: previewRect.width,
                    height: previewRect.height
                };

                console.log('[PREVIEW] 📊 Preview bounds on screen:', screenPreviewBounds);

                // 🆕 获取屏幕尺寸（通过Tauri API）
                const screenSize = await window.__TAURI__.webviewWindow.getCurrentWebviewWindow().currentMonitor();
                const screenWidth = screenSize.size.width;
                const screenHeight = screenSize.size.height;

                console.log('[PREVIEW] 📊 Screen size:', { width: screenWidth, height: screenHeight });

                // 工具栏尺寸（估算）
                const toolbarWidth = 60;  // 垂直布局宽度
                const toolbarHeight = 200; // 垂直布局高度
                const toolbarWidthHorizontal = 300; // 水平布局宽度
                const toolbarHeightHorizontal = 60;  // 水平布局高度

                const margin = 20; // 与边缘的最小距离

                // 🆕 计算各个位置在屏幕上的可用空间
                const spaces = {
                    right: screenWidth - screenPreviewBounds.right - margin,
                    left: screenPreviewBounds.left - margin,
                    top: screenPreviewBounds.top - margin,
                    bottom: screenHeight - screenPreviewBounds.bottom - margin
                };

                console.log('[PREVIEW] 📊 Available screen spaces:', spaces);

                // 选择最佳位置的优先级：右侧 > 左侧 > 下方 > 上方
                let bestPosition = 'right';
                let useHorizontalLayout = false;

                if (spaces.right >= toolbarWidth) {
                    bestPosition = 'right';
                    useHorizontalLayout = false;
                } else if (spaces.left >= toolbarWidth) {
                    bestPosition = 'left';
                    useHorizontalLayout = false;
                } else if (spaces.bottom >= toolbarHeightHorizontal) {
                    bestPosition = 'bottom';
                    useHorizontalLayout = true;
                } else if (spaces.top >= toolbarHeightHorizontal) {
                    bestPosition = 'top';
                    useHorizontalLayout = true;
                } else {
                    // 空间不足时，选择空间最大的位置
                    const maxSpace = Math.max(...Object.values(spaces));
                    bestPosition = Object.keys(spaces).find(key => spaces[key] === maxSpace);
                    useHorizontalLayout = (bestPosition === 'top' || bestPosition === 'bottom');
                }
            } catch (error) {
                console.warn('[PREVIEW] ⚠️ Failed to get screen bounds, falling back to window-relative positioning:', error);
                // 降级到原有的窗口相对定位逻辑
                return updateToolbarPositionFallback();
            }

            console.log('[PREVIEW] 🎯 Selected position:', bestPosition, 'horizontal:', useHorizontalLayout);

            // 🆕 尝试创建独立的工具栏窗口（如果支持）
            try {
                await createIndependentToolbar(screenPreviewBounds, bestPosition, useHorizontalLayout);
                console.log('[PREVIEW] ✅ Independent toolbar window created successfully');
                return;
            } catch (error) {
                console.warn('[PREVIEW] ⚠️ Failed to create independent toolbar, using embedded fallback:', error);
                // 继续使用嵌入式工具栏作为降级方案
            }

            // 应用布局方向
            toolbarContainer.classList.remove('vertical', 'horizontal');
            toolbarContainer.classList.add(useHorizontalLayout ? 'horizontal' : 'vertical');

            // 移除旧的位置类
            toolbarContainer.classList.remove('position-right', 'position-left', 'position-top', 'position-bottom');
            toolbarContainer.classList.add(`position-${bestPosition}`);

            // 🆕 计算具体位置（基于屏幕坐标，但转换为窗口相对坐标）
            let screenLeft, screenTop;

            switch (bestPosition) {
                case 'right':
                    screenLeft = screenPreviewBounds.right + margin;
                    screenTop = screenPreviewBounds.top + (screenPreviewBounds.height - toolbarHeight) / 2;
                    break;
                case 'left':
                    screenLeft = screenPreviewBounds.left - margin - toolbarWidth;
                    screenTop = screenPreviewBounds.top + (screenPreviewBounds.height - toolbarHeight) / 2;
                    break;
                case 'bottom':
                    screenLeft = screenPreviewBounds.left + (screenPreviewBounds.width - toolbarWidthHorizontal) / 2;
                    screenTop = screenPreviewBounds.bottom + margin;
                    break;
                case 'top':
                    screenLeft = screenPreviewBounds.left + (screenPreviewBounds.width - toolbarWidthHorizontal) / 2;
                    screenTop = screenPreviewBounds.top - margin - toolbarHeightHorizontal;
                    break;
            }

            // 🆕 转换屏幕坐标为窗口相对坐标
            const windowBounds = await window.__TAURI__.webviewWindow.getCurrentWebviewWindow().outerPosition();
            const left = screenLeft - windowBounds.x;
            const top = screenTop - windowBounds.y;

            // 确保工具栏不超出窗口边界（降级保护）
            const safeLeft = Math.max(0, Math.min(left, window.innerWidth - (useHorizontalLayout ? toolbarWidthHorizontal : toolbarWidth)));
            const safeTop = Math.max(0, Math.min(top, window.innerHeight - (useHorizontalLayout ? toolbarHeightHorizontal : toolbarHeight)));

            // 应用位置
            toolbarContainer.style.left = `${safeLeft}px`;
            toolbarContainer.style.top = `${safeTop}px`;

            // 移除旧的定位样式
            toolbarContainer.style.right = 'auto';
            toolbarContainer.style.bottom = 'auto';
            toolbarContainer.style.transform = 'none';

            toolbarPosition = bestPosition;
            previewBounds = screenPreviewBounds;

            console.log('[PREVIEW] ✅ Toolbar positioned at:', {
                screenLeft, screenTop,
                windowLeft: safeLeft, windowTop: safeTop,
                position: bestPosition
            });
        }

        // 🆕 降级函数：使用原有的窗口相对定位逻辑
        function updateToolbarPositionFallback() {
            console.log('[PREVIEW] 🔧 Using fallback positioning logic');

            const previewWrapper = document.getElementById('previewWrapper');
            const toolbarContainer = document.getElementById('toolbarContainer');

            if (!previewWrapper || !toolbarContainer) return;

            const previewRect = previewWrapper.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // 简化的定位逻辑：尽量放在预览区域外部
            const margin = 20;
            const toolbarWidth = 60;
            const toolbarHeight = 200;

            let left, top;

            // 优先尝试右侧
            if (previewRect.right + margin + toolbarWidth <= viewportWidth) {
                left = previewRect.right + margin;
                top = previewRect.top;
            }
            // 其次尝试左侧
            else if (previewRect.left - margin - toolbarWidth >= 0) {
                left = previewRect.left - margin - toolbarWidth;
                top = previewRect.top;
            }
            // 最后放在下方
            else {
                left = Math.max(margin, previewRect.left);
                top = Math.min(previewRect.bottom + margin, viewportHeight - toolbarHeight - margin);
            }

            toolbarContainer.style.left = `${left}px`;
            toolbarContainer.style.top = `${top}px`;

            console.log('[PREVIEW] ✅ Fallback toolbar positioned at:', { left, top });
        }

        // 显示工具栏
        function showToolbar() {
            console.log('[PREVIEW] 🔧 Showing toolbar');
            const toolbarContainer = document.getElementById('toolbarContainer');
            if (toolbarContainer) {
                toolbarContainer.classList.remove('hidden');
                isToolbarVisible = true;
                cancelHideTimer();
            }
        }

        // 隐藏工具栏
        function hideToolbar() {
            console.log('[PREVIEW] 🔧 Hiding toolbar');
            const toolbarContainer = document.getElementById('toolbarContainer');
            if (toolbarContainer) {
                toolbarContainer.classList.add('hidden');
                isToolbarVisible = false;
            }
        }

        // 开始隐藏计时器（10秒后自动隐藏）
        function startHideTimer() {
            console.log('[PREVIEW] ⏰ Starting toolbar hide timer (10s)');
            cancelHideTimer();
            toolbarHideTimer = setTimeout(() => {
                hideToolbar();
                console.log('[PREVIEW] ⏰ Toolbar auto-hidden after 10 seconds');
            }, 10000);
        }

        // 取消隐藏计时器
        function cancelHideTimer() {
            if (toolbarHideTimer) {
                clearTimeout(toolbarHideTimer);
                toolbarHideTimer = null;
                console.log('[PREVIEW] ⏰ Toolbar hide timer cancelled');
            }
        }

        // 处理工具栏点击事件
        function handleToolbarClick(event) {
            const button = event.target.closest('.toolbar-button');
            const colorOption = event.target.closest('.color-option');

            if (button) {
                const tool = button.dataset.tool;
                const action = button.dataset.action;

                if (tool) {
                    selectTool(tool, button);
                } else if (action) {
                    handleAction(action);
                }
            } else if (colorOption) {
                selectColor(colorOption.dataset.color);
            }

            // 重置隐藏计时器
            startHideTimer();
        }

        // 选择工具
        function selectTool(tool, button) {
            console.log('[PREVIEW] 🔧 Tool selected:', tool);

            // 移除其他工具的active状态
            document.querySelectorAll('.toolbar-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 设置当前工具为active
            button.classList.add('active');

            // 🔧 CRITICAL FIX: 统一工具选择变量，确保注解系统使用正确的工具
            currentTool = tool;
            currentAnnotationTool = tool; // 同步更新注解工具

            // 🔧 CRITICAL FIX: 立即更新光标样式
            updateCursorForAnnotationTool();

            console.log('[PREVIEW] 🎨 Tool synchronized:', {
                currentTool: currentTool,
                currentAnnotationTool: currentAnnotationTool
            });

            // 这里为后续Fabric.js集成预留接口
            if (window.fabricCanvas) {
                // 未来集成Fabric.js时的工具切换逻辑
                console.log('[PREVIEW] 🎨 Fabric.js tool switching (placeholder)');
            }
        }

        // 选择颜色
        function selectColor(color) {
            console.log('[PREVIEW] 🎨 Color selected:', color);
            currentColor = color;

            // 更新颜色选择器显示
            const colorPicker = document.getElementById('colorPicker');
            if (colorPicker) {
                colorPicker.style.background = color;
            }

            // 这里为后续Fabric.js集成预留接口
            if (window.fabricCanvas) {
                // 未来集成Fabric.js时的颜色设置逻辑
                console.log('[PREVIEW] 🎨 Fabric.js color setting (placeholder)');
            }
        }

        // 处理操作按钮
        function handleAction(action) {
            console.log('[PREVIEW] 🔧 Action triggered:', action);

            switch (action) {
                case 'save':
                    // 保存功能已移除 - 双击编辑功能已禁用
                    console.log('[PREVIEW] Save action disabled - double-click to edit feature removed');
                    break;
                case 'close':
                    // 关闭功能 - 关闭预览窗口
                    if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
                        const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                        currentWindow.close();
                    }
                    break;
            }
        }

        // 处理工具栏键盘快捷键
        function handleToolbarKeydown(event) {
            if (!isToolbarVisible) return;

            const key = event.key.toLowerCase();
            let toolButton = null;

            switch (key) {
                case 't':
                    toolButton = document.querySelector('[data-tool="text"]');
                    break;
                case 'a':
                    toolButton = document.querySelector('[data-tool="arrow"]');
                    break;
                case 'r':
                    toolButton = document.querySelector('[data-tool="rectangle"]');
                    break;
                case 'c':
                    toolButton = document.querySelector('[data-tool="circle"]');
                    break;
                case 'b':
                    toolButton = document.querySelector('[data-tool="brush"]');
                    break;
                case 's':
                    if (event.ctrlKey || event.metaKey) {
                        event.preventDefault();
                        handleAction('save');
                        return;
                    }
                    break;
            }

            if (toolButton) {
                event.preventDefault();
                selectTool(toolButton.dataset.tool, toolButton);
            }
        }

        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initialize);
        } else {
            initialize();
        }
    </script>
</body>
</html>
