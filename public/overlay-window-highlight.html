<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Window Highlight</title>

    <!-- 🆕 引入共享工具栏样式 -->
    <link rel="stylesheet" href="shared/toolbar-styles.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: transparent !important;
            cursor: crosshair;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
            pointer-events: auto; /* 🔧 CRITICAL FIX: 允许事件传递到子元素 */
        }

        .overlay-container {
            width: 100%;
            height: 100%;
            position: relative;
            pointer-events: auto; /* 🔧 CRITICAL FIX: 允许事件传递到事件捕获层 */
        }

        .window-highlight {
            position: absolute;
            border: 3px solid #FF6B35;
            background: rgba(255, 107, 53, 0.15);
            border-radius: 6px;
            display: none;
            pointer-events: none;
            transition: all 0.08s ease-out;
            box-shadow:
                0 0 0 1px rgba(255, 107, 53, 0.3),
                0 0 20px rgba(255, 107, 53, 0.6),
                inset 0 0 0 1px rgba(255, 255, 255, 0.2);
            z-index: 1000;
            animation: highlightPulse 2s ease-in-out infinite;
        }

        @keyframes highlightPulse {
            0%, 100% {
                box-shadow:
                    0 0 0 1px rgba(255, 107, 53, 0.3),
                    0 0 20px rgba(255, 107, 53, 0.6),
                    inset 0 0 0 1px rgba(255, 255, 255, 0.2);
            }
            50% {
                box-shadow:
                    0 0 0 1px rgba(255, 107, 53, 0.5),
                    0 0 30px rgba(255, 107, 53, 0.8),
                    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
            }
        }

        .window-info {
            position: absolute;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            font-size: 13px;
            display: none;
            pointer-events: none;
            max-width: 350px;
            min-width: 200px;
            z-index: 1001;
            border: 1px solid rgba(255, 107, 53, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .window-info .title {
            font-weight: 600;
            color: #FF6B35;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1.2;
            max-width: 100%;
            word-wrap: break-word;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .window-info .app-name {
            color: #bbb;
            font-size: 12px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .window-info .info-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .window-info .app-icon {
            font-size: 24px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 107, 53, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 107, 53, 0.2);
            flex-shrink: 0;
        }

        .window-info .info-text {
            flex: 1;
            min-width: 0;
        }

        .window-info .dimensions {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 11px;
            color: #888;
            background: rgba(255, 255, 255, 0.05);
            padding: 6px 10px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .instructions {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 14px;
            pointer-events: auto;
        }

        .instructions h3 {
            margin-bottom: 8px;
            color: #FF6B35;
        }

        /* 🔧 PRODUCTION: 模式指示器样式已移除 */

        .detection-stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 11px;
            min-width: 200px;
            pointer-events: auto;
        }

        .detection-stats .label {
            color: #FF6B35;
            font-weight: bold;
        }

        .crosshair {
            position: absolute;
            pointer-events: none;
            z-index: 999;
        }

        .crosshair::before,
        .crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255, 107, 53, 0.8);
        }

        .crosshair::before {
            width: 100vw;
            height: 1px;
            top: 0;
            left: -50vw;
        }

        .crosshair::after {
            width: 1px;
            height: 100vh;
            top: -50vh;
            left: 0;
        }

        /* 透明事件捕获层 */
        .event-capture-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            pointer-events: auto;
            z-index: 2000; /* 🔧 CRITICAL FIX: 确保事件捕获层在所有其他元素之上 */
        }

        /* 编辑模式相关样式 */
        .dimming-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1001;
            display: none;
            pointer-events: none;
        }

        .dimming-overlay.active {
            display: block;
        }

        .editing-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1003;
            display: none;
        }

        .editing-canvas.active {
            display: block;
            pointer-events: auto;
        }

        .screenshot-container {
            position: absolute;
            border: 3px solid #4CAF50;
            background: transparent;
            border-radius: 6px;
            box-shadow:
                0 0 0 1px rgba(76, 175, 80, 0.3),
                0 0 20px rgba(76, 175, 80, 0.6);
            z-index: 1004;
            display: none;
        }

        .screenshot-container.active {
            display: block;
        }

        /* 🔧 BUG FIX: 编辑模式样式指示器 */
        .screenshot-container.editing-mode {
            border: 3px solid #FF6B35;
            box-shadow:
                0 0 0 1px rgba(255, 107, 53, 0.3),
                0 0 20px rgba(255, 107, 53, 0.6);
            position: relative;
        }

        .screenshot-container.editing-mode::before {
            content: '🎨 编辑模式';
            position: absolute;
            top: -30px;
            left: 0;
            background: #FF6B35;
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10001;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 编辑模式下禁用鼠标交互的视觉提示 */
        .editing-mode-active {
            cursor: not-allowed !important;
        }

        .editing-mode-active * {
            pointer-events: none !important;
        }

        .screenshot-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 3px;
        }

        /* 🆕 标注Canvas样式 */
        #annotationCanvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: auto;
            cursor: crosshair;
            z-index: 6150;
        }

        #annotationCanvas.active {
            display: block !important;
        }

        /* 🆕 Overlay工具栏样式 */
        #overlaySharedToolbar {
            position: absolute;
            z-index: 6200;
        }

        #overlaySharedToolbar.active {
            display: block !important;
        }

        /* 🆕 工具栏定位样式 */
        .overlay-toolbar-positioned {
            transition: all 0.3s ease;
        }

        /* 🆕 标注模式下的特殊样式 */
        .annotation-mode .region-selector {
            pointer-events: none !important;
        }

        .annotation-mode .resize-handle {
            display: none !important;
        }

        /* 🗑️ REMOVED: Legacy regionToolbar styles - replaced by independent toolbar system */

        /* 🆕 区域锁定状态样式 */
        .region-locked {
            pointer-events: none !important;
            cursor: not-allowed !important;
        }

        .region-locked .resize-handle {
            display: none !important;
        }

        .region-locked::before {
            content: '🔒';
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(103, 58, 183, 0.9);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10;
        }

        /* 🆕 区域调整禁用时的提示 */
        .region-adjustment-disabled {
            position: relative;
        }

        .region-adjustment-disabled::after {
            content: 'Region locked for annotation';
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 10;
        }

        .region-adjustment-disabled:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="overlay-container">
        <!-- 🔧 PRODUCTION: 调试信息已移除，保持界面干净 -->

        <!-- 透明事件捕获层 -->
        <div class="event-capture-layer" id="eventCaptureLayer"></div>

        <!-- 背景变暗层 -->
        <div class="dimming-overlay" id="dimmingOverlay"></div>

        <!-- 编辑模式Canvas -->
        <canvas class="editing-canvas" id="editingCanvas"></canvas>

        <!-- 截图容器 -->
        <div class="screenshot-container" id="screenshotContainer">
            <img id="screenshotImage" alt="Selected window screenshot">
        </div>

        <div class="instructions">
            <h3>🎯 Smart Window Detection</h3>
            <p>Move mouse to highlight windows (with smart filtering)</p>
            <p>Click to select | <strong>ESC</strong> to exit</p>
        </div>

        <!-- 🔧 PRODUCTION: 模式指示器已移除，保持界面干净 -->

        <div class="window-highlight" id="windowHighlight"></div>
        <div class="window-info" id="windowInfo">
            <div class="info-header">
                <div class="app-icon" id="appIcon">🪟</div>
                <div class="info-text">
                    <div class="title">Window Title</div>
                    <div class="app-name">Application Name</div>
                </div>
            </div>
            <div class="dimensions">Position: (0, 0) | Size: 0×0</div>
        </div>

        <div class="crosshair" id="crosshair"></div>

        <!-- 🔧 BUG FIX: 区域选择UI -->
        <div id="regionSelector" style="display: none; position: absolute; border: 2px dashed #FFD700; background: rgba(255, 215, 0, 0.1); z-index: 5000; pointer-events: none;">
            <div style="position: absolute; top: -25px; left: 0; background: rgba(0, 0, 0, 0.8); color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; white-space: nowrap;">
                <span id="regionDimensions">0 x 0</span>
            </div>
        </div>

        <!-- 状态指示器 -->
        <div id="stateIndicator" style="position: fixed; top: 20px; right: 20px; background: rgba(0, 0, 0, 0.8); color: white; padding: 8px 12px; border-radius: 6px; font-size: 14px; font-weight: bold; z-index: 6000; display: none;">
            <span id="stateText">DETECTING</span>
        </div>

        <!-- 🆕 共享工具栏容器 (用于区域标注模式) -->
        <div id="overlaySharedToolbar" style="display: none; position: absolute; z-index: 6200;">
            <!-- 共享工具栏将动态插入到这里 -->
        </div>

        <!-- 🆕 标注Canvas (用于区域标注模式) -->
        <canvas id="annotationCanvas"
                style="display: none; position: absolute; z-index: 6150; pointer-events: auto; cursor: crosshair;"
                width="0"
                height="0">
        </canvas>

        <!-- 🗑️ REMOVED: Legacy regionToolbar - replaced by independent toolbar system -->
    </div>



    <script>
        // 🔧 CRITICAL FIX: 确保在DOM完全加载后再执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeOverlay);
        } else {
            initializeOverlay();
        }

        // 🔧 LOG REFACTOR: 统一日志系统，使用Tauri log插件
        let logAPI = null;
        let logInitialized = false;

        // 初始化日志API
        async function initializeLogging() {
            try {
                if (typeof window.__TAURI__ !== 'undefined' && window.__TAURI__.log) {
                    logAPI = window.__TAURI__.log;
                    // 附加控制台输出
                    await logAPI.attachConsole();
                    logInitialized = true;
                    log.info('[FRONTEND] 🔧 Tauri log plugin initialized successfully');
                } else {
                    console.warn('[FRONTEND] ⚠️ Tauri log plugin not available, falling back to console');
                    logInitialized = true; // 标记为已初始化，使用控制台fallback
                }
            } catch (e) {
                console.warn('[FRONTEND] ⚠️ Failed to initialize Tauri log plugin:', e);
                logInitialized = true; // 标记为已初始化，使用控制台fallback
            }
        }

        // 安全的JSON序列化函数
        function safeStringify(data) {
            try {
                if (data === null || data === undefined) return 'null';
                if (typeof data === 'string') return data;
                if (typeof data === 'number' || typeof data === 'boolean') return String(data);

                // 对于对象，使用安全的序列化
                return JSON.stringify(data, (key, value) => {
                    // 避免循环引用
                    if (typeof value === 'object' && value !== null) {
                        if (value.constructor && value.constructor.name === 'HTMLElement') {
                            return `[HTMLElement: ${value.tagName}]`;
                        }
                        if (value.constructor && value.constructor.name === 'Event') {
                            return `[Event: ${value.type}]`;
                        }
                    }
                    return value;
                });
            } catch (error) {
                return `[Serialization Error: ${error.message}]`;
            }
        }

        // 统一日志接口
        const log = {
            trace: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.trace(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-TRACE] ${fullMessage}`);
                }
            },
            debug: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.debug(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-DEBUG] ${fullMessage}`);
                }
            },
            info: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.info(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.log(`[FRONTEND-INFO] ${fullMessage}`);
                }
            },
            warn: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.warn(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.warn(`[FRONTEND-WARN] ${fullMessage}`);
                }
            },
            error: (message, data = null) => {
                const fullMessage = data ? `${message} | Data: ${safeStringify(data)}` : message;
                if (logInitialized && logAPI) {
                    logAPI.error(`[FRONTEND] ${fullMessage}`).catch(console.warn);
                } else {
                    console.error(`[FRONTEND-ERROR] ${fullMessage}`);
                }
            }
        };

        // 🚀 CRITICAL FIX: 在log对象定义后立即设置console重定向
        setTimeout(() => {
            setupConsoleRedirection();
        }, 100);

        // 兼容性函数（逐步迁移）
        function logToBackend(level, message, data = null) {
            switch(level.toUpperCase()) {
                case 'TRACE': log.trace(message, data); break;
                case 'DEBUG': log.debug(message, data); break;
                case 'INFO': log.info(message, data); break;
                case 'WARN': log.warn(message, data); break;
                case 'ERROR': log.error(message, data); break;
                default: log.info(message, data); break;
            }
        }

        async function initializeOverlay() {
            log.info('🚀 Initializing overlay after DOM ready');

            try {
                // 🔧 CRITICAL FIX: 等待Tauri API加载
                if (typeof window.__TAURI__ === 'undefined') {
                    log.info('🚀 Waiting for Tauri API...');
                    setTimeout(initializeOverlay, 100);
                    return;
                }

                // 🔧 LOG REFACTOR: 初始化日志系统
                await initializeLogging();

                log.info('🚀 Tauri API loaded, setting up event listeners');
                setupEventListeners();

                // 🆕 初始化共享工具栏组件
                await initializeSharedToolbarComponents();

            } catch (e) {
                log.error('Initialization failed', e.toString());
            }
        }

        // 🆕 初始化共享工具栏组件
        async function initializeSharedToolbarComponents() {
            log.info('🛠️ Initializing shared toolbar components for overlay');

            try {
                // 检查共享组件是否已加载
                if (typeof window.SharedToolbarComponent === 'undefined' ||
                    typeof window.SharedToolbar === 'undefined') {
                    log.warn('🛠️ Shared toolbar components not loaded, skipping initialization');
                    return;
                }

                // 初始化Canvas
                initializeAnnotationCanvas();

                // 设置Canvas绘图事件
                setupCanvasDrawingEvents();

                // 初始化性能优化系统
                if (typeof CanvasPerformanceManager !== 'undefined') {
                    CanvasPerformanceManager.initialize();
                }

                // 初始化撤销/重做系统
                if (typeof UndoRedoManager !== 'undefined') {
                    UndoRedoManager.initialize();
                }

                // 预创建工具栏但不显示
                createOverlayToolbar();

                // 设置窗口大小变化监听器
                setupWindowResizeListener();

                log.info('🛠️ Shared toolbar components initialized successfully');

            } catch (error) {
                log.error('🛠️ Failed to initialize shared toolbar components:', error);
            }
        }

        // 🆕 增强的标注Canvas初始化
        function initializeAnnotationCanvas() {
            const canvas = document.getElementById('annotationCanvas');
            if (!canvas) {
                log.error('🛠️ Annotation canvas not found');
                return;
            }

            // 设置Canvas尺寸为全屏，考虑设备像素比
            const devicePixelRatio = window.devicePixelRatio || 1;
            const displayWidth = window.innerWidth;
            const displayHeight = window.innerHeight;

            // 设置Canvas的实际尺寸（考虑高DPI显示器）
            canvas.width = displayWidth * devicePixelRatio;
            canvas.height = displayHeight * devicePixelRatio;

            // 设置Canvas的显示尺寸
            canvas.style.width = displayWidth + 'px';
            canvas.style.height = displayHeight + 'px';

            // 获取绘图上下文
            const ctx = canvas.getContext('2d');
            if (ctx) {
                // 缩放上下文以匹配设备像素比
                ctx.scale(devicePixelRatio, devicePixelRatio);

                // 设置默认绘图样式
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';
                ctx.strokeStyle = '#ff6b35';
                ctx.lineWidth = 3;
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.textBaseline = 'top';

                // 启用抗锯齿
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                // 存储上下文和配置供后续使用
                window.annotationContext = ctx;
                window.canvasDevicePixelRatio = devicePixelRatio;

                log.info('🛠️ Enhanced annotation canvas initialized:', {
                    displayWidth,
                    displayHeight,
                    canvasWidth: canvas.width,
                    canvasHeight: canvas.height,
                    devicePixelRatio
                });
            }
        }

        // 🆕 Canvas坐标转换工具
        const CanvasCoordinateManager = {
            // 将屏幕坐标转换为Canvas坐标
            screenToCanvas(screenX, screenY) {
                const canvas = document.getElementById('annotationCanvas');
                if (!canvas) return { x: screenX, y: screenY };

                const rect = canvas.getBoundingClientRect();
                const scaleX = canvas.width / rect.width;
                const scaleY = canvas.height / rect.height;

                return {
                    x: (screenX - rect.left) * scaleX,
                    y: (screenY - rect.top) * scaleY
                };
            },

            // 将Canvas坐标转换为屏幕坐标
            canvasToScreen(canvasX, canvasY) {
                const canvas = document.getElementById('annotationCanvas');
                if (!canvas) return { x: canvasX, y: canvasY };

                const rect = canvas.getBoundingClientRect();
                const scaleX = rect.width / canvas.width;
                const scaleY = rect.height / canvas.height;

                return {
                    x: canvasX * scaleX + rect.left,
                    y: canvasY * scaleY + rect.top
                };
            },

            // 获取相对于区域的坐标
            getRegionRelativeCoordinates(screenX, screenY) {
                const region = appStateManager.selectedRegion;
                if (!region) return { x: screenX, y: screenY };

                return {
                    x: screenX - region.x,
                    y: screenY - region.y
                };
            },

            // 检查坐标是否在当前区域内
            isInCurrentRegion(screenX, screenY) {
                const region = appStateManager.selectedRegion;
                if (!region) return false;

                return screenX >= region.x &&
                       screenX <= region.x + region.width &&
                       screenY >= region.y &&
                       screenY <= region.y + region.height;
            }
        };

        // 🆕 创建Overlay工具栏
        function createOverlayToolbar() {
            const container = document.getElementById('overlaySharedToolbar');
            if (!container) {
                log.error('🛠️ Overlay toolbar container not found');
                return;
            }

            try {
                // 使用共享组件创建工具栏
                const toolbar = window.SharedToolbarComponent.create({
                    containerId: 'overlayToolbarContent',
                    layout: 'vertical',
                    isOverlayMode: true
                });

                // 清空容器并添加工具栏
                container.innerHTML = '';
                container.appendChild(toolbar);

                // 初始化工具栏功能
                window.SharedToolbar.initialize({
                    isOverlayMode: true,
                    containerId: 'overlayToolbarContent',
                    layout: 'vertical'
                });

                log.info('🛠️ Overlay toolbar created successfully');

            } catch (error) {
                log.error('🛠️ Failed to create overlay toolbar:', error);
            }
        }

        // 🆕 显示共享工具栏
        function showSharedToolbar() {
            const container = document.getElementById('overlaySharedToolbar');
            if (container) {
                container.style.display = 'block';
                container.classList.add('active');

                // 如果有选中的区域，定位工具栏
                if (appStateManager.selectedRegion) {
                    positionSharedToolbar(appStateManager.selectedRegion);
                }

                log.info('🛠️ Shared toolbar shown');
            }
        }

        // 🆕 隐藏共享工具栏
        function hideSharedToolbar() {
            const container = document.getElementById('overlaySharedToolbar');
            if (container) {
                container.style.display = 'none';
                container.classList.remove('active');
                log.info('🛠️ Shared toolbar hidden');
            }
        }

        // 🆕 智能定位共享工具栏
        function positionSharedToolbar(region, options = {}) {
            const container = document.getElementById('overlaySharedToolbar');
            if (!container || !region) return;

            // 获取工具栏实际尺寸
            const toolbarRect = container.getBoundingClientRect();
            const toolbarWidth = toolbarRect.width || 60; // 垂直布局默认宽度
            const toolbarHeight = toolbarRect.height || 300; // 估计高度

            const {
                spacing = 20,
                preferredPosition = 'right', // 'right', 'left', 'top', 'bottom', 'auto'
                avoidOverlap = true,
                minDistanceFromEdge = 10
            } = options;

            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;

            // 计算所有可能的位置
            const positions = calculateToolbarPositions(region, toolbarWidth, toolbarHeight, spacing);

            // 根据偏好和可用空间选择最佳位置
            const bestPosition = selectBestPosition(positions, preferredPosition, screenWidth, screenHeight, minDistanceFromEdge);

            // 应用位置
            container.style.left = `${bestPosition.x}px`;
            container.style.top = `${bestPosition.y}px`;
            container.classList.add('overlay-toolbar-positioned');

            // 添加位置指示类
            container.setAttribute('data-position', bestPosition.position);

            log.info('🛠️ Shared toolbar positioned:', {
                position: bestPosition.position,
                x: bestPosition.x,
                y: bestPosition.y,
                region: region,
                toolbarSize: { width: toolbarWidth, height: toolbarHeight }
            });

            return bestPosition;
        }

        // 🆕 计算工具栏所有可能的位置
        function calculateToolbarPositions(region, toolbarWidth, toolbarHeight, spacing) {
            return {
                right: {
                    x: region.x + region.width + spacing,
                    y: region.y + (region.height - toolbarHeight) / 2,
                    position: 'right'
                },
                left: {
                    x: region.x - toolbarWidth - spacing,
                    y: region.y + (region.height - toolbarHeight) / 2,
                    position: 'left'
                },
                top: {
                    x: region.x + (region.width - toolbarWidth) / 2,
                    y: region.y - toolbarHeight - spacing,
                    position: 'top'
                },
                bottom: {
                    x: region.x + (region.width - toolbarWidth) / 2,
                    y: region.y + region.height + spacing,
                    position: 'bottom'
                },
                topRight: {
                    x: region.x + region.width + spacing,
                    y: region.y - toolbarHeight - spacing,
                    position: 'top-right'
                },
                topLeft: {
                    x: region.x - toolbarWidth - spacing,
                    y: region.y - toolbarHeight - spacing,
                    position: 'top-left'
                },
                bottomRight: {
                    x: region.x + region.width + spacing,
                    y: region.y + region.height + spacing,
                    position: 'bottom-right'
                },
                bottomLeft: {
                    x: region.x - toolbarWidth - spacing,
                    y: region.y + region.height + spacing,
                    position: 'bottom-left'
                }
            };
        }

        // 🆕 选择最佳工具栏位置
        function selectBestPosition(positions, preferredPosition, screenWidth, screenHeight, minDistanceFromEdge) {
            // 评估每个位置的可行性
            const evaluatedPositions = Object.values(positions).map(pos => {
                const score = evaluatePosition(pos, screenWidth, screenHeight, minDistanceFromEdge);
                return { ...pos, score };
            }).filter(pos => pos.score > 0); // 只保留可行的位置

            if (evaluatedPositions.length === 0) {
                // 如果没有完全可行的位置，选择最不坏的位置并调整
                const fallbackPosition = positions[preferredPosition] || positions.right;
                return adjustPositionToFitScreen(fallbackPosition, screenWidth, screenHeight, minDistanceFromEdge);
            }

            // 优先选择偏好位置（如果可行）
            const preferredPos = evaluatedPositions.find(pos => pos.position === preferredPosition);
            if (preferredPos && preferredPos.score >= 0.8) {
                return preferredPos;
            }

            // 否则选择得分最高的位置
            evaluatedPositions.sort((a, b) => b.score - a.score);
            return evaluatedPositions[0];
        }

        // 🆕 评估位置的可行性（返回0-1的分数）
        function evaluatePosition(position, screenWidth, screenHeight, minDistanceFromEdge) {
            const { x, y } = position;
            const toolbarWidth = 60;
            const toolbarHeight = 300;

            // 检查是否完全在屏幕内
            const leftEdge = x >= minDistanceFromEdge;
            const rightEdge = x + toolbarWidth <= screenWidth - minDistanceFromEdge;
            const topEdge = y >= minDistanceFromEdge;
            const bottomEdge = y + toolbarHeight <= screenHeight - minDistanceFromEdge;

            if (!leftEdge || !rightEdge || !topEdge || !bottomEdge) {
                return 0; // 不可行
            }

            // 计算距离屏幕边缘的最小距离
            const distanceFromEdges = Math.min(
                x, // 距离左边缘
                screenWidth - (x + toolbarWidth), // 距离右边缘
                y, // 距离上边缘
                screenHeight - (y + toolbarHeight) // 距离下边缘
            );

            // 根据距离边缘的远近给分（距离越远分数越高）
            const edgeScore = Math.min(distanceFromEdges / 100, 1);

            // 根据位置类型给予偏好分数
            const positionPreference = {
                'right': 1.0,
                'left': 0.9,
                'bottom': 0.8,
                'top': 0.7,
                'bottom-right': 0.6,
                'top-right': 0.6,
                'bottom-left': 0.5,
                'top-left': 0.5
            };

            const preferenceScore = positionPreference[position.position] || 0.5;

            return edgeScore * 0.7 + preferenceScore * 0.3;
        }

        // 🆕 调整位置以适应屏幕
        function adjustPositionToFitScreen(position, screenWidth, screenHeight, minDistanceFromEdge) {
            const toolbarWidth = 60;
            const toolbarHeight = 300;

            let { x, y } = position;

            // 调整X坐标
            if (x < minDistanceFromEdge) {
                x = minDistanceFromEdge;
            } else if (x + toolbarWidth > screenWidth - minDistanceFromEdge) {
                x = screenWidth - toolbarWidth - minDistanceFromEdge;
            }

            // 调整Y坐标
            if (y < minDistanceFromEdge) {
                y = minDistanceFromEdge;
            } else if (y + toolbarHeight > screenHeight - minDistanceFromEdge) {
                y = screenHeight - toolbarHeight - minDistanceFromEdge;
            }

            return {
                ...position,
                x,
                y,
                position: position.position + '-adjusted'
            };
        }

        // 🆕 工具栏跟随区域移动
        function updateToolbarPosition() {
            if (appStateManager.currentState === AppState.REGION_ANNOTATION && appStateManager.selectedRegion) {
                positionSharedToolbar(appStateManager.selectedRegion);
            }
        }

        // 🆕 智能工具栏定位选项
        function getToolbarPositionOptions(region) {
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;

            // 根据区域位置和大小智能选择偏好位置
            let preferredPosition = 'right'; // 默认偏好

            // 如果区域在屏幕右侧，偏好左侧
            if (region.x + region.width > screenWidth * 0.7) {
                preferredPosition = 'left';
            }
            // 如果区域很宽，偏好上方或下方
            else if (region.width > screenWidth * 0.6) {
                preferredPosition = region.y > screenHeight * 0.5 ? 'top' : 'bottom';
            }
            // 如果区域很高，偏好左右两侧
            else if (region.height > screenHeight * 0.7) {
                preferredPosition = region.x > screenWidth * 0.5 ? 'left' : 'right';
            }

            return {
                preferredPosition,
                spacing: Math.max(20, Math.min(region.width, region.height) * 0.1), // 动态间距
                avoidOverlap: true,
                minDistanceFromEdge: 15
            };
        }

        // 🆕 响应式工具栏定位
        function positionToolbarResponsively(region) {
            const options = getToolbarPositionOptions(region);
            return positionSharedToolbar(region, options);
        }

        // 🆕 工具栏位置动画
        function animateToolbarPosition(targetX, targetY, duration = 300) {
            const container = document.getElementById('overlaySharedToolbar');
            if (!container) return;

            const startX = parseFloat(container.style.left) || 0;
            const startY = parseFloat(container.style.top) || 0;
            const startTime = performance.now();

            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 使用easeOutCubic缓动函数
                const easeProgress = 1 - Math.pow(1 - progress, 3);

                const currentX = startX + (targetX - startX) * easeProgress;
                const currentY = startY + (targetY - startY) * easeProgress;

                container.style.left = `${currentX}px`;
                container.style.top = `${currentY}px`;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }

            requestAnimationFrame(animate);
        }

        // 🆕 事件处理器管理系统
        const EventHandlerManager = {
            // 当前活跃的事件处理器
            activeHandlers: new Set(),

            // 事件处理器映射
            handlers: {
                // 选择模式事件处理器
                selection: {
                    mousemove: handleMouseMove,
                    mousedown: handleMouseDown,
                    mouseup: handleMouseUp,
                    click: handleClick
                },

                // 标注模式事件处理器
                annotation: {
                    mousemove: handleAnnotationMouseMove,
                    mousedown: handleAnnotationMouseDown,
                    mouseup: handleAnnotationMouseUp,
                    click: handleAnnotationClick
                }
            },

            // 切换到选择模式事件处理器
            enableSelectionMode() {
                this.disableAllHandlers();
                this.enableHandlers('selection');
                log.info('🎯 Selection mode event handlers enabled');
            },

            // 切换到标注模式事件处理器
            enableAnnotationMode() {
                this.disableAllHandlers();
                this.enableHandlers('annotation');
                log.info('🎨 Annotation mode event handlers enabled');
            },

            // 启用指定类型的事件处理器
            enableHandlers(type) {
                const eventCaptureLayer = document.getElementById('eventCaptureLayer');
                if (!eventCaptureLayer) return;

                const handlers = this.handlers[type];
                if (!handlers) return;

                Object.entries(handlers).forEach(([eventType, handler]) => {
                    eventCaptureLayer.addEventListener(eventType, handler, { passive: false });
                    this.activeHandlers.add(`${type}-${eventType}`);
                });

                log.debug(`🎯 Enabled ${type} event handlers:`, Object.keys(handlers));
            },

            // 禁用所有事件处理器
            disableAllHandlers() {
                const eventCaptureLayer = document.getElementById('eventCaptureLayer');
                if (!eventCaptureLayer) return;

                // 移除所有已注册的事件处理器
                Object.values(this.handlers).forEach(handlerGroup => {
                    Object.entries(handlerGroup).forEach(([eventType, handler]) => {
                        eventCaptureLayer.removeEventListener(eventType, handler);
                    });
                });

                this.activeHandlers.clear();
                log.debug('🎯 All event handlers disabled');
            },

            // 获取当前活跃的处理器
            getActiveHandlers() {
                return Array.from(this.activeHandlers);
            }
        };

        // 🆕 标注模式鼠标事件处理器
        function handleAnnotationMouseMove(event) {
            // 在标注模式下，鼠标移动用于绘图预览或工具提示
            const x = event.clientX;
            const y = event.clientY;

            // 检查是否在Canvas区域内
            const canvas = document.getElementById('annotationCanvas');
            if (canvas && canvas.style.display !== 'none') {
                const rect = canvas.getBoundingClientRect();
                const isInCanvas = x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;

                if (isInCanvas) {
                    // 更新Canvas光标样式
                    canvas.style.cursor = getCurrentToolCursor();

                    // 如果正在绘制，更新绘制状态
                    if (window.isDrawing && window.annotationContext) {
                        updateDrawing(x - rect.left, y - rect.top);
                    }
                }
            }

            // 节流日志
            const now = Date.now();
            if (now - (window.lastAnnotationMoveLog || 0) > 1000) {
                log.trace('🎨 Annotation mouse move:', { x, y });
                window.lastAnnotationMoveLog = now;
            }
        }

        function handleAnnotationMouseDown(event) {
            // 在标注模式下，鼠标按下开始绘制
            const x = event.clientX;
            const y = event.clientY;

            // 检查是否在Canvas区域内
            const canvas = document.getElementById('annotationCanvas');
            if (canvas && canvas.style.display !== 'none') {
                const rect = canvas.getBoundingClientRect();
                const isInCanvas = x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;

                if (isInCanvas) {
                    event.preventDefault();
                    event.stopPropagation();

                    // 开始绘制
                    startDrawing(x - rect.left, y - rect.top);
                    log.info('🎨 Started drawing at:', { x: x - rect.left, y: y - rect.top });
                    return;
                }
            }

            // 如果不在Canvas区域，忽略事件
            log.debug('🎨 Mouse down outside canvas area, ignoring');
        }

        function handleAnnotationMouseUp(event) {
            // 在标注模式下，鼠标释放结束绘制
            if (window.isDrawing) {
                endDrawing();
                log.info('🎨 Finished drawing');
            }
        }

        function handleAnnotationClick(event) {
            // 在标注模式下，点击用于放置文本或形状
            const x = event.clientX;
            const y = event.clientY;

            // 检查是否在Canvas区域内
            const canvas = document.getElementById('annotationCanvas');
            if (canvas && canvas.style.display !== 'none') {
                const rect = canvas.getBoundingClientRect();
                const isInCanvas = x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;

                if (isInCanvas) {
                    const currentTool = window.SharedToolbar?.state?.getCurrentTool();

                    if (currentTool === 'text') {
                        // 文字工具：显示文本输入
                        showTextInput(x - rect.left, y - rect.top);
                    } else if (['rectangle', 'circle'].includes(currentTool)) {
                        // 形状工具：开始形状绘制
                        startShapeDrawing(currentTool, x - rect.left, y - rect.top);
                    }

                    log.info('🎨 Annotation click:', { tool: currentTool, x: x - rect.left, y: y - rect.top });
                }
            }
        }

        // 🆕 平滑工具栏重定位
        function repositionToolbarSmoothly(region, options = {}) {
            const container = document.getElementById('overlaySharedToolbar');
            if (!container || !region) return;

            const currentX = parseFloat(container.style.left) || 0;
            const currentY = parseFloat(container.style.top) || 0;

            // 计算新位置
            const newPosition = positionSharedToolbar(region, { ...options, animate: false });

            // 如果位置变化足够大，使用动画
            const distance = Math.sqrt(
                Math.pow(newPosition.x - currentX, 2) +
                Math.pow(newPosition.y - currentY, 2)
            );

            if (distance > 10) {
                animateToolbarPosition(newPosition.x, newPosition.y);
                log.info('🛠️ Toolbar repositioned with animation:', {
                    from: { x: currentX, y: currentY },
                    to: { x: newPosition.x, y: newPosition.y },
                    distance
                });
            }

            return newPosition;
        }

        // 🆕 绘图辅助函数
        function getCurrentToolCursor() {
            const currentTool = window.SharedToolbar?.state?.getCurrentTool();

            switch (currentTool) {
                case 'text': return 'text';
                case 'arrow': return 'crosshair';
                case 'rectangle': return 'crosshair';
                case 'circle': return 'crosshair';
                case 'brush': return 'crosshair';
                default: return 'default';
            }
        }

        function startDrawing(x, y) {
            // 使用新的绘图工具管理器
            if (typeof DrawingToolManager !== 'undefined') {
                DrawingToolManager.startDrawing(x, y);
            } else {
                // 降级到旧的实现
                window.isDrawing = true;
                window.drawingStartX = x;
                window.drawingStartY = y;

                const ctx = window.annotationContext;
                if (ctx) {
                    ctx.beginPath();
                    ctx.moveTo(x, y);
                }
            }
        }

        function updateDrawing(x, y) {
            // 使用新的绘图工具管理器
            if (typeof DrawingToolManager !== 'undefined') {
                DrawingToolManager.updateDrawing(x, y);
            } else {
                // 降级到旧的实现
                if (!window.isDrawing || !window.annotationContext) return;

                const currentTool = window.SharedToolbar?.state?.getCurrentTool();
                const ctx = window.annotationContext;

                if (currentTool === 'brush') {
                    ctx.lineTo(x, y);
                    ctx.stroke();
                    ctx.beginPath();
                    ctx.moveTo(x, y);
                }
            }
        }

        function endDrawing(x, y) {
            // 使用新的绘图工具管理器
            if (typeof DrawingToolManager !== 'undefined') {
                DrawingToolManager.endDrawing(x || DrawingToolManager.lastX, y || DrawingToolManager.lastY);
            } else {
                // 降级到旧的实现
                if (!window.isDrawing) return;

                window.isDrawing = false;

                const ctx = window.annotationContext;
                if (ctx) {
                    ctx.closePath();
                }
            }
        }

        function showTextInput(x, y) {
            // 创建文本输入框
            const input = document.createElement('input');
            input.type = 'text';
            input.style.position = 'absolute';
            input.style.left = `${x}px`;
            input.style.top = `${y}px`;
            input.style.zIndex = '6300';
            input.style.background = 'white';
            input.style.border = '2px solid #673AB7';
            input.style.borderRadius = '4px';
            input.style.padding = '4px 8px';
            input.style.fontSize = '14px';
            input.placeholder = 'Enter text...';

            // 添加到Canvas容器
            const canvas = document.getElementById('annotationCanvas');
            if (canvas && canvas.parentElement) {
                canvas.parentElement.appendChild(input);
                input.focus();

                // 处理文本输入完成
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        const text = input.value.trim();
                        if (text) {
                            drawText(text, x, y);
                        }
                        input.remove();
                    } else if (e.key === 'Escape') {
                        input.remove();
                    }
                });

                input.addEventListener('blur', () => {
                    const text = input.value.trim();
                    if (text) {
                        drawText(text, x, y);
                    }
                    input.remove();
                });
            }
        }

        function drawText(text, x, y) {
            // 使用新的绘图工具管理器
            if (typeof DrawingToolManager !== 'undefined') {
                const ctx = window.annotationContext;
                if (ctx) {
                    DrawingToolManager.drawText(ctx, text, x, y);
                }
            } else {
                // 降级到旧的实现
                const ctx = window.annotationContext;
                if (ctx) {
                    ctx.font = '16px Arial';
                    ctx.fillStyle = '#673AB7';
                    ctx.fillText(text, x, y);
                    log.info('🎨 Text drawn:', { text, x, y });
                }
            }
        }

        function startShapeDrawing(shape, x, y) {
            // 形状绘制现在已实现
            window.shapeDrawingMode = {
                active: true,
                shape: shape,
                startX: x,
                startY: y,
                currentX: x,
                currentY: y
            };

            log.info('🎨 Shape drawing started:', { shape, x, y });
        }

        // 🆕 完整的绘图工具系统
        const DrawingToolManager = {
            // 当前工具状态
            currentTool: 'brush',
            isDrawing: false,
            lastX: 0,
            lastY: 0,

            // 工具配置
            toolConfigs: {
                brush: {
                    strokeStyle: '#ff6b35',
                    lineWidth: 3,
                    lineCap: 'round',
                    lineJoin: 'round'
                },
                text: {
                    font: '16px Arial',
                    fillStyle: '#673AB7',
                    strokeStyle: '#673AB7',
                    textAlign: 'left',
                    textBaseline: 'top'
                },
                arrow: {
                    strokeStyle: '#e91e63',
                    lineWidth: 3,
                    lineCap: 'round',
                    arrowHeadSize: 10
                },
                rectangle: {
                    strokeStyle: '#2196f3',
                    lineWidth: 2,
                    fillStyle: 'rgba(33, 150, 243, 0.1)'
                },
                circle: {
                    strokeStyle: '#4caf50',
                    lineWidth: 2,
                    fillStyle: 'rgba(76, 175, 80, 0.1)'
                }
            },

            // 设置当前工具
            setTool(toolName) {
                if (this.toolConfigs[toolName]) {
                    this.currentTool = toolName;
                    this.applyToolConfig(toolName);
                    log.info('🎨 Tool changed to:', toolName);
                }
            },

            // 应用工具配置
            applyToolConfig(toolName) {
                const ctx = window.annotationContext;
                if (!ctx) return;

                const config = this.toolConfigs[toolName];
                Object.keys(config).forEach(key => {
                    if (key in ctx) {
                        ctx[key] = config[key];
                    }
                });
            },

            // 开始绘制
            startDrawing(x, y) {
                this.isDrawing = true;
                this.lastX = x;
                this.lastY = y;

                const ctx = window.annotationContext;
                if (!ctx) return;

                switch (this.currentTool) {
                    case 'brush':
                        ctx.beginPath();
                        ctx.moveTo(x, y);
                        break;

                    case 'arrow':
                    case 'rectangle':
                    case 'circle':
                        // 这些工具在鼠标释放时绘制
                        window.shapeStartX = x;
                        window.shapeStartY = y;
                        break;
                }

                log.debug('🎨 Drawing started:', { tool: this.currentTool, x, y });
            },

            // 更新绘制（性能优化版本）
            updateDrawing(x, y) {
                if (!this.isDrawing) return;

                const ctx = window.annotationContext;
                if (!ctx) return;

                // 使用性能管理器的节流功能
                if (typeof CanvasPerformanceManager !== 'undefined') {
                    CanvasPerformanceManager.throttle('updateDrawing', () => {
                        this.performDrawUpdate(ctx, x, y);
                    });
                } else {
                    this.performDrawUpdate(ctx, x, y);
                }
            },

            // 执行绘制更新
            performDrawUpdate(ctx, x, y) {
                switch (this.currentTool) {
                    case 'brush':
                        this.drawBrushStroke(ctx, this.lastX, this.lastY, x, y);
                        this.lastX = x;
                        this.lastY = y;
                        break;

                    case 'arrow':
                    case 'rectangle':
                    case 'circle':
                        // 实时预览（可选实现）
                        this.previewShape(ctx, window.shapeStartX, window.shapeStartY, x, y);
                        break;
                }
            },

            // 结束绘制
            endDrawing(x, y) {
                if (!this.isDrawing) return;

                this.isDrawing = false;

                const ctx = window.annotationContext;
                if (!ctx) return;

                switch (this.currentTool) {
                    case 'brush':
                        ctx.closePath();
                        break;

                    case 'arrow':
                        this.drawArrow(ctx, window.shapeStartX, window.shapeStartY, x, y);
                        break;

                    case 'rectangle':
                        this.drawRectangle(ctx, window.shapeStartX, window.shapeStartY, x, y);
                        break;

                    case 'circle':
                        this.drawCircle(ctx, window.shapeStartX, window.shapeStartY, x, y);
                        break;
                }

                // 保存状态到撤销/重做历史
                if (typeof UndoRedoManager !== 'undefined') {
                    UndoRedoManager.saveState();
                }

                log.debug('🎨 Drawing ended:', { tool: this.currentTool, x, y });
            },

            // 绘制画笔笔触
            drawBrushStroke(ctx, x1, y1, x2, y2) {
                ctx.lineTo(x2, y2);
                ctx.stroke();
                ctx.beginPath();
                ctx.moveTo(x2, y2);
            },

            // 绘制箭头
            drawArrow(ctx, startX, startY, endX, endY) {
                const config = this.toolConfigs.arrow;

                // 保存状态
                ctx.save();

                // 应用样式
                ctx.strokeStyle = config.strokeStyle;
                ctx.lineWidth = config.lineWidth;
                ctx.lineCap = config.lineCap;

                // 绘制箭头主线
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();

                // 计算箭头头部
                const angle = Math.atan2(endY - startY, endX - startX);
                const headSize = config.arrowHeadSize;

                // 绘制箭头头部
                ctx.beginPath();
                ctx.moveTo(endX, endY);
                ctx.lineTo(
                    endX - headSize * Math.cos(angle - Math.PI / 6),
                    endY - headSize * Math.sin(angle - Math.PI / 6)
                );
                ctx.moveTo(endX, endY);
                ctx.lineTo(
                    endX - headSize * Math.cos(angle + Math.PI / 6),
                    endY - headSize * Math.sin(angle + Math.PI / 6)
                );
                ctx.stroke();

                // 恢复状态
                ctx.restore();
            },

            // 绘制矩形
            drawRectangle(ctx, startX, startY, endX, endY) {
                const config = this.toolConfigs.rectangle;

                // 保存状态
                ctx.save();

                // 计算矩形参数
                const x = Math.min(startX, endX);
                const y = Math.min(startY, endY);
                const width = Math.abs(endX - startX);
                const height = Math.abs(endY - startY);

                // 填充
                if (config.fillStyle) {
                    ctx.fillStyle = config.fillStyle;
                    ctx.fillRect(x, y, width, height);
                }

                // 描边
                ctx.strokeStyle = config.strokeStyle;
                ctx.lineWidth = config.lineWidth;
                ctx.strokeRect(x, y, width, height);

                // 恢复状态
                ctx.restore();
            },

            // 绘制圆形
            drawCircle(ctx, startX, startY, endX, endY) {
                const config = this.toolConfigs.circle;

                // 保存状态
                ctx.save();

                // 计算圆形参数
                const centerX = (startX + endX) / 2;
                const centerY = (startY + endY) / 2;
                const radius = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2)) / 2;

                // 绘制圆形
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);

                // 填充
                if (config.fillStyle) {
                    ctx.fillStyle = config.fillStyle;
                    ctx.fill();
                }

                // 描边
                ctx.strokeStyle = config.strokeStyle;
                ctx.lineWidth = config.lineWidth;
                ctx.stroke();

                // 恢复状态
                ctx.restore();
            },

            // 预览形状（实时绘制）
            previewShape(ctx, startX, startY, currentX, currentY) {
                // 这里可以实现实时预览功能
                // 暂时跳过以避免性能问题
            },

            // 绘制文本
            drawText(ctx, text, x, y) {
                const config = this.toolConfigs.text;

                // 保存状态
                ctx.save();

                // 应用文本样式
                ctx.font = config.font;
                ctx.fillStyle = config.fillStyle;
                ctx.textAlign = config.textAlign;
                ctx.textBaseline = config.textBaseline;

                // 绘制文本
                ctx.fillText(text, x, y);

                // 恢复状态
                ctx.restore();

                // 保存状态到撤销/重做历史
                if (typeof UndoRedoManager !== 'undefined') {
                    UndoRedoManager.saveState();
                }

                log.info('🎨 Text drawn:', { text, x, y });
            }
        };

        // 🆕 工具选择集成
        function onToolSelected(toolName) {
            if (typeof DrawingToolManager !== 'undefined') {
                DrawingToolManager.setTool(toolName);

                // 更新光标样式
                const canvas = document.getElementById('annotationCanvas');
                if (canvas) {
                    canvas.style.cursor = getToolCursor(toolName);
                }

                log.info('🎨 Tool selected:', toolName);
            }
        }

        // 🆕 获取工具光标样式
        function getToolCursor(toolName) {
            const cursorMap = {
                brush: 'crosshair',
                text: 'text',
                arrow: 'crosshair',
                rectangle: 'crosshair',
                circle: 'crosshair'
            };

            return cursorMap[toolName] || 'default';
        }

        // 🆕 监听工具栏工具选择事件
        document.addEventListener('toolSelected', function(event) {
            const toolName = event.detail?.tool;
            if (toolName) {
                // 处理特殊工具
                switch (toolName) {
                    case 'undo':
                        if (typeof UndoRedoManager !== 'undefined') {
                            UndoRedoManager.undo();
                        }
                        break;

                    case 'redo':
                        if (typeof UndoRedoManager !== 'undefined') {
                            UndoRedoManager.redo();
                        }
                        break;

                    case 'clear':
                        clearCanvas();
                        break;

                    case 'save':
                        if (typeof SaveExportManager !== 'undefined') {
                            SaveExportManager.showSaveDialog();
                        }
                        break;

                    case 'copy':
                        if (typeof SaveExportManager !== 'undefined') {
                            SaveExportManager.exportToClipboard();
                        }
                        break;

                    case 'download':
                        if (typeof SaveExportManager !== 'undefined') {
                            SaveExportManager.saveAnnotatedImage();
                        }
                        break;

                    default:
                        // 普通绘图工具
                        onToolSelected(toolName);
                        break;
                }
            }
        });

        // 🆕 清除Canvas
        function clearCanvas() {
            const canvas = document.getElementById('annotationCanvas');
            const ctx = window.annotationContext;

            if (canvas && ctx) {
                // 清除Canvas内容
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 重新绘制区域边界
                const region = appStateManager.selectedRegion;
                if (region) {
                    drawRegionBoundary(ctx, region);
                }

                // 保存清除后的状态
                if (typeof UndoRedoManager !== 'undefined') {
                    UndoRedoManager.saveState();
                }

                log.info('🎨 Canvas cleared');
            }
        }

        // 🆕 性能监控测试函数
        window.getCanvasPerformanceReport = function() {
            if (typeof CanvasPerformanceManager !== 'undefined') {
                const report = CanvasPerformanceManager.getPerformanceReport();
                log.info('🚀 Canvas Performance Report:', report);
                return report;
            } else {
                log.warn('🚀 Performance manager not available');
                return null;
            }
        };

        // 🆕 保存/导出功能管理器
        const SaveExportManager = {
            // 保存标注后的图片
            async saveAnnotatedImage(customPath = null) {
                try {
                    const canvas = document.getElementById('annotationCanvas');
                    if (!canvas) {
                        throw new Error('Canvas not found');
                    }

                    // 获取Canvas数据
                    const canvasData = canvas.toDataURL('image/png');

                    // 调用后端保存函数
                    const savedPath = await window.__TAURI__.core.invoke('save_annotated_image', {
                        canvasData: canvasData,
                        originalImagePath: null,
                        savePath: customPath
                    });

                    log.info('💾 Image saved successfully:', savedPath);

                    // 显示成功提示
                    this.showSaveSuccessNotification(savedPath);

                    return savedPath;

                } catch (error) {
                    log.error('💾 Failed to save image:', error);
                    this.showSaveErrorNotification(error.message);
                    throw error;
                }
            },

            // 导出到剪贴板
            async exportToClipboard() {
                try {
                    const canvas = document.getElementById('annotationCanvas');
                    if (!canvas) {
                        throw new Error('Canvas not found');
                    }

                    // 获取Canvas数据
                    const canvasData = canvas.toDataURL('image/png');

                    // 调用后端导出函数
                    await window.__TAURI__.core.invoke('export_annotated_image_to_clipboard', {
                        canvasData: canvasData
                    });

                    log.info('📋 Image exported to clipboard successfully');

                    // 显示成功提示
                    this.showClipboardSuccessNotification();

                } catch (error) {
                    log.error('📋 Failed to export to clipboard:', error);
                    this.showClipboardErrorNotification(error.message);
                    throw error;
                }
            },

            // 显示保存对话框
            async showSaveDialog() {
                try {
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                    const defaultFilename = `annotated_screenshot_${timestamp}.png`;

                    const selectedPath = await window.__TAURI__.core.invoke('show_save_dialog', {
                        defaultFilename: defaultFilename
                    });

                    if (selectedPath) {
                        return await this.saveAnnotatedImage(selectedPath);
                    } else {
                        log.info('💾 Save dialog cancelled');
                        return null;
                    }

                } catch (error) {
                    log.error('💾 Failed to show save dialog:', error);
                    throw error;
                }
            },

            // 获取保存选项
            async getSaveOptions() {
                try {
                    const options = await window.__TAURI__.core.invoke('get_save_options');
                    log.info('📁 Save options retrieved:', options);
                    return options;

                } catch (error) {
                    log.error('📁 Failed to get save options:', error);
                    throw error;
                }
            },

            // 显示成功通知
            showSaveSuccessNotification(path) {
                // 创建临时通知元素
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #4caf50;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    z-index: 10000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    animation: slideIn 0.3s ease-out;
                `;
                notification.innerHTML = `
                    <div style="font-weight: bold;">✅ Image Saved</div>
                    <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">
                        ${path.split('/').pop()}
                    </div>
                `;

                document.body.appendChild(notification);

                // 3秒后自动移除
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);
            },

            // 显示剪贴板成功通知
            showClipboardSuccessNotification() {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #2196f3;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    z-index: 10000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                `;
                notification.innerHTML = `
                    <div style="font-weight: bold;">📋 Copied to Clipboard</div>
                    <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">
                        Image ready to paste
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 2000);
            },

            // 显示错误通知
            showSaveErrorNotification(message) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #f44336;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    z-index: 10000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                `;
                notification.innerHTML = `
                    <div style="font-weight: bold;">❌ Save Failed</div>
                    <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">
                        ${message}
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 4000);
            },

            // 显示剪贴板错误通知
            showClipboardErrorNotification(message) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #ff9800;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    z-index: 10000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                `;
                notification.innerHTML = `
                    <div style="font-weight: bold;">⚠️ Clipboard Error</div>
                    <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">
                        ${message}
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 4000);
            }
        };

        // 🆕 Canvas绘图事件集成
        function setupCanvasDrawingEvents() {
            const canvas = document.getElementById('annotationCanvas');
            if (!canvas) return;

            // 鼠标按下事件
            canvas.addEventListener('mousedown', function(e) {
                if (appStateManager.currentState !== AppState.REGION_ANNOTATION) return;

                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 检查是否在选中区域内
                if (CanvasCoordinateManager.isInCurrentRegion(e.clientX, e.clientY)) {
                    startDrawing(x, y);
                    e.preventDefault();
                    e.stopPropagation();
                }
            });

            // 鼠标移动事件
            canvas.addEventListener('mousemove', function(e) {
                if (appStateManager.currentState !== AppState.REGION_ANNOTATION) return;

                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                if (DrawingToolManager.isDrawing) {
                    updateDrawing(x, y);
                }
            });

            // 鼠标释放事件
            canvas.addEventListener('mouseup', function(e) {
                if (appStateManager.currentState !== AppState.REGION_ANNOTATION) return;

                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                if (DrawingToolManager.isDrawing) {
                    endDrawing(x, y);
                }
            });

            log.info('🎨 Canvas drawing events setup completed');
        }

        // 🆕 撤销/重做系统
        const UndoRedoManager = {
            // 历史记录栈
            history: [],
            currentIndex: -1,
            maxHistorySize: 50,

            // 保存当前Canvas状态（性能优化版本）
            saveState() {
                const canvas = document.getElementById('annotationCanvas');
                if (!canvas) return;

                try {
                    // 使用性能优化的状态保存
                    let imageData;
                    if (typeof CanvasPerformanceManager !== 'undefined') {
                        imageData = CanvasPerformanceManager.saveCanvasStateOptimized();
                    } else {
                        imageData = canvas.toDataURL();
                    }

                    if (!imageData) return;

                    // 如果当前不在历史记录的末尾，删除后面的记录
                    if (this.currentIndex < this.history.length - 1) {
                        this.history = this.history.slice(0, this.currentIndex + 1);
                    }

                    // 添加新状态
                    this.history.push({
                        imageData: imageData,
                        timestamp: Date.now(),
                        action: 'draw' // 可以扩展为更具体的操作类型
                    });

                    // 使用性能管理器的配置限制历史记录大小
                    const maxSize = CanvasPerformanceManager?.config?.maxHistorySize || this.maxHistorySize;
                    if (this.history.length > maxSize) {
                        this.history.shift();
                    } else {
                        this.currentIndex++;
                    }

                    log.debug('🔄 Canvas state saved:', {
                        historyLength: this.history.length,
                        currentIndex: this.currentIndex
                    });

                    // 更新UI按钮状态
                    this.updateUndoRedoButtons();

                } catch (error) {
                    log.error('🔄 Failed to save canvas state:', error);
                }
            },

            // 撤销操作
            undo() {
                if (!this.canUndo()) {
                    log.warn('🔄 Cannot undo: no previous state available');
                    return false;
                }

                this.currentIndex--;
                this.restoreState(this.history[this.currentIndex]);
                this.updateUndoRedoButtons();

                log.info('🔄 Undo performed:', {
                    currentIndex: this.currentIndex,
                    historyLength: this.history.length
                });

                return true;
            },

            // 重做操作
            redo() {
                if (!this.canRedo()) {
                    log.warn('🔄 Cannot redo: no next state available');
                    return false;
                }

                this.currentIndex++;
                this.restoreState(this.history[this.currentIndex]);
                this.updateUndoRedoButtons();

                log.info('🔄 Redo performed:', {
                    currentIndex: this.currentIndex,
                    historyLength: this.history.length
                });

                return true;
            },

            // 检查是否可以撤销
            canUndo() {
                return this.currentIndex > 0;
            },

            // 检查是否可以重做
            canRedo() {
                return this.currentIndex < this.history.length - 1;
            },

            // 恢复Canvas状态
            restoreState(state) {
                const canvas = document.getElementById('annotationCanvas');
                const ctx = window.annotationContext;

                if (!canvas || !ctx || !state) return;

                try {
                    // 创建临时图像对象
                    const img = new Image();
                    img.onload = () => {
                        // 清除Canvas
                        ctx.clearRect(0, 0, canvas.width, canvas.height);

                        // 绘制保存的图像
                        ctx.drawImage(img, 0, 0);

                        log.debug('🔄 Canvas state restored');
                    };

                    img.src = state.imageData;

                } catch (error) {
                    log.error('🔄 Failed to restore canvas state:', error);
                }
            },

            // 清除历史记录
            clearHistory() {
                this.history = [];
                this.currentIndex = -1;
                this.updateUndoRedoButtons();

                log.info('🔄 History cleared');
            },

            // 更新撤销/重做按钮状态
            updateUndoRedoButtons() {
                // 查找撤销按钮
                const undoButton = document.querySelector('[data-tool="undo"]');
                if (undoButton) {
                    undoButton.disabled = !this.canUndo();
                    undoButton.classList.toggle('disabled', !this.canUndo());
                }

                // 查找重做按钮
                const redoButton = document.querySelector('[data-tool="redo"]');
                if (redoButton) {
                    redoButton.disabled = !this.canRedo();
                    redoButton.classList.toggle('disabled', !this.canRedo());
                }
            },

            // 初始化撤销/重做系统
            initialize() {
                // 保存初始空白状态
                setTimeout(() => {
                    this.saveState();
                }, 100);

                // 设置键盘快捷键
                document.addEventListener('keydown', (e) => {
                    if (appStateManager.currentState !== AppState.REGION_ANNOTATION) return;

                    if (e.ctrlKey || e.metaKey) {
                        if (e.key === 'z' && !e.shiftKey) {
                            e.preventDefault();
                            this.undo();
                        } else if ((e.key === 'z' && e.shiftKey) || e.key === 'y') {
                            e.preventDefault();
                            this.redo();
                        } else if (e.key === 's') {
                            e.preventDefault();
                            // 保存快捷键
                            if (typeof SaveExportManager !== 'undefined') {
                                SaveExportManager.showSaveDialog();
                            }
                        } else if (e.key === 'c') {
                            e.preventDefault();
                            // 复制到剪贴板快捷键
                            if (typeof SaveExportManager !== 'undefined') {
                                SaveExportManager.exportToClipboard();
                            }
                        }
                    }
                });

                log.info('🔄 Undo/Redo system initialized');
            }
        };

        // 🆕 Canvas性能优化管理器
        const CanvasPerformanceManager = {
            // 性能配置
            config: {
                maxHistorySize: 20, // 减少历史记录大小以节省内存
                throttleDelay: 16, // 60fps的节流延迟
                batchSize: 10, // 批处理大小
                compressionQuality: 0.8 // 图像压缩质量
            },

            // 节流器
            throttleTimers: new Map(),

            // 性能监控
            performanceMetrics: {
                drawCalls: 0,
                lastFrameTime: 0,
                averageFrameTime: 0,
                memoryUsage: 0
            },

            // 节流函数
            throttle(key, func, delay) {
                if (this.throttleTimers.has(key)) {
                    clearTimeout(this.throttleTimers.get(key));
                }

                const timer = setTimeout(() => {
                    func();
                    this.throttleTimers.delete(key);
                }, delay || this.config.throttleDelay);

                this.throttleTimers.set(key, timer);
            },

            // 优化的Canvas状态保存
            saveCanvasStateOptimized() {
                const canvas = document.getElementById('annotationCanvas');
                if (!canvas) return null;

                try {
                    // 使用较低的质量来减少内存使用
                    const imageData = canvas.toDataURL('image/jpeg', this.config.compressionQuality);

                    // 监控内存使用
                    this.updateMemoryMetrics(imageData);

                    return imageData;

                } catch (error) {
                    log.error('🚀 Failed to save canvas state:', error);
                    return null;
                }
            },

            // 批量绘制优化
            batchDraw(drawOperations) {
                const ctx = window.annotationContext;
                if (!ctx || !drawOperations.length) return;

                // 开始批量绘制
                ctx.save();

                try {
                    // 执行所有绘制操作
                    drawOperations.forEach(operation => {
                        if (typeof operation === 'function') {
                            operation(ctx);
                        }
                    });

                    this.performanceMetrics.drawCalls += drawOperations.length;

                } finally {
                    // 恢复状态
                    ctx.restore();
                }
            },

            // 内存清理
            cleanupMemory() {
                // 清理节流器
                this.throttleTimers.forEach(timer => clearTimeout(timer));
                this.throttleTimers.clear();

                // 清理Canvas缓存（如果有的话）
                if (window.canvasCache) {
                    window.canvasCache.clear();
                }

                // 强制垃圾回收（如果可用）
                if (window.gc) {
                    window.gc();
                }

                log.info('🚀 Memory cleanup completed');
            },

            // 更新内存指标
            updateMemoryMetrics(imageData) {
                if (imageData) {
                    // 估算内存使用（Base64字符串大小）
                    this.performanceMetrics.memoryUsage = imageData.length * 0.75; // Base64编码开销
                }

                // 更新帧时间
                const now = performance.now();
                if (this.performanceMetrics.lastFrameTime > 0) {
                    const frameTime = now - this.performanceMetrics.lastFrameTime;
                    this.performanceMetrics.averageFrameTime =
                        (this.performanceMetrics.averageFrameTime * 0.9) + (frameTime * 0.1);
                }
                this.performanceMetrics.lastFrameTime = now;
            },

            // 获取性能报告
            getPerformanceReport() {
                return {
                    ...this.performanceMetrics,
                    fps: this.performanceMetrics.averageFrameTime > 0 ?
                         1000 / this.performanceMetrics.averageFrameTime : 0,
                    memoryUsageMB: this.performanceMetrics.memoryUsage / (1024 * 1024)
                };
            },

            // 优化Canvas设置
            optimizeCanvasSettings() {
                const canvas = document.getElementById('annotationCanvas');
                const ctx = window.annotationContext;

                if (!canvas || !ctx) return;

                // 启用硬件加速（如果可用）
                canvas.style.willChange = 'transform';

                // 优化绘图设置
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                // 设置合适的线条样式以提高性能
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';

                log.info('🚀 Canvas settings optimized');
            },

            // 初始化性能优化
            initialize() {
                // 优化Canvas设置
                this.optimizeCanvasSettings();

                // 设置性能监控
                this.startPerformanceMonitoring();

                // 设置内存清理定时器
                setInterval(() => {
                    this.cleanupMemory();
                }, 60000); // 每分钟清理一次

                log.info('🚀 Canvas performance optimization initialized');
            },

            // 开始性能监控
            startPerformanceMonitoring() {
                // 监控绘制性能
                const originalStroke = CanvasRenderingContext2D.prototype.stroke;
                const self = this;

                CanvasRenderingContext2D.prototype.stroke = function() {
                    const start = performance.now();
                    originalStroke.call(this);
                    const end = performance.now();

                    self.performanceMetrics.drawCalls++;
                    self.updateMemoryMetrics();
                };

                log.info('🚀 Performance monitoring started');
            }
        };

        // 🆕 区域调整功能管理
        const RegionAdjustmentManager = {
            // 启用区域调整功能
            enableRegionAdjustment() {
                const regionSelector = document.getElementById('regionSelector');
                if (!regionSelector) return;

                // 启用指针事件
                regionSelector.style.pointerEvents = 'auto';

                // 添加调整手柄
                addResizeHandles(regionSelector);

                // 添加拖拽功能
                addRegionDragFunctionality(regionSelector);

                // 移除锁定类
                regionSelector.classList.remove('region-locked');

                log.info('🔧 Region adjustment enabled');
            },

            // 禁用区域调整功能
            disableRegionAdjustment() {
                const regionSelector = document.getElementById('regionSelector');
                if (!regionSelector) return;

                // 禁用指针事件
                regionSelector.style.pointerEvents = 'none';

                // 移除调整手柄
                removeResizeHandles(regionSelector);

                // 移除拖拽功能
                removeRegionDragFunctionality(regionSelector);

                // 移除拖拽属性
                regionSelector.removeAttribute('data-tauri-drag-region');

                // 添加锁定类
                regionSelector.classList.add('region-locked');

                log.info('🔧 Region adjustment disabled');
            },

            // 检查区域调整是否启用
            isRegionAdjustmentEnabled() {
                const regionSelector = document.getElementById('regionSelector');
                if (!regionSelector) return false;

                return regionSelector.style.pointerEvents !== 'none' &&
                       !regionSelector.classList.contains('region-locked');
            },

            // 根据状态设置区域调整功能
            setRegionAdjustmentByState(state) {
                switch (state) {
                    case AppState.REGION_PREVIEW:
                    case AppState.REGION_EDITING:
                        this.enableRegionAdjustment();
                        break;

                    case AppState.REGION_ANNOTATION:
                    case AppState.CAPTURING:
                    case AppState.SUCCESS:
                    case AppState.ERROR:
                        this.disableRegionAdjustment();
                        break;

                    case AppState.DETECTING:
                    case AppState.REGION_SELECTING:
                    default:
                        // 在这些状态下，区域选择器通常不可见
                        break;
                }

                log.debug(`🔧 Region adjustment set for state: ${state}`);
            }
        };

        // 🆕 区域拖拽功能管理
        function addRegionDragFunctionality(regionElement) {
            if (!regionElement) return;

            // 添加拖拽属性
            regionElement.setAttribute('data-tauri-drag-region', '');

            // 添加拖拽样式类
            regionElement.classList.add('draggable-region');

            log.debug('🔧 Region drag functionality added');
        }

        function removeRegionDragFunctionality(regionElement) {
            if (!regionElement) return;

            // 移除拖拽属性
            regionElement.removeAttribute('data-tauri-drag-region');

            // 移除拖拽样式类
            regionElement.classList.remove('draggable-region');

            log.debug('🔧 Region drag functionality removed');
        }

        // 🆕 新区域创建控制
        const NewRegionCreationManager = {
            // 是否允许创建新区域
            allowNewRegionCreation: true,

            // 启用新区域创建
            enableNewRegionCreation() {
                this.allowNewRegionCreation = true;
                log.info('🔧 New region creation enabled');
            },

            // 禁用新区域创建
            disableNewRegionCreation() {
                this.allowNewRegionCreation = false;
                log.info('🔧 New region creation disabled');
            },

            // 检查是否可以创建新区域
            canCreateNewRegion() {
                return this.allowNewRegionCreation;
            },

            // 根据状态设置新区域创建功能
            setNewRegionCreationByState(state) {
                switch (state) {
                    case AppState.DETECTING:
                    case AppState.REGION_SELECTING:
                        this.enableNewRegionCreation();
                        break;

                    case AppState.REGION_PREVIEW:
                    case AppState.REGION_EDITING:
                    case AppState.REGION_ANNOTATION:
                    case AppState.CAPTURING:
                    case AppState.SUCCESS:
                    case AppState.ERROR:
                        this.disableNewRegionCreation();
                        break;
                }

                log.debug(`🔧 New region creation set for state: ${state}`);
            }
        };

        // 🆕 显示标注Canvas
        function showAnnotationCanvas() {
            const canvas = document.getElementById('annotationCanvas');
            const region = appStateManager.selectedRegion;

            if (!canvas || !region) {
                log.error('🛠️ Cannot show annotation canvas: missing canvas or region');
                return;
            }

            // Canvas保持全屏尺寸，但只在选中区域内绘制
            // 这样可以避免坐标转换的复杂性
            canvas.style.left = '0px';
            canvas.style.top = '0px';
            canvas.style.width = window.innerWidth + 'px';
            canvas.style.height = window.innerHeight + 'px';

            // 确保Canvas尺寸正确（考虑设备像素比）
            const devicePixelRatio = window.canvasDevicePixelRatio || window.devicePixelRatio || 1;
            canvas.width = window.innerWidth * devicePixelRatio;
            canvas.height = window.innerHeight * devicePixelRatio;

            // 显示Canvas
            canvas.style.display = 'block';
            canvas.classList.add('active');

            // 重新设置绘图上下文样式（因为改变canvas尺寸会重置上下文）
            const ctx = canvas.getContext('2d');
            if (ctx) {
                // 重新应用设备像素比缩放
                ctx.scale(devicePixelRatio, devicePixelRatio);

                // 重新设置绘图样式
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';
                ctx.strokeStyle = '#ff6b35';
                ctx.lineWidth = 3;
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.textBaseline = 'top';
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                window.annotationContext = ctx;

                // 绘制区域边界指示（可选）
                drawRegionBoundary(ctx, region);
            }

            log.info('🛠️ Enhanced annotation canvas shown:', {
                region: region,
                canvasSize: { width: canvas.width, height: canvas.height },
                displaySize: { width: canvas.style.width, height: canvas.style.height },
                devicePixelRatio
            });
        }

        // 🆕 绘制区域边界指示
        function drawRegionBoundary(ctx, region) {
            if (!ctx || !region) return;

            // 保存当前状态
            ctx.save();

            // 设置边界样式
            ctx.strokeStyle = 'rgba(103, 58, 183, 0.8)';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);

            // 绘制区域边界
            ctx.strokeRect(region.x, region.y, region.width, region.height);

            // 恢复状态
            ctx.restore();

            log.debug('🛠️ Region boundary drawn:', region);
        }

        // 🆕 隐藏标注Canvas
        function hideAnnotationCanvas() {
            const canvas = document.getElementById('annotationCanvas');
            if (canvas) {
                canvas.style.display = 'none';
                canvas.classList.remove('active');
                log.info('🛠️ Annotation canvas hidden');
            }
        }

        // 🆕 清理标注模式
        function cleanupAnnotationMode() {
            // 移除标注模式的body类
            document.body.classList.remove('annotation-mode');

            // 隐藏共享工具栏和Canvas
            hideSharedToolbar();
            hideAnnotationCanvas();

            // 清理Canvas内容
            const canvas = document.getElementById('annotationCanvas');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                }
            }

            log.info('🛠️ Annotation mode cleaned up');
        }

        // 🆕 设置窗口大小变化监听
        function setupWindowResizeListener() {
            let resizeTimeout;

            window.addEventListener('resize', () => {
                // 防抖处理，避免频繁重定位
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    if (appStateManager.currentState === AppState.REGION_ANNOTATION &&
                        appStateManager.selectedRegion) {
                        log.info('🛠️ Window resized, repositioning toolbar');
                        positionToolbarResponsively(appStateManager.selectedRegion);
                    }
                }, 150);
            });
        }

        // 🔧 CRITICAL FIX: 防止重复事件处理的全局标志
        let isProcessingToolbarCapture = false;
        let lastToolbarCaptureTime = 0;
        const TOOLBAR_CAPTURE_DEBOUNCE = 1000; // 1秒内只处理一次

        // 🆕 设置工具栏事件监听器
        function setupToolbarEventListeners() {
            console.log('🛠️ setupToolbarEventListeners called');
            if (window.__TAURI__) {
                console.log('🛠️ Tauri API available, setting up event listeners');
                log.info('🛠️ Setting up toolbar event listeners...');

                // 监听工具栏触发的捕获事件
                window.__TAURI__.event.listen('toolbar-trigger-capture', async (event) => {
                    const now = Date.now();

                    // 🔧 CRITICAL FIX: 防止重复处理同一个事件
                    if (isProcessingToolbarCapture || (now - lastToolbarCaptureTime < TOOLBAR_CAPTURE_DEBOUNCE)) {
                        log.warn('🛠️ ⚠️ Toolbar capture event ignored - already processing or too soon');
                        return;
                    }

                    isProcessingToolbarCapture = true;
                    lastToolbarCaptureTime = now;

                    log.info('🛠️ ✅ RECEIVED toolbar capture trigger event:', event.payload);
                    console.log('🛠️ ✅ RECEIVED toolbar capture trigger event:', event.payload);

                    const selectedTool = event.payload.selectedTool;
                    const currentRegion = appStateManager.selectedRegion;

                    if (!currentRegion) {
                        log.error('🛠️ No region selected for capture');
                        isProcessingToolbarCapture = false;
                        return;
                    }

                    log.info('🛠️ Triggering region capture with tool:', selectedTool);

                    try {
                        // 🔧 CRITICAL FIX: 隐藏工具栏以避免被截图捕获
                        log.info('🛠️ Hiding toolbar before screenshot capture');
                        try {
                            await window.__TAURI__.core.invoke('close_all_toolbar_windows');
                            log.info('🛠️ ✅ Toolbar hidden successfully');
                        } catch (toolbarError) {
                            log.warn('🛠️ ⚠️ Failed to hide toolbar, continuing with capture:', toolbarError);
                        }

                        // 🔧 ENHANCED: 使用新的工具选择捕获命令
                        log.info('🛠️ Triggering region capture with tool selection:', selectedTool);

                        const regionData = {
                            x: parseFloat(currentRegion.x),
                            y: parseFloat(currentRegion.y),
                            width: parseFloat(currentRegion.width),
                            height: parseFloat(currentRegion.height),
                            monitor_index: 0
                        };

                        const result = await window.__TAURI__.core.invoke('capture_region_with_tool_selection', {
                            region: regionData,
                            selectedTool: selectedTool
                        });

                        if (result && result.path) {
                            log.info('🛠️ ✅ Region capture with tool selection completed successfully');
                            log.info('🛠️ Screenshot saved to:', result.path);

                            // 切换到成功状态
                            appStateManager.setState(AppState.SUCCESS);
                        } else {
                            throw new Error('Region capture with tool selection failed - no path returned');
                        }
                    } catch (error) {
                        log.error('🛠️ Failed to capture region with tool selection:', error);
                        appStateManager.setState(AppState.ERROR);
                    } finally {
                        // 🔧 CRITICAL FIX: 确保标志被重置
                        isProcessingToolbarCapture = false;
                    }
                });

                // 🆕 监听工具栏注解模式启动事件
                window.__TAURI__.event.listen('toolbar-event', async (event) => {
                    log.info('🎨 Received toolbar event:', event.payload);

                    const eventType = event.payload.type;
                    const eventData = event.payload.data;

                    if (eventType === 'annotation-mode-start') {
                        log.info('🎨 Annotation mode start requested from toolbar');
                        await handleAnnotationModeStart(eventData);
                    }
                });

                log.info('🛠️ Toolbar event listeners setup completed');
            }
        }

        // 🆕 处理注解模式启动
        async function handleAnnotationModeStart(eventData) {
            try {
                log.info('🎨 Starting annotation mode transition:', eventData);

                const currentRegion = appStateManager.selectedRegion;
                if (!currentRegion) {
                    log.error('🎨 No region selected for annotation mode');
                    return;
                }

                // 首先捕获当前选中的区域
                log.info('🎨 Capturing region for annotation:', currentRegion);
                const captureResult = await captureSelectedRegion(currentRegion);

                if (!captureResult || !captureResult.success || !captureResult.path) {
                    log.error('🎨 Failed to capture region for annotation:', captureResult);
                    return;
                }

                const screenshotPath = captureResult.path;

                // 准备传输数据
                const regionData = {
                    x: currentRegion.x,
                    y: currentRegion.y,
                    width: currentRegion.width,
                    height: currentRegion.height,
                    timestamp: Date.now()
                };

                const toolbarState = {
                    selectedTool: eventData.tool,
                    style: eventData.style || {
                        strokeColor: '#FF0000',
                        strokeWidth: 2,
                        fillColor: null,
                        fontSize: 16,
                        fontFamily: 'Arial'
                    },
                    layout: 'vertical'
                };

                log.info('🎨 Creating annotation window with data:', {
                    regionData,
                    screenshotPath,
                    toolbarState
                });

                // 调用后端创建注解窗口
                const annotationWindowId = await window.__TAURI__.core.invoke('create_annotation_window', {
                    regionData: regionData,
                    screenshotPath: screenshotPath,
                    toolbarState: toolbarState
                });

                log.info('🎨 Annotation window created successfully:', annotationWindowId);

                // 等待一小段时间确保注解窗口完全加载
                setTimeout(() => {
                    // 关闭当前的区域选择覆盖层
                    log.info('🎨 Closing region selection overlay after annotation window creation');
                    cleanupAndCloseOverlay();
                }, 1000);

            } catch (error) {
                log.error('🎨 Failed to start annotation mode:', error);

                // 显示错误提示
                showErrorMessage('Failed to start annotation mode: ' + error.message);
            }
        }

        // 🆕 显示错误消息
        function showErrorMessage(message) {
            // 创建临时错误提示
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 0, 0, 0.9);
                color: white;
                padding: 20px;
                border-radius: 8px;
                font-size: 16px;
                z-index: 10000;
                max-width: 400px;
                text-align: center;
            `;
            errorDiv.textContent = message;
            document.body.appendChild(errorDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 3000);
        }

        // 🆕 清理并关闭覆盖层
        function cleanupAndCloseOverlay() {
            try {
                log.info('🧹 Starting overlay cleanup process');

                // 清理状态管理器
                if (typeof appStateManager !== 'undefined') {
                    appStateManager.setState(AppState.IDLE);
                    log.info('🧹 App state reset to IDLE');
                }

                // 清理区域选择器
                hideRegionSelector();
                log.info('🧹 Region selector hidden');

                // 清理事件监听器
                cleanupEventListeners();
                log.info('🧹 Event listeners cleaned up');

                // 清理工具栏组件
                if (typeof cleanupSharedToolbarComponents === 'function') {
                    cleanupSharedToolbarComponents();
                    log.info('🧹 Shared toolbar components cleaned up');
                }

                // 清理Canvas
                cleanupCanvas();
                log.info('🧹 Canvas cleaned up');

                // 最后关闭窗口
                setTimeout(() => {
                    closeOverlay();
                }, 100);

            } catch (error) {
                log.error('🧹 Error during cleanup:', error);
                // 即使清理失败也要尝试关闭窗口
                closeOverlay();
            }
        }

        // 🆕 清理事件监听器
        function cleanupEventListeners() {
            try {
                // 清理全局事件监听器
                const eventCaptureLayer = document.getElementById('eventCaptureLayer');
                if (eventCaptureLayer) {
                    // 移除鼠标事件
                    eventCaptureLayer.removeEventListener('mousemove', handleMouseMove);
                    eventCaptureLayer.removeEventListener('click', handleClick);
                    eventCaptureLayer.removeEventListener('mousedown', handleMouseDown);
                    eventCaptureLayer.removeEventListener('mouseup', handleMouseUp);
                }

                // 清理键盘事件
                document.removeEventListener('keydown', handleKeyboard);

                log.info('🧹 Event listeners removed successfully');
            } catch (error) {
                log.warn('🧹 Error removing event listeners:', error);
            }
        }

        // 🆕 清理Canvas
        function cleanupCanvas() {
            try {
                const canvas = document.getElementById('annotationCanvas');
                if (canvas) {
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                    }
                    log.info('🧹 Canvas cleared');
                }
            } catch (error) {
                log.warn('🧹 Error cleaning up canvas:', error);
            }
        }

        function setupEventListeners() {
            console.log('🚀 setupEventListeners called');
            logToBackend('INFO', '🚀 Setting up event listeners');

            // 确保窗口获得焦点以接收键盘事件
            window.focus();
            document.body.focus();

            // 🆕 设置工具栏事件监听器
            console.log('🚀 About to call setupToolbarEventListeners');
            setupToolbarEventListeners();

            // 🗑️ REMOVED: initializeRegionToolbar() - replaced by independent toolbar system

            // 🆕 初始化事件处理器管理系统
            setupEventHandlerManager();

            // 🆕 设置键盘事件监听器（全局）
            document.addEventListener('keydown', handleKeyboard, { passive: false });

            logToBackend('INFO', '🚀 Event listeners setup completed with handler management system');
        }

        // 🆕 设置事件处理器管理系统 (TEMPORARILY DISABLED)
        function setupEventHandlerManager() {
            const eventCaptureLayer = document.getElementById('eventCaptureLayer');
            if (!eventCaptureLayer) {
                logToBackend('ERROR', '❌ Event capture layer not found!');
                return;
            }

            logToBackend('INFO', '🚀 Event capture layer found, but EventHandlerManager temporarily disabled');
            logToBackend('INFO', '🔧 Using original event listeners for window detection and region selection');

            // TEMPORARILY DISABLED: EventHandlerManager to fix window detection and region selection
            // The original event listeners are being used instead
            // TODO: Fix EventHandlerManager integration in follow-up

            /*
            // 根据当前状态启用相应的事件处理器
            const currentState = appStateManager.currentState;

            switch (currentState) {
                case AppState.DETECTING:
                case AppState.REGION_SELECTING:
                case AppState.REGION_PREVIEW:
                case AppState.REGION_EDITING:
                    EventHandlerManager.enableSelectionMode();
                    break;

                case AppState.REGION_ANNOTATION:
                    EventHandlerManager.enableAnnotationMode();
                    break;

                default:
                    // 默认启用选择模式
                    EventHandlerManager.enableSelectionMode();
                    break;
            }
            */

            logToBackend('INFO', '🚀 Event handler setup completed (using original system)');
        }

        // 🔧 PRODUCTION: 调试信息函数 - 仅在开发模式下启用
        function updateDebugInfo(message, color = 'green') {
            // 生产环境下不显示调试信息，保持界面干净
            if (DEBUG_MODE) {
                const debugInfo = document.getElementById('debugInfo');
                if (debugInfo) {
                    debugInfo.textContent = 'DEBUG: ' + message;
                    debugInfo.style.background = color;
                }
            }
        }

        log.info('🚀 JavaScript execution started');
        log.info('🚀 Document ready state: ' + document.readyState);

        // 立即测试基础功能
        log.debug('🧪 Testing basic functionality...');
        log.debug('🧪 Window object available: ' + !!window);
        log.debug('🧪 Document object available: ' + !!document);
        log.debug('🧪 Tauri API available: ' + !!window.__TAURI__);

        // 🔧 CRITICAL FIX: 日志级别系统 - 移到全局作用域
        const DEBUG_MODE = false; // 设为true启用详细调试日志

        // 🚀 LOG UNIFICATION: 延迟console重定向，等待log对象初始化
        function setupConsoleRedirection() {
            // 🔧 CRITICAL FIX: 禁用控制台重定向以防止无限循环
            // 控制台重定向会导致 log.debug() -> logAPI.debug() -> console重定向 -> log.debug() 的无限循环
            // 暂时禁用此功能，直到找到更好的解决方案
            console.log('[LOG] Console redirection disabled to prevent infinite loops');
            return;

            if (!DEBUG_MODE && typeof log !== 'undefined') {
                const originalConsole = {
                    log: console.log,
                    error: console.error,
                    warn: console.warn,
                    info: console.info
                };

                // 🔧 添加循环检测标志
                let isRedirecting = false;

                // 重写console方法，在生产模式下使用统一日志系统
                console.log = function(message, ...args) {
                    if (isRedirecting) {
                        originalConsole.log(message, ...args);
                        return;
                    }

                    if (typeof message === 'string' && message.includes('[FRONTEND]')) {
                        const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                        if (log && log.debug) {
                            isRedirecting = true;
                            log.debug(cleanMessage, args.length > 0 ? args[0] : undefined);
                            isRedirecting = false;
                        } else {
                            originalConsole.log(message, ...args);
                        }
                    } else {
                        originalConsole.log(message, ...args);
                    }
                };

                console.log('[LOG] Console redirection setup completed');
            }
        }
        const LOG_LEVELS = {
            ERROR: 0,   // 错误信息，总是显示
            WARN: 1,    // 警告信息，总是显示
            INFO: 2,    // 重要信息，总是显示
            DEBUG: 3,   // 调试信息，DEBUG_MODE时显示
            TRACE: 4    // 跟踪信息（如鼠标移动），DEBUG_MODE时显示
        };
        const CURRENT_LOG_LEVEL = DEBUG_MODE ? LOG_LEVELS.TRACE : LOG_LEVELS.INFO;

        function logWithLevel(level, message, data = null) {
            if (level <= CURRENT_LOG_LEVEL) {
                const prefix = Object.keys(LOG_LEVELS)[level];
                console.log(`[FRONTEND-${prefix}] ${message}`, data || '');
            }
        }

        // 应用状态管理 - 扩展支持区域截图的7状态机
        const AppState = {
            DETECTING: 'detecting',              // 窗口检测状态
            WINDOW_SELECTED: 'selected',         // 窗口已选中
            REGION_SELECTING: 'region_selecting', // 区域选择中
            REGION_PREVIEW: 'region_preview',     // 区域预览状态
            REGION_EDITING: 'region_editing',     // 区域标注状态 (旧版本，保留兼容性)
            REGION_ANNOTATION: 'region_annotation', // 🆕 区域标注模式 (新版本，使用共享工具栏)
            CAPTURING: 'capturing',              // 截图进行中
            EDITING: 'editing',                  // 编辑模式
            SUCCESS: 'success',                  // 成功状态
            ERROR: 'error'                       // 错误状态
        };

        const appStateManager = {
            currentState: AppState.DETECTING,
            selectedWindow: null,
            selectedRegion: null, // 区域选择状态
            screenshotData: null,
            editingRegion: null,
            errorTimeout: null, // 用于保存错误恢复的定时器ID

            setState(newState, data = {}) {
                log.info(`[STATE] ${this.currentState} → ${newState}`);

                // 允许同一状态的重复设置
                if (this.currentState === newState) {
                    log.debug(`[STATE] Same state transition: ${newState}`);
                    Object.assign(this, data);
                    return true;
                }

                // 验证状态转换的合法性
                if (!this.isValidTransition(this.currentState, newState)) {
                    log.warn(`[STATE] Invalid transition: ${this.currentState} → ${newState}`);
                    return false;
                }

                // 如果存在正在执行的错误恢复定时器，则清除它
                if (this.errorTimeout) {
                    clearTimeout(this.errorTimeout);
                    this.errorTimeout = null;
                }

                const oldState = this.currentState;

                // 🆕 状态转换清理逻辑
                this.cleanupStateTransition(oldState, newState);

                this.currentState = newState;
                Object.assign(this, data);
                this.onStateChange(newState, oldState);

                // 关键修复：如果新状态是ERROR，则在短暂显示错误信息后自动恢复
                if (newState === AppState.ERROR) {
                    log.error("[STATE] An error occurred. Reverting to detection mode in 2 seconds.");
                    this.errorTimeout = setTimeout(() => {
                        this.setState(AppState.DETECTING, {
                            selectedWindow: null,
                            selectedRegion: null,
                            screenshotData: null,
                            editingRegion: null
                        });
                    }, 2000);
                }
                return true;
            },

            // 验证状态转换的合法性
            isValidTransition(fromState, toState) {
                const validTransitions = {
                    [AppState.DETECTING]: [AppState.WINDOW_SELECTED, AppState.REGION_SELECTING, AppState.ERROR],
                    [AppState.WINDOW_SELECTED]: [AppState.CAPTURING, AppState.DETECTING, AppState.ERROR],
                    [AppState.REGION_SELECTING]: [AppState.REGION_PREVIEW, AppState.DETECTING, AppState.ERROR],
                    [AppState.REGION_PREVIEW]: [AppState.REGION_EDITING, AppState.REGION_ANNOTATION, AppState.CAPTURING, AppState.DETECTING, AppState.SUCCESS],
                    [AppState.REGION_EDITING]: [AppState.REGION_PREVIEW, AppState.SUCCESS, AppState.ERROR],
                    [AppState.REGION_ANNOTATION]: [AppState.REGION_PREVIEW, AppState.CAPTURING, AppState.SUCCESS, AppState.DETECTING, AppState.ERROR], // 🆕 新状态转换
                    [AppState.CAPTURING]: [AppState.SUCCESS, AppState.ERROR, AppState.DETECTING],
                    [AppState.EDITING]: [AppState.SUCCESS, AppState.ERROR],
                    [AppState.SUCCESS]: [AppState.DETECTING],
                    [AppState.ERROR]: [AppState.DETECTING]
                };

                return validTransitions[fromState]?.includes(toState) || false;
            },

            // 🆕 状态转换清理逻辑
            cleanupStateTransition(oldState, newState) {
                log.info(`[CLEANUP] State transition cleanup: ${oldState} → ${newState}`);

                // 从标注模式退出时的清理
                if (oldState === AppState.REGION_ANNOTATION) {
                    cleanupAnnotationMode();
                    log.info('[CLEANUP] Annotation mode cleaned up');
                }

                // 进入检测模式时的全面清理
                if (newState === AppState.DETECTING) {
                    // 清理所有UI元素
                    hideRegionSelector();
                    // 🗑️ REMOVED: hideRegionToolbar() - replaced by independent toolbar system
                    hideSharedToolbar();
                    hideAnnotationCanvas();
                    cleanupAnnotationMode();

                    // 🔧 CRITICAL FIX: Close independent toolbars when transitioning to DETECTING
                    if (window.currentRegionToolbarId) {
                        // Use async cleanup for toolbar closure
                        (async () => {
                            try {
                                log.info('[CLEANUP] Closing independent toolbar:', window.currentRegionToolbarId);
                                await window.__TAURI__.core.invoke('close_toolbar_window', {
                                    toolbarId: window.currentRegionToolbarId
                                });
                                log.info('[CLEANUP] Independent toolbar closed successfully');
                                window.currentRegionToolbarId = null;
                            } catch (error) {
                                log.error('[CLEANUP] Failed to close independent toolbar:', error);
                                // Fallback: close all active toolbars
                                try {
                                    await window.__TAURI__.core.invoke('close_all_active_toolbars');
                                    log.info('[CLEANUP] All toolbars closed via fallback');
                                    window.currentRegionToolbarId = null;
                                } catch (fallbackError) {
                                    log.error('[CLEANUP] Fallback toolbar closure failed:', fallbackError);
                                }
                            }
                        })();
                    }

                    // 重置相关状态
                    this.selectedRegion = null;
                    this.selectedWindow = null;
                    this.screenshotData = null;
                    this.editingRegion = null;

                    log.info('[CLEANUP] Full cleanup completed for DETECTING state');
                }

                // 从预览模式进入标注模式时的准备
                if (oldState === AppState.REGION_PREVIEW && newState === AppState.REGION_ANNOTATION) {
                    // 隐藏旧工具栏，准备显示新工具栏
                    // 🗑️ REMOVED: hideRegionToolbar() - replaced by independent toolbar system
                    log.info('[CLEANUP] Prepared for annotation mode transition');
                }
            },

            onStateChange(newState, oldState) {
                this.updateUI(newState);
                this.updateEventHandlers(newState, oldState);
            },

            updateUI(state) {
                const dimmingOverlay = document.getElementById('dimmingOverlay');
                const editingCanvas = document.getElementById('editingCanvas');
                const screenshotContainer = document.getElementById('screenshotContainer');
                const crosshair = document.getElementById('crosshair');
                const instructions = document.querySelector('.instructions');
                const regionSelector = document.getElementById('regionSelector');
                const stateIndicator = document.getElementById('stateIndicator');
                const stateText = document.getElementById('stateText');

                // 先将所有UI元素重置为默认状态
                if (dimmingOverlay) dimmingOverlay.classList.remove('active');
                if (screenshotContainer) screenshotContainer.classList.remove('active');
                if (editingCanvas) editingCanvas.classList.remove('active');
                if (crosshair) crosshair.style.display = 'none';

                // 移除调整手柄（除非是预览或编辑状态）
                if (regionSelector && state !== AppState.REGION_PREVIEW && state !== AppState.REGION_EDITING && state !== AppState.REGION_ANNOTATION) {
                    removeResizeHandles(regionSelector);
                    regionSelector.style.pointerEvents = 'none'; // 禁用交互
                }

                // 更新状态指示器
                if (stateIndicator && stateText) {
                    stateIndicator.style.display = 'block';
                    stateText.textContent = state.toUpperCase().replace('_', ' ');

                    // 根据状态设置颜色
                    switch (state) {
                        case AppState.DETECTING:
                            stateIndicator.style.background = 'rgba(33, 150, 243, 0.9)'; // 蓝色
                            break;
                        case AppState.REGION_SELECTING:
                            stateIndicator.style.background = 'rgba(255, 193, 7, 0.9)'; // 黄色
                            break;
                        case AppState.REGION_PREVIEW:
                            stateIndicator.style.background = 'rgba(255, 107, 53, 0.9)'; // 橙色
                            break;
                        case AppState.REGION_EDITING:
                            stateIndicator.style.background = 'rgba(244, 67, 54, 0.9)'; // 红色
                            break;
                        case AppState.REGION_ANNOTATION: // 🆕 新状态颜色
                            stateIndicator.style.background = 'rgba(103, 58, 183, 0.9)'; // 深紫色
                            break;
                        case AppState.CAPTURING:
                            stateIndicator.style.background = 'rgba(156, 39, 176, 0.9)'; // 紫色
                            break;
                        case AppState.SUCCESS:
                            stateIndicator.style.background = 'rgba(76, 175, 80, 0.9)'; // 绿色
                            break;
                        case AppState.ERROR:
                            stateIndicator.style.background = 'rgba(244, 67, 54, 0.9)'; // 红色
                            break;
                        default:
                            stateIndicator.style.background = 'rgba(0, 0, 0, 0.8)'; // 默认黑色
                    }
                }

                // 根据当前状态精确设置UI
                switch (state) {
                    case AppState.DETECTING:
                        if (crosshair) crosshair.style.display = 'block';
                        if (instructions) {
                            instructions.innerHTML = `<h3>🎯 Smart Window Detection</h3><p>Move mouse to highlight windows | Click to select | Drag to select region</p><p><strong>ESC</strong> to exit | <strong>Space</strong> for region mode</p>`;
                        }
                        break;

                    case AppState.REGION_SELECTING:
                        if (crosshair) crosshair.style.display = 'block';
                        if (instructions) {
                            instructions.innerHTML = `<h3>📐 Region Selection</h3><p>Drag to select area | Release to complete</p><p><strong>ESC</strong> to cancel</p>`;
                        }
                        break;

                    case AppState.REGION_PREVIEW:
                        if (crosshair) crosshair.style.display = 'none';
                        if (instructions) {
                            instructions.innerHTML = `<h3>👁️ Region Preview</h3><p>Region selected | Ready to capture</p><p><strong>Enter</strong> to capture | <strong>ESC</strong> to cancel</p>`;
                        }
                        // 更新区域选择器样式为预览状态（橙色边框）
                        if (regionSelector) {
                            regionSelector.style.border = '2px dashed #FF6B35';
                            regionSelector.style.background = 'rgba(255, 107, 53, 0.1)';
                            regionSelector.style.pointerEvents = 'auto'; // 允许交互
                            // 移除可能存在的拖拽属性，避免窗口拖拽冲突
                            regionSelector.removeAttribute('data-tauri-drag-region');
                            // 添加调整手柄
                            addResizeHandles(regionSelector);
                        }
                        // 显示工具栏
                        // 🗑️ REMOVED: showRegionToolbar() - replaced by independent toolbar system
                        break;

                    case AppState.REGION_EDITING:
                        if (crosshair) crosshair.style.display = 'none';
                        if (instructions) {
                            instructions.innerHTML = `<h3>✏️ Region Editing</h3><p>Use tools to annotate | Region locked</p><p><strong>Enter</strong> to save | <strong>ESC</strong> to cancel</p>`;
                        }
                        // 更新区域选择器样式为编辑状态（红色边框）
                        if (regionSelector) {
                            regionSelector.style.border = '2px dashed #FF0000';
                            regionSelector.style.background = 'rgba(255, 0, 0, 0.1)';
                            regionSelector.style.pointerEvents = 'auto'; // 允许交互
                            // 移除拖拽属性
                            regionSelector.removeAttribute('data-tauri-drag-region');
                            // 在编辑状态下也添加调整手柄
                            addResizeHandles(regionSelector);
                        }
                        // 显示工具栏
                        // 🗑️ REMOVED: showRegionToolbar() - replaced by independent toolbar system
                        break;

                    case AppState.REGION_ANNOTATION: // 🆕 新的标注状态
                        if (crosshair) crosshair.style.display = 'none';
                        if (instructions) {
                            instructions.innerHTML = `<h3>🎨 Region Annotation</h3><p>Use annotation tools | Region locked for editing</p><p><strong>ESC</strong> to exit annotation mode</p>`;
                        }
                        // 更新区域选择器样式为标注状态（深紫色边框）
                        if (regionSelector) {
                            regionSelector.style.border = '2px solid #673AB7';
                            regionSelector.style.background = 'rgba(103, 58, 183, 0.1)';
                            regionSelector.style.pointerEvents = 'none'; // 禁用区域调整
                            // 移除拖拽属性和调整手柄
                            regionSelector.removeAttribute('data-tauri-drag-region');
                            removeResizeHandles(regionSelector);
                        }
                        // 添加标注模式的body类
                        document.body.classList.add('annotation-mode');
                        // 隐藏旧工具栏，显示共享工具栏
                        // 🗑️ REMOVED: hideRegionToolbar() - replaced by independent toolbar system
                        showSharedToolbar();
                        // 显示并定位标注Canvas
                        showAnnotationCanvas();
                        break;

                    case AppState.CAPTURING:
                        if (crosshair) crosshair.style.display = 'none';
                        if (instructions) {
                            instructions.innerHTML = `<h3>📸 Capturing...</h3><p>Please wait while capturing screenshot</p>`;
                        }
                        break;

                    case AppState.EDITING:
                        if (dimmingOverlay) dimmingOverlay.classList.add('active');
                        if (screenshotContainer) screenshotContainer.classList.add('active');
                        if (editingCanvas) editingCanvas.classList.add('active');
                        if (instructions) {
                            instructions.innerHTML = `<h3>🎨 Editing Mode</h3><p>Use tools to edit screenshot</p><p><strong>ESC</strong> to exit</p>`;
                        }
                        break;

                    case AppState.SUCCESS:
                        if (instructions) {
                            instructions.innerHTML = `<h3 style="color: green;">✅ Success!</h3><p>Screenshot captured successfully</p>`;
                        }
                        break;

                    case AppState.ERROR:
                        if (instructions) {
                            instructions.innerHTML = `<h3 style="color: red;">❌ Capture Failed</h3><p>Please try again. Reverting automatically...</p>`;
                        }
                        if (crosshair) crosshair.style.display = 'none';
                        break;
                }

                // 🆕 根据状态设置区域调整功能
                if (typeof RegionAdjustmentManager !== 'undefined') {
                    RegionAdjustmentManager.setRegionAdjustmentByState(state);
                }

                // 🆕 根据状态设置新区域创建功能
                if (typeof NewRegionCreationManager !== 'undefined') {
                    NewRegionCreationManager.setNewRegionCreationByState(state);
                }

                log.info(`[UI] Updated UI for state: ${state}`);
            },

            updateEventHandlers(newState, oldState) {
                log.info(`[EVENTS] Updating event handlers: ${oldState} → ${newState}`);

                // 🆕 根据状态切换事件处理器
                this.switchEventHandlers(newState, oldState);

                // 根据新状态管理窗口检测
                if (newState === AppState.DETECTING) {
                    this.enableWindowDetection();
                } else {
                    this.disableWindowDetection();
                }

                // 根据新状态管理编辑模式
                if (newState === AppState.EDITING || newState === AppState.REGION_EDITING || newState === AppState.REGION_ANNOTATION) {
                    this.enableEditingMode();
                } else {
                    this.disableEditingMode();
                }

                // 根据状态管理拖拽功能
                switch (newState) {
                    case AppState.DETECTING:
                    case AppState.REGION_SELECTING:
                        // 启用拖拽检测
                        detectionEnabled = true;
                        break;

                    case AppState.REGION_PREVIEW:
                        // 禁用拖拽检测，但保持区域可见
                        detectionEnabled = false;
                        break;

                    case AppState.CAPTURING:
                    case AppState.EDITING:
                    case AppState.REGION_EDITING:
                    case AppState.REGION_ANNOTATION: // 🆕 标注模式也禁用检测
                        // 完全禁用交互
                        detectionEnabled = false;
                        break;
                }
            },

            disableWindowDetection() {
                detectionEnabled = false;
                hideHighlight();
            },

            enableWindowDetection() {
                detectionEnabled = true;
            },

            enableEditingMode() {
                log.info('[STATE] 🎨 Enabling editing mode - disabling mouse interactions');

                // 🔧 BUG FIX: 禁用鼠标交互，防止意外拖拽和绘制
                this.disableMouseInteractions();

                // 显示编辑模式指示器
                this.showEditingModeIndicator();
            },

            disableEditingMode() {
                log.info('[STATE] 🎨 Disabling editing mode - re-enabling mouse interactions');

                // 重新启用鼠标交互
                this.enableMouseInteractions();

                // 隐藏编辑模式指示器
                this.hideEditingModeIndicator();
            },

            disableMouseInteractions() {
                // 禁用事件捕获层的鼠标事件
                if (eventCaptureLayer) {
                    eventCaptureLayer.style.pointerEvents = 'none';
                    log.debug('[STATE] 🚫 Mouse interactions disabled on event capture layer');
                }

                // 设置全局标志
                window.editingModeActive = true;
                window.mouseInteractionsDisabled = true;
            },

            enableMouseInteractions() {
                // 重新启用事件捕获层的鼠标事件
                if (eventCaptureLayer) {
                    eventCaptureLayer.style.pointerEvents = 'auto';
                    log.debug('[STATE] ✅ Mouse interactions re-enabled on event capture layer');
                }

                // 清除全局标志
                window.editingModeActive = false;
                window.mouseInteractionsDisabled = false;
            },

            // 🆕 根据状态切换事件处理器 (TEMPORARILY DISABLED)
            switchEventHandlers(newState, oldState) {
                log.info(`[EVENTS] Event handler switching temporarily disabled: ${oldState} → ${newState}`);
                log.info(`[EVENTS] Using original event system for window detection and region selection`);

                // TEMPORARILY DISABLED: EventHandlerManager switching to fix window detection and region selection
                // The original event listeners remain active for all states
                // TODO: Fix EventHandlerManager integration in follow-up

                /*
                switch (newState) {
                    case AppState.DETECTING:
                    case AppState.REGION_SELECTING:
                    case AppState.REGION_PREVIEW:
                    case AppState.REGION_EDITING:
                        // 选择模式：启用区域选择和窗口检测事件
                        if (typeof EventHandlerManager !== 'undefined') {
                            EventHandlerManager.enableSelectionMode();
                        }
                        break;

                    case AppState.REGION_ANNOTATION:
                        // 标注模式：启用Canvas绘图事件
                        if (typeof EventHandlerManager !== 'undefined') {
                            EventHandlerManager.enableAnnotationMode();
                        }
                        break;

                    case AppState.CAPTURING:
                    case AppState.EDITING:
                    case AppState.SUCCESS:
                    case AppState.ERROR:
                        // 其他状态：禁用所有自定义事件处理器
                        if (typeof EventHandlerManager !== 'undefined') {
                            EventHandlerManager.disableAllHandlers();
                        }
                        break;
                }
                */

                log.debug(`[EVENTS] Event handler switching skipped for state: ${newState}`);
            },

            showEditingModeIndicator() {
                // 在截图容器上添加编辑模式指示器
                const screenshotContainer = document.getElementById('screenshotContainer');
                if (screenshotContainer) {
                    screenshotContainer.classList.add('editing-mode');
                    log.debug('[STATE] 🎨 Editing mode indicator shown');
                }
            },

            hideEditingModeIndicator() {
                const screenshotContainer = document.getElementById('screenshotContainer');
                if (screenshotContainer) {
                    screenshotContainer.classList.remove('editing-mode');
                    log.debug('[STATE] 🎨 Editing mode indicator hidden');
                }
            },


        };

        // 测试状态管理器
        log.debug('🧪 State manager initialized: ' + !!appStateManager);
        log.debug('🧪 Initial state: ' + appStateManager.currentState);
        log.debug('🧪 AppState constants', AppState);

        // 🔧 CRITICAL FIX: Alternative click handling using Tauri WebView API
        log.debug('🔧 Setting up alternative Tauri-based click handling...');

        // Check if we can use Tauri's WebView window API for events
        if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
            console.log('[FRONTEND] 🔧 Tauri WebView API available, setting up window-level event handling');

            try {
                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                console.log('[FRONTEND] 🔧 Current window obtained:', !!currentWindow);

                // Listen for window-level events
                currentWindow.listen('tauri://click', (event) => {
                    console.log('[FRONTEND] 🔧 Tauri window click event:', event);
                });

            } catch (error) {
                console.error('[FRONTEND] 🔧 Failed to set up Tauri window events:', error);
            }
        } else {
            console.log('[FRONTEND] 🔧 Tauri WebView API not available, using DOM events only');
        }

        let currentWindowId = null;
        let windows = [];
        let detectionEnabled = true;
        let isDragging = false;
        let dragStartX = 0;
        let dragStartY = 0;

        // 🔧 ENHANCED LOGGING: Log initial state
        log.info('🚀 [INIT] === OVERLAY INITIALIZATION ===');
        log.info('🚀 [INIT] detectionEnabled:', detectionEnabled);
        log.info('🚀 [INIT] isDragging:', isDragging);
        log.info('🚀 [INIT] currentState:', appStateManager.currentState);

        // 🔧 BUG FIX: 保存最后的点击坐标用于截图
        let lastClickX = 0;
        let lastClickY = 0;

        // 窗口选择模式相关变量
        let isWindowSelected = false;
        let selectedWindowId = null;

        const windowHighlight = document.getElementById('windowHighlight');
        const windowInfo = document.getElementById('windowInfo');
        const crosshair = document.getElementById('crosshair');

        // 🚀 LOG UNIFICATION: 创建统一的console替换机制
        const unifiedConsole = {
            log: (message, ...args) => {
                if (DEBUG_MODE) {
                    console.log(message, ...args);
                } else {
                    // 生产模式下使用统一日志系统
                    const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                    log.debug(cleanMessage, args.length > 0 ? args[0] : undefined);
                }
            },
            error: (message, ...args) => {
                const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                log.error(cleanMessage, args.length > 0 ? args[0] : undefined);
            },
            warn: (message, ...args) => {
                const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                log.warn(cleanMessage, args.length > 0 ? args[0] : undefined);
            },
            info: (message, ...args) => {
                const cleanMessage = message.replace(/\[FRONTEND\]\s*/, '').replace(/🔧|🎯|🧪|🚨|✅|❌|⚠️|🔍|📸|🛠️|🚪|🖱️|🧠/g, '').trim();
                log.info(cleanMessage, args.length > 0 ? args[0] : undefined);
            }
        };

        const eventCaptureLayer = document.getElementById('eventCaptureLayer');

        // 强化事件监听 - 绑定到事件捕获层
        console.log('[FRONTEND] 🎯 Setting up event listeners on capture layer...');
        console.log('[FRONTEND] 🎯 Event capture layer element:', eventCaptureLayer);
        console.log('[FRONTEND] 🎯 Event capture layer style:', window.getComputedStyle(eventCaptureLayer).pointerEvents);

        // 🔧 CRITICAL FIX: 验证pointer-events层级修复
        console.log('[FRONTEND] 🔧 Verifying pointer-events hierarchy fix:');
        console.log('[FRONTEND] 🔧   - body pointer-events:', window.getComputedStyle(document.body).pointerEvents);
        console.log('[FRONTEND] 🔧   - overlay-container pointer-events:', window.getComputedStyle(document.querySelector('.overlay-container')).pointerEvents);
        console.log('[FRONTEND] 🔧   - event-capture-layer pointer-events:', window.getComputedStyle(eventCaptureLayer).pointerEvents);

        // 🚨 CRITICAL TEST: Verify our CSS fixes are applied
        const bodyPointerEvents = window.getComputedStyle(document.body).pointerEvents;
        const containerPointerEvents = window.getComputedStyle(document.querySelector('.overlay-container')).pointerEvents;
        const layerPointerEvents = window.getComputedStyle(eventCaptureLayer).pointerEvents;

        if (bodyPointerEvents === 'none') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: body still has pointer-events: none!');
        }
        if (containerPointerEvents === 'none') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: overlay-container still has pointer-events: none!');
        }
        if (layerPointerEvents !== 'auto') {
            console.error('[FRONTEND] 🚨 CRITICAL ERROR: event-capture-layer does not have pointer-events: auto!');
        }

        if (bodyPointerEvents === 'auto' && containerPointerEvents === 'auto' && layerPointerEvents === 'auto') {
            console.log('[FRONTEND] ✅ All pointer-events settings are correct!');
        }

        // 🔧 EMERGENCY FIX: Re-enable original event listeners to fix window detection and region selection
        // The EventHandlerManager will be fixed in a follow-up, but we need working functionality now
        log.info('🎯 [EVENT_SETUP] === SETTING UP EVENT LISTENERS ===');
        log.info('🎯 [EVENT_SETUP] eventCaptureLayer element:', eventCaptureLayer);
        log.info('🎯 [EVENT_SETUP] handleMouseMove function:', typeof handleMouseMove);

        eventCaptureLayer.addEventListener('mousemove', handleMouseMove, { passive: false });
        eventCaptureLayer.addEventListener('click', handleClick, { passive: false });
        eventCaptureLayer.addEventListener('mousedown', handleMouseDown, { passive: false });
        eventCaptureLayer.addEventListener('mouseup', handleMouseUp, { passive: false });

        log.info('🎯 [EVENT_SETUP] ✅ All event listeners attached to eventCaptureLayer');
        log.info('🎯 [EVENT_SETUP] === EVENT LISTENER SETUP COMPLETED ===');

        // 🔧 REMOVED: 删除重复的点击监听器，避免与handleClick冲突

        // 🔧 HIGH PRIORITY FIX: 条件化文档点击测试
        if (DEBUG_MODE) {
            document.addEventListener('click', function(e) {
                console.log('[FRONTEND] 🧪 DOCUMENT CLICK TEST - Click detected on document!');
                console.log('[FRONTEND] 🧪 Click coordinates:', e.clientX, e.clientY);
                console.log('[FRONTEND] 🧪 Target element:', e.target);

                // 🔧 CRITICAL DEBUG: 可视化文档点击反馈
                updateDebugInfo('DOC CLICK! Target: ' + e.target.tagName, 'red');
            }, { passive: false });
        }

        // 🔧 CRITICAL FIX: Test cursor events availability
        console.log('[FRONTEND] 🔧 Testing cursor events availability...');

        // Test if we can receive basic mouse events
        let mouseEventCount = 0;
        document.addEventListener('mousemove', function(e) {
            mouseEventCount++;
            // 🔧 MID PRIORITY FIX: 使用TRACE级别记录鼠标事件计数
            if (mouseEventCount % 50 === 0 && appStateManager.currentState !== AppState.EDITING) {
                logWithLevel(LOG_LEVELS.TRACE, `🔧 Mouse events working - count: ${mouseEventCount} at ${e.clientX}, ${e.clientY}`);
            }
        }, { passive: true });

        // 🔧 CRITICAL FIX: 移除重复的日志级别系统声明（已在全局作用域声明）

        if (DEBUG_MODE) {
            document.addEventListener('click', function(e) {
                console.log('[FRONTEND] 🚨 DOCUMENT CLICK DETECTED!');
                console.log('[FRONTEND] 🚨 Click at:', e.clientX, e.clientY);
                console.log('[FRONTEND] 🚨 Target:', e.target.tagName, e.target.className);
                console.log('[FRONTEND] 🚨 Event phase:', e.eventPhase);
                console.log('[FRONTEND] 🚨 Bubbles:', e.bubbles);
            }, { passive: false, capture: true });
        } // Use capture phase to catch early

        // Test pointer events
        document.addEventListener('pointerdown', function(e) {
            console.log('[FRONTEND] 🔧 POINTER DOWN detected:', e.pointerId, e.pointerType);
        }, { passive: false });

        document.addEventListener('pointerup', function(e) {
            console.log('[FRONTEND] 🔧 POINTER UP detected:', e.pointerId, e.pointerType);
        }, { passive: false });

        // 键盘事件仍然绑定到document
        // 🆕 DEPRECATED: 键盘事件监听器已在setupEventListeners中设置
        // document.addEventListener('keydown', handleKeyboard, { passive: false });

        console.log('[FRONTEND] 🎯 Event listeners setup completed on capture layer');

        // 测试事件捕获层
        eventCaptureLayer.addEventListener('mouseover', () => {
            console.log('[FRONTEND] 🎯 Mouse entered event capture layer');
        }, { once: true });

        // 🔧 CRITICAL DEBUG: 添加更多事件测试
        eventCaptureLayer.addEventListener('mouseenter', () => {
            console.log('[FRONTEND] 🔧 MOUSE ENTER on event capture layer');
        });

        eventCaptureLayer.addEventListener('mouseleave', () => {
            console.log('[FRONTEND] 🔧 MOUSE LEAVE on event capture layer');
        });

        // 🔧 MID PRIORITY FIX: 移除重复的点击监听器，避免重复日志
        if (DEBUG_MODE) {
            eventCaptureLayer.addEventListener('click', (e) => {
                console.log('[FRONTEND] 🔧 DIRECT CLICK on event capture layer at:', e.clientX, e.clientY);
                console.log('[FRONTEND] 🔧 Event target:', e.target);
                console.log('[FRONTEND] 🔧 Current target:', e.currentTarget);
            }, { capture: true });
        }

        // 🔧 MID PRIORITY FIX: 条件化所有鼠标事件调试日志
        if (DEBUG_MODE) {
            ['mousedown', 'mouseup', 'mousemove'].forEach(eventType => {
                eventCaptureLayer.addEventListener(eventType, (e) => {
                    // 在编辑模式下禁用mousemove日志
                    const shouldLog = eventType !== 'mousemove' ||
                                     (Date.now() - lastLogTime > 1000 && appStateManager.currentState !== AppState.EDITING);

                    if (shouldLog) {
                        console.log(`[FRONTEND] 🔧 ${eventType.toUpperCase()} on event capture layer at:`, e.clientX, e.clientY);
                        if (eventType !== 'mousemove') lastLogTime = Date.now();
                    }
                }, { capture: true });
            });
        }

        // 监听模式切换事件
        if (window.__TAURI__) {
            window.__TAURI__.event.listen('mode-switch', (event) => {
                console.log('[FRONTEND] 🎯 Mode switch event received:', event.payload);
                handleModeSwitch(event.payload);
            });
        }

        // 防止右键菜单
        document.addEventListener('contextmenu', (e) => e.preventDefault());

        console.log('[FRONTEND] Event listeners setup completed');

        // 改进的初始化逻辑
        console.log('[FRONTEND] 🔍 Checking overlay initialization state...');
        console.log('[FRONTEND] 🔍 document.hidden:', document.hidden);
        console.log('[FRONTEND] 🔍 document.hasFocus():', document.hasFocus());
        console.log('[FRONTEND] 🔍 document.visibilityState:', document.visibilityState);

        // 🔧 DEBUG: 状态检查（仅开发模式显示）
        updateDebugInfo('STATE CHECK: hidden=' + document.hidden + ' vis=' + document.visibilityState, 'cyan');

        // 检查是否为预初始化状态
        if (document.hidden || document.visibilityState === 'hidden') {
            console.log('[FRONTEND] 🔧 Overlay in pre-initialized/hidden state, setting up activation listeners');
            updateDebugInfo('HIDDEN STATE - Setting up listeners', 'yellow');

            // 监听窗口显示事件
            document.addEventListener('visibilitychange', function() {
                console.log('[FRONTEND] 🎯 Visibility changed - hidden:', document.hidden, 'state:', document.visibilityState);
                if (!document.hidden && document.visibilityState === 'visible') {
                    console.log('[FRONTEND] 🎯 Overlay activated via visibility change, starting initialization...');
                    initializeOverlay();
                }
            });

            // 监听窗口焦点事件
            window.addEventListener('focus', function() {
                console.log('[FRONTEND] 🎯 Overlay focused, ensuring initialization...');
                initializeOverlay();
            });

            // 监听窗口显示事件（备用）
            window.addEventListener('load', function() {
                console.log('[FRONTEND] 🎯 Window load event, checking if should initialize...');
                if (!document.hidden) {
                    initializeOverlay();
                }
            });

            // 定期检查窗口状态（防止事件丢失）
            const checkInterval = setInterval(() => {
                if (!document.hidden && document.visibilityState === 'visible') {
                    console.log('[FRONTEND] 🎯 Periodic check detected visible state, initializing...');
                    clearInterval(checkInterval);
                    initializeOverlay();
                }
            }, 500);

            // 5秒后清除检查间隔（防止无限检查）
            setTimeout(() => {
                clearInterval(checkInterval);
                console.log('[FRONTEND] 🔧 Periodic visibility check timeout');
            }, 5000);

        } else {
            // 立即初始化
            console.log('[FRONTEND] 🎯 Overlay visible, starting immediate initialization...');
            updateDebugInfo('VISIBLE STATE - Starting immediate init', 'lime');
            initializeOverlay();
        }



        async function loadWindows() {
            try {
                if (window.__TAURI__) {
                    const startTime = performance.now();
                    windows = await window.__TAURI__.core.invoke('list_windows_new');
                    const endTime = performance.now();

                    log.info('Loaded windows: ' + windows.length);
                }
            } catch (error) {
                log.error('Failed to load windows', error);
                windows = [];
            }
        }

        async function checkSmartDetectionAPI() {
            log.info('🔍 Checking smart detection API availability...');
            try {
                if (!window.__TAURI__) {
                    console.error('[FRONTEND] ❌ Tauri API not available');
                    return;
                }

                // 测试智能检测API
                log.debug('🔍 Testing detect_window_smart API...');
                const testResult = await window.__TAURI__.core.invoke('modules::hybrid_screenshot::detect_window_smart', {
                    x: 100,
                    y: 100
                });
                log.info('✅ Smart detection API test successful', testResult);
            } catch (error) {
                log.error('❌ Smart detection API test failed', error);
                log.error('❌ Error details: ' + (error.message || error));

                // 测试降级API
                try {
                    log.debug('🔍 Testing fallback API...');
                    const fallbackResult = await window.__TAURI__.core.invoke('modules::window::detect_window_under_mouse_realtime', {
                        x: 100,
                        y: 100,
                        exclude_overlay_windows: true
                    });
                    log.info('✅ Fallback API test successful', fallbackResult);
                } catch (fallbackError) {
                    log.error('❌ Fallback API also failed', fallbackError);
                }
            }
        }

        // 节流控制
        let lastDetectionTime = 0;
        let lastLogTime = 0;
        let pendingDetection = null;
        const DETECTION_THROTTLE = 100; // 增加到100ms节流，提高性能
        const LOG_THROTTLE = 1000; // 增加到1000ms日志节流，减少日志噪音

        async function handleMouseMove(event) {
            const now = Date.now();
            const x = event.clientX;
            const y = event.clientY;

            // 🔧 ENHANCED LOGGING: Log mouse movement entry
            if (DEBUG_MODE && now - lastLogTime > LOG_THROTTLE) {
                log.trace(`🖱️ [MOUSE_MOVE] Entry - Position: (${x}, ${y}), isDragging: ${isDragging}, detectionEnabled: ${detectionEnabled}, currentState: ${appStateManager.currentState}`);
            }

            // 🔧 完善拖拽逻辑：在拖拽时实时更新区域选择UI
            if (isDragging) {
                // 切换到区域选择状态（如果还没有切换）
                if (appStateManager.currentState === AppState.DETECTING) {
                    appStateManager.setState(AppState.REGION_SELECTING);
                    log.info('🎯 [MOUSE_MOVE] Switched to region selecting mode');
                }

                // 实时更新区域选择UI
                updateRegionSelector(dragStartX, dragStartY, x, y);

                // 节流日志输出
                if (now - lastLogTime > LOG_THROTTLE) {
                    log.debug(`🖱️ [MOUSE_MOVE] Region selecting: ${dragStartX},${dragStartY} → ${x},${y}`);
                    lastLogTime = now;
                }

                // 拖拽时跳过窗口检测
                log.trace('🖱️ [MOUSE_MOVE] Skipping window detection - dragging in progress');
                return;
            }

            // 改进的节流机制 - 使用防抖而不是简单节流
            if (pendingDetection) {
                clearTimeout(pendingDetection);
                log.trace('🖱️ [MOUSE_MOVE] Cleared pending detection timeout');
            }

            if (now - lastDetectionTime < DETECTION_THROTTLE) {
                // 设置延迟检测，确保最后一次鼠标移动会被处理
                log.trace(`🖱️ [MOUSE_MOVE] Throttling detection - setting delayed detection for (${x}, ${y})`);
                pendingDetection = setTimeout(async () => {
                    try {
                        log.trace(`🖱️ [MOUSE_MOVE] Executing delayed window detection for (${x}, ${y})`);
                        await performWindowDetection(x, y);
                    } catch (error) {
                        log.error('[MOUSE_MOVE] ❌ Delayed window detection failed:', error);
                    }
                }, DETECTION_THROTTLE);
                return;
            }

            lastDetectionTime = now;
            pendingDetection = null;
            log.trace(`🖱️ [MOUSE_MOVE] Proceeding with immediate window detection for (${x}, ${y})`);

            // 🔧 优化：只在调试模式下记录鼠标移动，避免日志噪音
            if (DEBUG_MODE && now - lastLogTime > LOG_THROTTLE && appStateManager.currentState !== AppState.EDITING) {
                logWithLevel(LOG_LEVELS.TRACE, `🖱️ Mouse move detected at: ${x}, ${y}`);
                logWithLevel(LOG_LEVELS.TRACE, `🖱️ Detection enabled: ${detectionEnabled}, isDragging: ${isDragging}`);
                lastLogTime = now;
            }

            // 🔧 CRITICAL FIX: 在特定状态下禁用窗口检测，防止干扰现有区域
            const currentState = appStateManager.currentState;
            const statesWithExistingRegion = [
                AppState.REGION_PREVIEW,
                AppState.REGION_EDITING,
                AppState.REGION_ANNOTATION,
                AppState.CAPTURING,
                AppState.EDITING,
                AppState.SUCCESS
            ];

            if (statesWithExistingRegion.includes(currentState)) {
                // 在这些状态下，完全禁用窗口检测以保护现有区域
                log.trace(`⚠️ [MOUSE_MOVE] Window detection disabled in state: ${currentState}`);
                return;
            }

            if (!detectionEnabled && !isDragging) {
                // 使用TRACE级别记录检测禁用信息
                if (appStateManager.currentState !== AppState.EDITING) {
                    log.trace('⚠️ [MOUSE_MOVE] Detection disabled, ignoring mouse move');
                }
                return;
            }

            // 🔧 ENHANCED LOGGING: Log detection attempt
            log.trace(`🖱️ [MOUSE_MOVE] Proceeding to window detection - state: ${currentState}, detectionEnabled: ${detectionEnabled}`);

            // 🔧 CRITICAL FIX: 只在检测状态下显示和更新十字线
            if (currentState === AppState.DETECTING || currentState === AppState.REGION_SELECTING) {
                // 更新十字线位置
                if (crosshair) {
                    crosshair.style.left = x + 'px';
                    crosshair.style.top = y + 'px';
                    crosshair.style.display = 'block';
                }
            } else {
                // 在其他状态下隐藏十字线
                if (crosshair) {
                    crosshair.style.display = 'none';
                }
            }

            // 在拖拽过程中跳过窗口检测
            if (isDragging) {
                if (DEBUG_MODE && now - lastLogTime > LOG_THROTTLE) {
                    logWithLevel(LOG_LEVELS.DEBUG, '🖱️ Skipping window detection during drag');
                }
                return;
            }

            // 🔧 CRITICAL FIX: 确保窗口检测始终被调用
            // 立即执行窗口检测
            try {
                // 🔧 优化：只在调试模式下记录检测调用，避免日志噪音
                if (DEBUG_MODE) {
                    logWithLevel(LOG_LEVELS.TRACE, '🔍 Calling performWindowDetection', {x, y});
                }
                await performWindowDetection(x, y);
            } catch (error) {
                logWithLevel(LOG_LEVELS.ERROR, `❌ Window detection failed: ${error}`);
                hideHighlight();
            }
        }

        // 本地检测函数已移除，现在使用后端API进行实时检测

        function handleModeSwitch(payload) {
            console.log('[FRONTEND] 🎯 Handling mode switch:', payload);

            if (payload.to === 'region_selection') {
                console.log('[FRONTEND] 🎯 Switching to region selection mode');

                // 隐藏窗口检测相关的UI
                hideHighlight();
                document.querySelector('.instructions h3').textContent = '📐 Region Selection';
                document.querySelector('.instructions p').innerHTML = 'Click and drag to select area<br><strong>ESC</strong> to exit';
                // 🔧 PRODUCTION: mode-indicator已移除

                // 禁用窗口检测
                detectionEnabled = false;

                // 启用区域选择功能
                enableRegionSelection();

                console.log('[FRONTEND] 🎯 Mode switch to region selection completed');
            }
        }

        function enableRegionSelection() {
            console.log('[FRONTEND] 🎯 Enabling region selection functionality');

            // 这里可以添加区域选择的具体实现
        }

        function getAppIcon(appName) {
            const iconMap = {
                'chrome': '🌐',
                'safari': '🧭',
                'firefox': '🦊',
                'edge': '🌊',
                'finder': '📁',
                'terminal': '⚡',
                'code': '💻',
                'vscode': '💻',
                'xcode': '🔨',
                'photoshop': '🎨',
                'illustrator': '✏️',
                'sketch': '📐',
                'figma': '🎯',
                'slack': '💬',
                'discord': '🎮',
                'zoom': '📹',
                'teams': '👥',
                'mail': '📧',
                'calendar': '📅',
                'notes': '📝',
                'music': '🎵',
                'spotify': '🎶',
                'vlc': '🎬',
                'quicktime': '🎥',
                'preview': '👁️',
                'calculator': '🧮',
                'system preferences': '⚙️',
                'activity monitor': '📊',
                'console': '🖥️',
                'simulator': '📱',
                'docker': '🐳',
                'postman': '📮',
                'git': '🌿',
                'github': '🐙',
                'notion': '📋',
                'obsidian': '🔮',
                'bear': '🐻',
                'typora': '📄'
            };

            const lowerAppName = appName.toLowerCase();

            // 精确匹配
            if (iconMap[lowerAppName]) {
                return iconMap[lowerAppName];
            }

            // 模糊匹配
            for (const [key, icon] of Object.entries(iconMap)) {
                if (lowerAppName.includes(key) || key.includes(lowerAppName)) {
                    return icon;
                }
            }

            // 默认图标
            return '🪟';
        }

        function highlightWindow(window) {
            log.info(`🎯 [HIGHLIGHT] === STARTING WINDOW HIGHLIGHT ===`);
            log.info(`🎯 [HIGHLIGHT] Window ID: ${window.id}`);
            log.info(`🎯 [HIGHLIGHT] Window title: "${window.title}"`);
            log.info(`🎯 [HIGHLIGHT] Window bounds: (${window.x}, ${window.y}) ${window.width}x${window.height}`);

            currentWindowId = window.id;

            // 设置高亮区域
            log.info(`🎯 [HIGHLIGHT] Setting highlight element styles...`);
            windowHighlight.style.left = window.x + 'px';
            windowHighlight.style.top = window.y + 'px';
            windowHighlight.style.width = window.width + 'px';
            windowHighlight.style.height = window.height + 'px';
            windowHighlight.style.display = 'block';
            log.info(`🎯 [HIGHLIGHT] Highlight element styles applied - display: ${windowHighlight.style.display}`);

            // 更新窗口信息
            const titleElement = windowInfo.querySelector('.title');
            const appNameElement = windowInfo.querySelector('.app-name');
            const dimensionsElement = windowInfo.querySelector('.dimensions');
            const appIconElement = windowInfo.querySelector('.app-icon');

            titleElement.textContent = window.title || 'Untitled Window';
            appNameElement.textContent = window.app_name || 'Unknown App';
            dimensionsElement.textContent = `${window.width} × ${window.height} at (${window.x}, ${window.y})`;

            // 设置应用图标（基于应用名称）
            appIconElement.textContent = getAppIcon(window.app_name || '');

            // 智能定位信息面板 - 避免超出屏幕边界
            const screenWidth = window.innerWidth || screen.width;
            const screenHeight = window.innerHeight || screen.height;
            const infoWidth = 350; // 信息卡片最大宽度
            const infoHeight = 120; // 信息卡片估计高度
            const margin = 15;

            let infoX, infoY;

            // 优先在窗口右上角显示
            if (window.x + window.width + margin + infoWidth <= screenWidth) {
                // 右侧有足够空间
                infoX = window.x + window.width + margin;
            } else if (window.x - margin - infoWidth >= 0) {
                // 左侧有足够空间
                infoX = window.x - margin - infoWidth;
            } else {
                // 两侧都没有足够空间，放在屏幕右边缘
                infoX = screenWidth - infoWidth - margin;
            }

            // 垂直位置：优先在窗口顶部对齐
            if (window.y + infoHeight <= screenHeight) {
                infoY = window.y;
            } else if (window.y + window.height - infoHeight >= 0) {
                // 在窗口底部对齐
                infoY = window.y + window.height - infoHeight;
            } else {
                // 放在屏幕顶部
                infoY = margin;
            }

            // 确保不超出边界
            infoX = Math.max(margin, Math.min(infoX, screenWidth - infoWidth - margin));
            infoY = Math.max(margin, Math.min(infoY, screenHeight - infoHeight - margin));

            windowInfo.style.left = infoX + 'px';
            windowInfo.style.top = infoY + 'px';
            windowInfo.style.display = 'block';

            log.info(`🎯 [HIGHLIGHT] Window info panel positioned at (${infoX}, ${infoY})`);
            log.info(`🎯 [HIGHLIGHT] === WINDOW HIGHLIGHT COMPLETED ===`);
        }

        function hideHighlight() {
            log.info(`🎯 [HIGHLIGHT] === HIDING WINDOW HIGHLIGHT ===`);
            log.info(`🎯 [HIGHLIGHT] Previous window ID: ${currentWindowId}`);

            currentWindowId = null;
            windowHighlight.style.display = 'none';
            windowInfo.style.display = 'none';

            log.info(`🎯 [HIGHLIGHT] Highlight elements hidden - windowHighlight.display: ${windowHighlight.style.display}, windowInfo.display: ${windowInfo.style.display}`);
            log.info(`🎯 [HIGHLIGHT] === HIDE HIGHLIGHT COMPLETED ===`);
        }



        // 🔧 节流控制：避免疯狂打印日志
        let lastClickWarningTime = 0;
        const CLICK_WARNING_THROTTLE = 2000; // 2秒内最多打印一次警告

        function handleClick(event) {
            const now = Date.now();

            // 🔧 优化：只在调试模式下记录详细的点击信息
            if (DEBUG_MODE) {
                logToBackend('DEBUG', `🖱️ Click event detected at: ${event.clientX}, ${event.clientY}`);
                logToBackend('DEBUG', `🖱️ Current state: ${appStateManager.currentState}`);
            }

            // 🔧 CRITICAL FIX: 直接处理窗口选择，不依赖复杂的事件链
            if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                logToBackend('INFO', '🖱️ Handling window selection from click event');
                event.preventDefault(); // 防止其他处理
                event.stopPropagation(); // 防止事件冒泡
                handleWindowSelection();
                return;
            } else {
                // 🔧 节流控制：避免疯狂打印警告日志
                if (now - lastClickWarningTime > CLICK_WARNING_THROTTLE) {
                    logToBackend('DEBUG', 'Click ignored: Not in DETECTING state or no window selected.');
                    logToBackend('DEBUG', `  - State: ${appStateManager.currentState}`);
                    if (DEBUG_MODE) {
                        logToBackend('DEBUG', `  - Window:`, appStateManager.selectedWindow);
                    }
                    lastClickWarningTime = now;
                }
            }
        }

        // 防止重复调用的标志
        let isCapturingWindow = false;

        // 处理窗口选择
        async function handleWindowSelection() {
            log.info('🎯 Handling window selection');

            // 🔧 CRITICAL FIX: 防止重复调用
            if (isCapturingWindow) {
                log.warn('🎯 Window capture already in progress, ignoring duplicate call');
                return;
            }

            // 关键修复：在函数开始时立即捕获选定的窗口信息
            const windowToCapture = appStateManager.selectedWindow;

            if (!windowToCapture) {
                log.warn('🎯 No window to select');
                return;
            }
            log.debug('🎯 Selected window details', windowToCapture);

            // 设置捕获标志
            isCapturingWindow = true;

            try {
                log.info('🎯 Setting state to CAPTURING');
                appStateManager.setState(AppState.CAPTURING);

                log.info('🎯 Calling captureSelectedWindow with coordinates: ' + lastClickX + ', ' + lastClickY);
                const screenshotResult = await captureSelectedWindow(windowToCapture, lastClickX, lastClickY);

                log.debug('🎯 Screenshot received');

                if (screenshotResult && screenshotResult.success) {
                    log.info('🎯 Screenshot captured successfully');

                    const region = {
                        x: windowToCapture.x,
                        y: windowToCapture.y,
                        width: windowToCapture.width,
                        height: windowToCapture.height
                    };

                    // 🔧 FIX: 截图成功，显示成功消息并进行清理
                    log.info('🎯 ✅ Screenshot captured successfully!');
                    log.info('🎯 Screenshot details:', {
                        success: screenshotResult.success,
                        path: screenshotResult.path,
                        width: screenshotResult.width,
                        height: screenshotResult.height,
                        capture_time_ms: screenshotResult.capture_time_ms
                    });

                    // 设置成功状态
                    appStateManager.setState(AppState.SUCCESS);

                    // 截图完成，后端已处理所有清理工作（包括关闭覆盖层）
                    log.info('🎯 Screenshot workflow completed - backend handles cleanup and preview creation');
                } else {
                    log.error('🎯 Screenshot capture failed', screenshotResult);
                    appStateManager.setState(AppState.ERROR);
                }
            } catch (error) {
                log.error('🎯 Error during window selection', error);
                log.error('🎯 Error type: ' + typeof error);
                log.error('🎯 Error JSON: ' + JSON.stringify(error));
                appStateManager.setState(AppState.ERROR);
            } finally {
                // 🔧 CRITICAL FIX: 重置捕获标志，允许后续操作
                isCapturingWindow = false;
                log.debug('🎯 Window capture flag reset');
            }
        }

        // 🔧 完善区域截图功能
        async function captureSelectedRegion(region) {
            log.info('🔍 Starting region capture', region);

            // 切换到截图状态
            appStateManager.setState(AppState.CAPTURING);

            try {
                // 检查Tauri API是否可用
                if (!window.__TAURI__ || !window.__TAURI__.core || !window.__TAURI__.core.invoke) {
                    throw new Error('Tauri API not available');
                }

                // 验证区域参数
                if (!region || region.width < 1 || region.height < 1) {
                    throw new Error('Invalid region parameters');
                }

                // 🔧 CRITICAL FIX: 使用capture_region_from_overlay以获得预览窗口和独立工具栏
                const regionData = {
                    x: parseFloat(region.x),
                    y: parseFloat(region.y),
                    width: parseFloat(region.width),
                    height: parseFloat(region.height),
                    monitor_index: 0 // 默认使用主显示器
                };

                logToBackend('INFO', '🔍 About to call capture_region_from_overlay for annotation support', regionData);

                const result = await window.__TAURI__.core.invoke('capture_region_from_overlay', {
                    region: regionData
                });

                logToBackend('INFO', '🔍 Region screenshot result received');

                if (result && result.success) {
                    // 切换到成功状态
                    appStateManager.setState(AppState.SUCCESS);

                    log.info('🔍 ✅ Region screenshot captured successfully with annotation support');
                    log.info('🔍 Screenshot details:', {
                        path: result.path,
                        width: result.width,
                        height: result.height,
                        region: region
                    });

                    // 🔧 CRITICAL FIX: 移除重复的预览窗口创建，capture_region_from_overlay已经处理
                    // capture_region_from_overlay会自动创建预览窗口和独立工具栏

                    // 发送截图保存事件
                    if (result.path && window.__TAURI__) {
                        try {
                            await window.__TAURI__.core.invoke('emit_screenshot_saved', {
                                path: result.path,
                                name: `Region Screenshot ${new Date().toLocaleTimeString()}`
                            });
                            log.info('🔍 ✅ Screenshot saved event emitted');
                        } catch (eventError) {
                            log.warn('🔍 ⚠️ Failed to emit screenshot saved event:', eventError);
                        }
                    }

                    // 🔧 CRITICAL FIX: 不在前端清理UI，让后端统一处理overlay清理
                    // hideRegionSelector(); // 移除，避免与后端清理逻辑冲突
                    // completeScreenshotCapture(); // 移除，后端会处理完整的清理流程

                    log.info('🔍 ✅ Region capture completed, backend handling overlay cleanup and preview creation');
                } else {
                    console.error('[FRONTEND] 🔍 Region capture failed:', result);
                    appStateManager.setState(AppState.ERROR);
                }

                return result;
            } catch (error) {
                log.error('🔍 Failed to capture region', error);
                log.error('🔍 Error details: ' + error.message + ' | ' + error.stack);
                logToBackend('ERROR', '🔍 Failed to capture region', error.toString());
                logToBackend('ERROR', '🔍 Error details', `${error.message} | ${error.stack}`);

                // 根据错误类型提供不同的处理
                let errorMessage = '截图失败，请重试';
                if (error.message.includes('permission')) {
                    errorMessage = '权限不足，请检查屏幕录制权限';
                } else if (error.message.includes('network') || error.message.includes('timeout')) {
                    errorMessage = '网络错误，请检查连接';
                } else if (error.message.includes('Invalid region')) {
                    errorMessage = '选择区域无效，请重新选择';
                } else if (error.message.includes('Tauri API not available')) {
                    errorMessage = '系统错误，请重启应用';
                }

                // 显示用户友好的错误信息
                const instructions = document.querySelector('.instructions');
                if (instructions) {
                    instructions.innerHTML = `<h3 style="color: red;">❌ ${errorMessage}</h3><p>错误详情: ${error.message}</p><p><strong>ESC</strong> 返回 | <strong>R</strong> 重试</p>`;
                }

                // 设置错误状态
                appStateManager.setState(AppState.ERROR);

                // 清理UI
                hideRegionSelector();

                // 自动恢复机制
                setTimeout(() => {
                    if (appStateManager.currentState === AppState.ERROR) {
                        log.info('🔍 Auto-recovering from error state');
                        appStateManager.setState(AppState.DETECTING);
                    }
                }, 3000);

                throw error;
            }
        }

        // 🗑️ REMOVED: Obsolete toolbar bounds tracking functions
        // These were part of the complex click-through detection approach
        // Now replaced by the elegant solution: close and recreate toolbar lifecycle

        // 🔒 ATOMIC TOOLBAR MANAGEMENT: 防止竞争条件的原子工具栏管理

        // 工具栏操作锁，防止并发操作
        let toolbarOperationLock = false;
        let pendingToolbarOperations = [];

        // 原子工具栏操作队列处理器
        async function processToolbarOperation(operation) {
            return new Promise((resolve, reject) => {
                pendingToolbarOperations.push({ operation, resolve, reject });
                processNextToolbarOperation();
            });
        }

        // 处理下一个工具栏操作
        async function processNextToolbarOperation() {
            if (toolbarOperationLock || pendingToolbarOperations.length === 0) {
                return;
            }

            toolbarOperationLock = true;
            const { operation, resolve, reject } = pendingToolbarOperations.shift();

            try {
                const result = await operation();
                resolve(result);
            } catch (error) {
                reject(error);
            } finally {
                toolbarOperationLock = false;
                // 处理队列中的下一个操作
                setTimeout(processNextToolbarOperation, 10);
            }
        }

        // 🔧 CRITICAL FIX: Force close toolbar for drag operations
        async function forceCloseToolbarForDrag() {
            log.info(`🔒 [DRAG] Force closing toolbar to prevent z-order conflicts`);

            try {
                // 强制关闭所有活动工具栏，不依赖于 currentRegionToolbarId
                await window.__TAURI__.core.invoke('close_all_active_toolbars');
                window.currentRegionToolbarId = null;
                log.info(`✅ [DRAG] All toolbars force closed for drag operation`);
            } catch (error) {
                log.error(`❌ [DRAG] Failed to force close toolbars for drag:`, error);
                // 即使失败也要清理状态，避免后续问题
                window.currentRegionToolbarId = null;
            }
        }

        // 🔒 原子工具栏关闭操作
        async function closeToolbarBeforeRegionChange(operationType) {
            return processToolbarOperation(async () => {
                if (!window.currentRegionToolbarId) {
                    log.debug(`🛠️ No toolbar to close before ${operationType}`);
                    return;
                }

                const toolbarToClose = window.currentRegionToolbarId;
                log.info(`🔒 [ATOMIC] Closing toolbar before ${operationType}:`, toolbarToClose);

                try {
                    await window.__TAURI__.core.invoke('close_toolbar_window', {
                        toolbarId: toolbarToClose
                    });

                    // 确保工具栏ID被清理
                    if (window.currentRegionToolbarId === toolbarToClose) {
                        window.currentRegionToolbarId = null;
                    }

                    log.info(`✅ [ATOMIC] Toolbar closed successfully before ${operationType}`);
                } catch (error) {
                    log.error(`❌ [ATOMIC] Failed to close toolbar before ${operationType}:`, error);

                    // 原子回退：尝试关闭所有活动工具栏
                    try {
                        await window.__TAURI__.core.invoke('close_all_active_toolbars');
                        window.currentRegionToolbarId = null;
                        log.info(`✅ [ATOMIC] All toolbars closed via fallback before ${operationType}`);
                    } catch (fallbackError) {
                        log.error(`❌ [ATOMIC] Fallback toolbar closure failed before ${operationType}:`, fallbackError);
                        throw fallbackError;
                    }
                }
            });
        }

        // 🔒 原子工具栏创建操作
        async function recreateToolbarAfterRegionChange(operationType) {
            return processToolbarOperation(async () => {
                if (!appStateManager.selectedRegion) {
                    log.warn(`🛠️ [ATOMIC] Cannot recreate toolbar after ${operationType} - no selected region`);
                    return;
                }

                // 确保没有现有工具栏
                if (window.currentRegionToolbarId) {
                    log.warn(`🛠️ [ATOMIC] Existing toolbar detected during recreation, closing first`);
                    await closeToolbarBeforeRegionChange(`${operationType}-cleanup`);
                }

                log.info(`🔒 [ATOMIC] Recreating toolbar after ${operationType}`);

                try {
                    const newToolbarId = await createIndependentToolbarForRegion();
                    log.info(`✅ [ATOMIC] Toolbar recreated successfully after ${operationType}:`, newToolbarId);
                    return newToolbarId;
                } catch (error) {
                    log.error(`❌ [ATOMIC] Failed to recreate toolbar after ${operationType}:`, error);
                    throw error;
                }
            });
        }

        // 🔒 原子工具栏替换操作（用于快速连续操作）
        async function atomicToolbarReplace(operationType) {
            return processToolbarOperation(async () => {
                log.info(`🔒 [ATOMIC] Starting atomic toolbar replace for ${operationType}`);

                // 步骤1：关闭现有工具栏
                if (window.currentRegionToolbarId) {
                    const toolbarToClose = window.currentRegionToolbarId;
                    try {
                        await window.__TAURI__.core.invoke('close_toolbar_window', {
                            toolbarId: toolbarToClose
                        });

                        if (window.currentRegionToolbarId === toolbarToClose) {
                            window.currentRegionToolbarId = null;
                        }

                        log.info(`✅ [ATOMIC] Old toolbar closed in replace operation`);
                    } catch (error) {
                        log.error(`❌ [ATOMIC] Failed to close old toolbar in replace:`, error);
                        // 继续创建新工具栏，即使关闭失败
                    }
                }

                // 步骤2：创建新工具栏
                if (appStateManager.selectedRegion) {
                    try {
                        const newToolbarId = await createIndependentToolbarForRegion();
                        log.info(`✅ [ATOMIC] New toolbar created in replace operation:`, newToolbarId);
                        return newToolbarId;
                    } catch (error) {
                        log.error(`❌ [ATOMIC] Failed to create new toolbar in replace:`, error);
                        throw error;
                    }
                } else {
                    log.warn(`🛠️ [ATOMIC] No selected region for toolbar replace`);
                }
            });
        }

        // 🔒 ATOMIC TOOLBAR: 快速连续操作的防抖处理
        let rapidOperationTimeout = null;
        let lastOperationType = null;

        // 🔒 智能工具栏管理：处理快速连续的区域变更
        async function handleRapidRegionChange(operationType) {
            // 清除之前的防抖定时器
            if (rapidOperationTimeout) {
                clearTimeout(rapidOperationTimeout);
                rapidOperationTimeout = null;
            }

            // 🔧 CRITICAL FIX: Always handle drag operations independently
            if (operationType === 'drag') {
                log.info(`🔒 [DRAG] Processing drag operation (independent of previous operations)`);
                await forceCloseToolbarForDrag();
                // 不设置 lastOperationType，让每次拖拽都独立处理
                log.info(`🔒 [DRAG] Toolbar closed, drag operation ready`);
                return;
            }

            // 如果是相同类型的快速连续操作，使用原子替换
            if (lastOperationType === operationType) {
                log.info(`🔒 [RAPID] Detected rapid ${operationType} operations, using atomic replace`);

                rapidOperationTimeout = setTimeout(async () => {
                    try {
                        await atomicToolbarReplace(`rapid-${operationType}`);
                        lastOperationType = null;
                    } catch (error) {
                        log.error(`❌ [RAPID] Atomic replace failed for ${operationType}:`, error);
                    }
                }, 150); // 150ms 防抖延迟

                return;
            }

            // 记录操作类型
            lastOperationType = operationType;

            // 正常的关闭操作
            await closeToolbarBeforeRegionChange(operationType);
        }

        // 🔒 完成快速操作后的工具栏重建
        async function completeRapidRegionChange(operationType) {
            // 🔧 CRITICAL FIX: Handle drag completion specially with immediate recreation
            if (operationType === 'drag') {
                log.info(`🔒 [DRAG] Completing drag operation, recreating toolbar immediately`);
                try {
                    // 立即重建工具栏，不使用延迟
                    await recreateToolbarAfterRegionChange('drag-complete');
                    // 重要：立即清理状态，避免后续拖拽被误识别为快速操作
                    lastOperationType = null;
                    log.info(`✅ [DRAG] Toolbar recreated and state cleared after drag`);
                } catch (error) {
                    log.error(`❌ [DRAG] Failed to recreate toolbar after drag:`, error);
                    // 即使失败也要清理状态
                    lastOperationType = null;
                }
                return;
            }

            // 如果有防抖定时器，说明是快速操作，不需要额外处理
            if (rapidOperationTimeout) {
                log.debug(`🔒 [RAPID] ${operationType} completed, atomic replace will handle toolbar`);
                return;
            }

            // 正常的重建操作
            await recreateToolbarAfterRegionChange(operationType);
            lastOperationType = null;
        }

        // 防止重复后端调用的标志
        let isBackendCapturing = false;

        // 🔧 BUG FIX: 基于坐标的实时窗口截图
        async function captureSelectedWindow(windowInfo, clickX, clickY) {
            log.info('🎯 Capturing window at coordinates: ' + clickX + ', ' + clickY);

            // 🔧 CRITICAL FIX: 防止重复后端调用
            if (isBackendCapturing) {
                log.warn('🎯 Backend capture already in progress, ignoring duplicate call');
                return { success: false, message: 'Capture already in progress' };
            }

            isBackendCapturing = true;

            try {
                // 检查Tauri API是否可用
                if (!window.__TAURI__ || !window.__TAURI__.core || !window.__TAURI__.core.invoke) {
                    throw new Error('Tauri API not available');
                }

                // 🔧 BUG FIX: 使用坐标检测策略，避免ID映射问题
                const coordinateRequest = {
                    x: Math.round(clickX),
                    y: Math.round(clickY)
                };

                log.info('🎯 About to call capture_window_at_coordinates', coordinateRequest);

                // 🚀 BUG FIX: 添加超时处理和进度指示
                const capturePromise = window.__TAURI__.core.invoke('capture_window_at_coordinates', {
                    x: coordinateRequest.x,
                    y: coordinateRequest.y
                });

                // 设置15秒超时（考虑到图像处理优化可能需要更多时间）
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error('Screenshot capture timeout after 15 seconds'));
                    }, 15000);
                });

                // 添加进度指示
                const progressIndicator = setTimeout(() => {
                    log.info('🎯 Screenshot processing in progress... (large images may take longer)');
                }, 3000);

                try {
                    const result = await Promise.race([capturePromise, timeoutPromise]);
                    clearTimeout(progressIndicator);
                    log.info('🎯 Screenshot result received');
                    return result;
                } catch (error) {
                    clearTimeout(progressIndicator);
                    throw error;
                }
                return result;
            } catch (error) {
                log.error('🎯 Failed to capture window', error);
                log.error('🎯 Error details: ' + error.message + ' | ' + error.stack);
                logToBackend('ERROR', '🎯 Failed to capture window', error.toString());
                logToBackend('ERROR', '🎯 Error details', `${error.message} | ${error.stack}`);
                throw error;
            } finally {
                // 🔧 CRITICAL FIX: 重置后端捕获标志
                isBackendCapturing = false;
                log.debug('🎯 Backend capture flag reset');
            }
        }

        // 在编辑模式中显示截图
        function displayScreenshotInEditingMode(screenshotResult, region) {
            log.info('🎯 Displaying screenshot in editing mode');

            const screenshotContainer = document.getElementById('screenshotContainer');
            const screenshotImage = document.getElementById('screenshotImage');

            if (screenshotResult.base64) {
                // 设置截图图片
                // 🚀 PERFORMANCE: 支持JPEG格式，提升加载速度
                screenshotImage.src = `data:image/jpeg;base64,${screenshotResult.base64}`;

                // 设置容器位置和大小
                screenshotContainer.style.left = `${region.x}px`;
                screenshotContainer.style.top = `${region.y}px`;
                screenshotContainer.style.width = `${region.width}px`;
                screenshotContainer.style.height = `${region.height}px`;

                log.debug('🎯 Screenshot displayed at', region);

                // 🔧 REACT INTEGRATION: 禁用原生工具栏，使用React组件
                log.info('🎯 Skipping native toolbar - React component will handle UI');
                // showQuickActionToolbar(); // 已禁用：使用React ScreenshotResultOverlay

                // 🔧 BUG FIX: 添加编辑模式样式指示器
                screenshotContainer.classList.add('editing-mode');

                // 🔧 BUG FIX: 显示编辑模式提示信息
                showEditingModeInstructions();

                // 将窗口坐标传递给编辑组件（通过后端命令）
                if (window.__TAURI__) {
                    // 使用后端命令发送全局事件到主应用
                    window.__TAURI__.core.invoke('send_window_selected_event', {
                        x: region.x,
                        y: region.y,
                        width: region.width,
                        height: region.height,
                        imageData: screenshotResult.base64
                    }).then(() => {
                        log.info('🎯 ✅ Window-selected event sent successfully to main app via backend');
                    }).catch((error) => {
                        log.error('🎯 ❌ Failed to send window-selected event via backend:', error);
                    });
                    log.debug('🎯 Window coordinates sent to editor via backend command', region);
                }

                // 🔧 BUG FIX: 确保鼠标交互已禁用
                log.info('🎯 Ensuring mouse interactions are disabled in editing mode');
                if (window.mouseInteractionsDisabled !== true) {
                    log.warn('🎯 Mouse interactions were not disabled, forcing disable now');
                    appStateManager.disableMouseInteractions();
                }

            } else {
                log.error('🎯 No base64 data in screenshot result');
                // 如果截图失败，回到检测模式
                appStateManager.setState(AppState.DETECTING);
            }
        }

        // 🔧 BUG FIX: 新增编辑模式指导函数
        function showEditingModeInstructions() {
            // 创建或更新编辑模式指导提示
            let instructionOverlay = document.getElementById('editingInstructions');
            if (!instructionOverlay) {
                instructionOverlay = document.createElement('div');
                instructionOverlay.id = 'editingInstructions';
                instructionOverlay.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    z-index: 10000;
                    pointer-events: none;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                `;
                document.body.appendChild(instructionOverlay);
            }

            instructionOverlay.textContent = '🎨 编辑模式已激活 - 请从工具栏选择编辑操作，鼠标拖拽已禁用';
            instructionOverlay.style.display = 'block';

            // 3秒后自动隐藏指导提示
            setTimeout(() => {
                if (instructionOverlay) {
                    instructionOverlay.style.display = 'none';
                }
            }, 3000);

            log.info('🎨 Editing mode instructions displayed');
        }

        function showSelectedWindow() {
            // 创建暗化遮罩
            if (!dimOverlay) {
                dimOverlay = document.createElement('div');
                dimOverlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.6);
                    z-index: 999;
                    pointer-events: none;
                `;
                document.body.appendChild(dimOverlay);
            }
            dimOverlay.style.display = 'block';

            // 修改窗口高亮样式为选中状态
            windowHighlight.style.background = 'rgba(255, 107, 53, 0.1)';
            windowHighlight.style.border = '3px solid #FF6B35';
            windowHighlight.style.zIndex = '1000';
        }



        function exitWindowSelection() {
            log.info('🚪 Exiting window selection mode');





            // 重置窗口高亮样式
            windowHighlight.style.background = 'rgba(255, 107, 53, 0.1)';
            windowHighlight.style.border = '2px solid #FF6B35';

            // 重置状态
            isWindowSelected = false;
            selectedWindowId = null;

            // 重新启用窗口检测
            detectionEnabled = true;
        }

        // 🔧 BUG FIX: 重命名旧函数，避免与新的窗口截图函数冲突
        async function captureSelectedWindowByRegion() {
            log.warn('📸 [DEPRECATED] Using region-based window capture - this function should not be called');

            if (!appStateManager.selectedWindow) {
                console.error('[FRONTEND] 📸 No window selected for capture');
                logToBackend('ERROR', '📸 No window selected for capture');
                return null;
            }

            try {
                // 构建截图区域参数
                const area = {
                    x: appStateManager.selectedWindow.x,
                    y: appStateManager.selectedWindow.y,
                    width: appStateManager.selectedWindow.width,
                    height: appStateManager.selectedWindow.height
                };

                log.warn('🎯 [DEPRECATED] About to call capture_region_new - should use capture_window_new instead', area);

                const result = await window.__TAURI__.core.invoke('capture_region_new', {
                    area: area
                });

                log.info('🎯 Screenshot result received');
                return result;
            } catch (error) {
                log.error('🎯 Failed to capture window', error.toString());
                log.error('🎯 Error details', `${error.message} | ${error.stack}`);
                throw error;
            }
        }

        async function pinSelectedWindow() {
            console.log('[FRONTEND] 📌 Pinning selected window:', selectedWindowId);
            // TODO: 实现固定功能
        }

        async function handleKeyboard(event) {
            log.info('🎹 Key pressed: ' + event.key + ', Current state: ' + appStateManager.currentState);
            console.log('[FRONTEND] 🎹 Key pressed:', event.key, 'State:', appStateManager.currentState);

            // 阻止默认行为，避免干扰
            event.preventDefault();

            switch (event.key.toLowerCase()) {
                case 'escape':
                    log.info('🎹 ESC key detected');
                    await handleEscapeKey();
                    break;

                case 'enter':
                    log.info('🎹 Enter key detected');
                    await handleEnterKey();
                    break;

                case ' ':
                    log.info('🎹 Space key detected');
                    await handleSpaceKey();
                    break;

                case 'r':
                    // 刷新窗口列表（仅在检测模式下）
                    if (appStateManager.currentState === AppState.DETECTING) {
                        loadWindows();
                        log.info('🎹 Window list refreshed');
                    }
                    break;

                case 'd':
                    // 切换检测模式（仅在检测模式下）
                    if (appStateManager.currentState === AppState.DETECTING) {
                        detectionEnabled = !detectionEnabled;
                        if (!detectionEnabled) {
                            hideHighlight();
                        }
                        log.info('🎹 Detection mode toggled: ' + detectionEnabled);
                    }
                    break;
            }
        }

        async function handleEscapeKey() {
            log.info('🚪 ESC key pressed, current state: ' + appStateManager.currentState);

            // 根据当前状态处理ESC键
            switch (appStateManager.currentState) {
                case AppState.REGION_SELECTING:
                    // 取消区域选择，返回检测模式
                    log.info('🚪 Canceling region selection');
                    hideRegionSelector();
                    appStateManager.setState(AppState.DETECTING);
                    break;

                case AppState.REGION_PREVIEW:
                    // 取消区域预览，返回检测模式
                    log.info('🚪 Canceling region preview');
                    hideRegionSelector();
                    appStateManager.setState(AppState.DETECTING);
                    break;

                case AppState.REGION_EDITING:
                    // 从区域编辑返回预览模式
                    log.info('🚪 Exiting region editing mode');
                    appStateManager.setState(AppState.REGION_PREVIEW);
                    break;

                case AppState.REGION_ANNOTATION: // 🆕 标注模式ESC处理
                    // 从标注模式返回预览模式
                    log.info('🚪 Exiting region annotation mode');
                    appStateManager.setState(AppState.REGION_PREVIEW);
                    break;

                case AppState.EDITING:
                    // 从编辑模式返回检测模式
                    log.info('🚪 Exiting editing mode');
                    appStateManager.setState(AppState.DETECTING, {
                        selectedWindow: null,
                        screenshotData: null,
                        editingRegion: null
                    });
                    break;

                case AppState.CAPTURING:
                    // 取消截图操作
                    log.info('🚪 Canceling capture operation');
                    appStateManager.setState(AppState.DETECTING);
                    break;

                case AppState.DETECTING:
                default:
                    // 完全退出应用
                    log.info('🚪 Exiting application');
                    await exitApplication();
                    break;
            }
        }

        async function handleEnterKey() {
            log.info('⏎ Enter key pressed, current state: ' + appStateManager.currentState);

            switch (appStateManager.currentState) {
                case AppState.REGION_PREVIEW:
                    // 在区域预览状态下，Enter键触发截图
                    log.info('⏎ Capturing region from preview state');
                    if (appStateManager.selectedRegion) {
                        await captureSelectedRegion(appStateManager.selectedRegion);
                    }
                    break;

                case AppState.REGION_EDITING:
                    // 在区域编辑状态下，Enter键保存并完成编辑
                    log.info('⏎ Saving region editing');
                    if (appStateManager.selectedRegion) {
                        await captureSelectedRegion(appStateManager.selectedRegion);
                    }
                    break;

                case AppState.DETECTING:
                    // 在检测状态下，如果有选中的窗口，Enter键触发窗口截图
                    if (appStateManager.selectedWindow) {
                        log.info('⏎ Capturing selected window');
                        await handleWindowSelection();
                    }
                    break;

                default:
                    log.debug('⏎ Enter key ignored in current state');
                    break;
            }
        }

        async function handleSpaceKey() {
            log.info('␣ Space key pressed, current state: ' + appStateManager.currentState);

            switch (appStateManager.currentState) {
                case AppState.DETECTING:
                    // 在检测状态下，空格键切换到区域选择模式
                    log.info('␣ Switching to region selection mode');
                    await handleSpacebarPress();
                    break;

                case AppState.REGION_PREVIEW:
                    // 在区域预览状态下，空格键切换到编辑模式
                    log.info('␣ Switching to region editing mode');
                    appStateManager.setState(AppState.REGION_EDITING);
                    break;

                default:
                    log.debug('␣ Space key ignored in current state');
                    break;
            }
        }

        // 🗑️ REMOVED: Legacy regionToolbar functions - replaced by independent toolbar system
        // The following functions have been replaced by the independent toolbar:
        // - initializeRegionToolbar() → Independent toolbar initialization
        // - handleRegionCapture() → Independent toolbar 'save' action
        // - handleRegionEdit() → Independent toolbar drawing tools
        // - handleRegionCopy() → Independent toolbar 'copy' action
        // - handleRegionCancel() → Independent toolbar 'close' action

        // 🆕 创建独立工具栏用于区域选择阶段
        async function createIndependentToolbarForRegion() {
            const region = appStateManager.selectedRegion;

            if (!region) {
                throw new Error('No region selected for toolbar creation');
            }

            log.info('🛠️ Creating independent toolbar for region selection phase');
            log.debug('🛠️ Region coordinates:', region);

            try {
                // 调用后端创建独立工具栏，传入区域坐标用于定位
                const toolbarId = await window.__TAURI__.core.invoke('create_independent_toolbar_for_region', {
                    region: {
                        x: region.x,
                        y: region.y,
                        width: region.width,
                        height: region.height
                    }
                });

                log.info('🛠️ Independent toolbar created with ID:', toolbarId);

                // 存储工具栏ID以便后续管理
                window.currentRegionToolbarId = toolbarId;

                return toolbarId;
            } catch (error) {
                log.error('🛠️ Failed to create independent toolbar:', error);
                throw error;
            }
        }

        // 🗑️ REMOVED: Legacy regionToolbar show/hide functions - replaced by independent toolbar system
        // These functions have been replaced by:
        // - createIndependentToolbarForRegion() → Creates independent toolbar
        // - close_toolbar_window() → Closes independent toolbar via backend

        // 🗑️ REMOVED: Legacy regionToolbar positioning functions - replaced by independent toolbar system
        // These functions have been replaced by backend positioning logic in independent_toolbar.rs

        async function completeScreenshotCapture() {
            log.info('📸 Completing screenshot capture');
            log.debug('📸 Tauri available: ' + !!window.__TAURI__);

            try {
                if (window.__TAURI__) {
                    log.info('📸 Calling complete_screenshot_capture...');
                    await window.__TAURI__.core.invoke('complete_screenshot_capture');
                    log.info('📸 Screenshot capture completed successfully');

                    // 关闭当前overlay窗口
                    log.info('📸 Closing overlay window...');
                    await window.__TAURI__.window.getCurrent().close();
                    log.info('📸 Overlay window closed successfully');
                } else {
                    log.error('📸 Tauri API not available');
                    // 降级处理：直接关闭当前窗口
                    window.close();
                }
            } catch (error) {
                console.error('[FRONTEND] 📸 Failed to complete screenshot capture:', error);
                console.error('[FRONTEND] 📸 Error details:', error.message || error);
                // 降级处理：直接关闭当前窗口
                try {
                    console.log('[FRONTEND] 📸 Attempting fallback window close...');
                    await window.__TAURI__.window.getCurrent().close();
                    console.log('[FRONTEND] 📸 Fallback window close completed');
                } catch (closeError) {
                    console.error('[FRONTEND] 📸 Failed to close window:', closeError);
                }
            }
        }

        async function exitApplication() {
            log.info('🚪 Attempting to exit application');
            log.debug('🚪 Tauri available: ' + !!window.__TAURI__);

            try {
                if (window.__TAURI__) {
                    log.info('🚪 Calling exit_capture_completely_direct...');
                    await window.__TAURI__.core.invoke('exit_capture_completely_direct');
                    log.info('🚪 Direct exit command completed successfully');
                } else {
                    log.error('🚪 Tauri API not available');
                }
            } catch (error) {
                console.error('[FRONTEND] 🚪 Failed to exit capture directly:', error);
                console.error('[FRONTEND] 🚪 Error details:', error.message || error);
                // 降级处理：直接关闭当前窗口
                try {
                    console.log('[FRONTEND] 🚪 Attempting fallback window close...');
                    await window.__TAURI__.window.getCurrent().close();
                    console.log('[FRONTEND] 🚪 Fallback window close completed');
                } catch (closeError) {
                    console.error('[FRONTEND] 🚪 Failed to close window:', closeError);
                }
            }
        }

        async function handleSpacebarPress() {
            try {
                if (window.__TAURI__) {
                    const overlayId = window.location.hash.substring(1) || 'window_highlight_overlay';
                    await window.__TAURI__.core.invoke('handle_spacebar_press', {
                        window_id: overlayId
                    });
                    console.log('Spacebar handled - switching to region selection mode');
                }
            } catch (error) {
                console.error('Failed to handle spacebar press:', error);
            }
        }

        async function handleMouseDown(event) {
            // 🔧 BUG FIX: 在编辑模式下禁用鼠标拖拽操作
            if (appStateManager.currentState === AppState.EDITING) {
                log.info('🚫 Mouse down ignored - editing mode active, mouse interactions disabled');
                event.preventDefault();
                event.stopPropagation();
                return;
            }

            // 🔧 EMERGENCY FIX: Temporarily disable NewRegionCreationManager check
            // This was blocking region selection - will be fixed in follow-up
            /*
            if (typeof NewRegionCreationManager !== 'undefined' &&
                !NewRegionCreationManager.canCreateNewRegion()) {
                log.info('🚫 Mouse down ignored - new region creation disabled');
                event.preventDefault();
                event.stopPropagation();
                return;
            }
            */

            // Log the current state for debugging
            if (typeof NewRegionCreationManager !== 'undefined') {
                log.debug('🔧 NewRegionCreationManager state:', NewRegionCreationManager.canCreateNewRegion());
            }

            log.debug('🖱️ Mouse down detected at: ' + event.clientX + ', ' + event.clientY);
            log.trace('🖱️ Event target: ' + (event.target.className || event.target.tagName));
            log.trace('🖱️ Event capture layer: ' + (event.target === eventCaptureLayer));
            log.debug('🖱️ Current state: ' + appStateManager.currentState);
            log.trace('🖱️ Selected window', appStateManager.selectedWindow);

            // 🔧 CRITICAL FIX: Handle new region creation when clicking outside existing region
            if (appStateManager.currentState === AppState.REGION_PREVIEW &&
                appStateManager.selectedRegion) {

                const clickX = event.clientX;
                const clickY = event.clientY;
                const region = appStateManager.selectedRegion;

                // Check if click is outside the existing region
                const isOutsideRegion = (
                    clickX < region.x ||
                    clickX > region.x + region.width ||
                    clickY < region.y ||
                    clickY > region.y + region.height
                );

                if (isOutsideRegion) {
                    log.info('🔧 Click detected outside existing region - starting new region creation');

                    // 🔧 CRITICAL FIX: Close existing independent toolbar before creating new region
                    if (window.currentRegionToolbarId) {
                        try {
                            log.info('🗑️ Closing existing independent toolbar:', window.currentRegionToolbarId);
                            await window.__TAURI__.core.invoke('close_toolbar_window', {
                                toolbarId: window.currentRegionToolbarId
                            });
                            log.info('✅ Existing independent toolbar closed successfully');
                            window.currentRegionToolbarId = null;
                        } catch (error) {
                            log.error('❌ Failed to close existing toolbar:', error);
                            // Fallback: try to close all active toolbars
                            try {
                                log.info('🗑️ Fallback: Closing all active toolbars');
                                await window.__TAURI__.core.invoke('close_all_active_toolbars');
                                log.info('✅ All active toolbars closed via fallback');
                            } catch (fallbackError) {
                                log.error('❌ Fallback toolbar closure also failed:', fallbackError);
                            }
                            // Continue anyway - don't block new region creation
                        }
                    }

                    // Clear the existing region and transition to detecting state
                    appStateManager.selectedRegion = null;
                    const regionSelector = document.getElementById('regionSelector');
                    if (regionSelector) {
                        regionSelector.style.display = 'none';
                    }

                    // 🗑️ REMOVED: Legacy regionToolbar hiding - replaced by independent toolbar system

                    // Transition to detecting state to allow new region creation
                    appStateManager.setState(AppState.DETECTING);
                    log.info('🔧 Transitioned to DETECTING state for new region creation');

                    // Reset any resize state that might be active
                    if (typeof isResizing !== 'undefined') {
                        isResizing = false;
                    }
                    if (typeof resizeDirection !== 'undefined') {
                        resizeDirection = null;
                    }
                }
            }

            isDragging = true;
            dragStartX = event.clientX;
            dragStartY = event.clientY;

            // 🔧 BUG FIX: 保存点击坐标用于后续截图
            lastClickX = event.clientX;
            lastClickY = event.clientY;

            // 为可能的拖拽操作做准备，但不立即隐藏高亮
            // hideHighlight();

            log.trace('🖱️ Drag state initialized - start position: ' + dragStartX + ', ' + dragStartY);
        }

        function handleMouseUp(event) {
            // 🔧 BUG FIX: 在编辑模式下禁用鼠标拖拽操作
            if (appStateManager.currentState === AppState.EDITING) {
                log.info('🚫 Mouse up ignored - editing mode active, mouse interactions disabled');
                event.preventDefault();
                event.stopPropagation();
                return;
            }

            log.debug('🖱️ Mouse up detected at: ' + event.clientX + ', ' + event.clientY);
            log.trace('🖱️ isDragging state: ' + isDragging);
            log.debug('🖱️ Current state: ' + appStateManager.currentState);
            log.trace('🖱️ Selected window', appStateManager.selectedWindow);

            if (isDragging) {
                const dragEndX = event.clientX;
                const dragEndY = event.clientY;
                const dragDistance = Math.sqrt(
                    Math.pow(dragEndX - dragStartX, 2) + Math.pow(dragEndY - dragStartY, 2)
                );

                log.trace('🖱️ Drag calculation - Start: ' + dragStartX + ', ' + dragStartY +
                         ', End: ' + dragEndX + ', ' + dragEndY +
                         ', Distance: ' + dragDistance + ', Threshold exceeded: ' + (dragDistance > 10));

                if (dragDistance > 10) {
                    log.info('🎯 Drag threshold exceeded! Region selection completed');

                    // 确保区域已被选择
                    if (appStateManager.selectedRegion &&
                        appStateManager.selectedRegion.width > 5 &&
                        appStateManager.selectedRegion.height > 5) {

                        // 切换到区域预览状态，允许用户调整区域
                        appStateManager.setState(AppState.REGION_PREVIEW);
                        log.info('🎯 Switched to region preview mode - user can adjust region');

                        // 添加调整手柄，允许用户继续调整区域
                        const regionSelector = document.getElementById('regionSelector');
                        if (regionSelector) {
                            addResizeHandles(regionSelector);
                        }

                        // 🆕 创建独立工具栏，在区域选择阶段显示
                        log.info('🛠️ Creating independent toolbar during region selection phase');
                        setTimeout(async () => {
                            try {
                                await createIndependentToolbarForRegion();
                                log.info('🛠️ Independent toolbar created successfully during region selection');
                            } catch (error) {
                                log.error('🛠️ Failed to create independent toolbar:', error);
                                // 如果工具栏创建失败，显示fallback overlay工具栏
                                // 🗑️ REMOVED: showRegionToolbar() fallback - independent toolbar system handles failures
                            }
                        }, 100);
                    } else {
                        log.warn('🎯 Region too small or invalid, reverting to detection mode');
                        hideRegionSelector();
                        appStateManager.setState(AppState.DETECTING);
                    }
                } else {
                    log.trace('🖱️ Drag distance is small, treating as a click.');

                    // 如果在区域选择状态，取消选择
                    if (appStateManager.currentState === AppState.REGION_SELECTING) {
                        hideRegionSelector();
                        appStateManager.setState(AppState.DETECTING);
                    }
                    // 检查是否在检测模式下，并且有选中的窗口
                    else if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                        log.debug('🖱️ Triggering window selection from mouse up.');
                        handleWindowSelection();
                    } else {
                        log.trace('🖱️ Click ignored: Not in DETECTING state or no window selected.');
                    }
                }
            } else {
                console.log('[FRONTEND] 🖱️ Mouse up without drag state');
                // 直接点击处理（非拖拽结束）
                if (appStateManager.currentState === AppState.DETECTING && appStateManager.selectedWindow) {
                    console.log('[FRONTEND] 🖱️ Handling direct click on window');
                    handleWindowSelection();
                }
            }

            isDragging = false;
            console.log('[FRONTEND] 🖱️ Drag state reset');
        }

        // 独立的窗口检测函数，支持异步调用和错误处理
        async function performWindowDetection(x, y) {
            // 🔧 ENHANCED LOGGING: Entry point logging
            log.info(`🧠 [WINDOW_DETECT] === STARTING WINDOW DETECTION ===`);
            log.info(`🧠 [WINDOW_DETECT] Position: (${x}, ${y})`);
            log.info(`🧠 [WINDOW_DETECT] Current state: ${appStateManager.currentState}`);
            log.info(`🧠 [WINDOW_DETECT] Detection enabled: ${detectionEnabled}`);
            log.info(`🧠 [WINDOW_DETECT] Tauri API available: ${!!window.__TAURI__}`);

            // 🔧 CRITICAL FIX: 只在特定状态下允许窗口检测
            const currentState = appStateManager.currentState;
            const allowedStates = [AppState.DETECTING];

            if (!allowedStates.includes(currentState)) {
                log.debug(`🧠 [WINDOW_DETECT] Skipping window detection - state ${currentState} not allowed for detection`);
                return;
            }

            try {
                const startTime = performance.now();
                lastLogTime = Date.now();

                // 🔧 ENHANCED LOGGING: API call attempts
                log.info(`🧠 [WINDOW_DETECT] Starting API detection cascade`);

                // 🔧 ISSUE 3 FIX: 优先使用智能检测API，它具有更好的z-order处理
                let detectedWindow = null;
                try {
                    // 首先尝试使用智能检测API（具有增强的z-order处理）
                    log.info(`🧠 [WINDOW_DETECT] Attempting smart detection API call...`);
                    detectedWindow = await window.__TAURI__.core.invoke('detect_window_smart', {
                        x: x,
                        y: y
                    });
                    log.info(`🧠 [WINDOW_DETECT] Smart detection API response:`, detectedWindow);
                } catch (smartError) {
                    log.warn(`🧠 [WINDOW_DETECT] Smart detection failed: ${smartError.message || smartError}`);
                    log.warn(`🧠 [WINDOW_DETECT] Smart detection error details:`, smartError);

                    // 降级到cursor检测API
                    try {
                        log.info(`🧠 [WINDOW_DETECT] Attempting cursor detection API call...`);
                        detectedWindow = await window.__TAURI__.core.invoke('detect_window_under_cursor', {
                            x: x,
                            y: y
                        });
                        log.info(`🧠 [WINDOW_DETECT] Cursor detection API response:`, detectedWindow);
                    } catch (cursorError) {
                        log.warn(`🧠 [WINDOW_DETECT] Cursor detection failed: ${cursorError.message || cursorError}`);
                        log.warn(`🧠 [WINDOW_DETECT] Cursor detection error details:`, cursorError);

                        // 最后降级到实时检测API
                        try {
                            log.info(`🧠 [WINDOW_DETECT] Attempting realtime detection API call...`);
                            detectedWindow = await window.__TAURI__.core.invoke('detect_window_under_mouse_realtime', {
                                x: x,
                                y: y,
                                exclude_overlay_windows: true
                            });
                            log.info(`🧠 [WINDOW_DETECT] Realtime detection API response:`, detectedWindow);
                        } catch (fallbackError) {
                            log.error(`🧠 [WINDOW_DETECT] All detection methods failed: ${fallbackError.message || fallbackError}`);
                            log.error(`🧠 [WINDOW_DETECT] Final fallback error details:`, fallbackError);
                            return;
                        }
                    }
                }

                const endTime = performance.now();
                const detectionTimeMs = Math.round(endTime - startTime);

                log.info(`🧠 [WINDOW_DETECT] Detection completed in ${detectionTimeMs}ms`);

                if (detectedWindow) {
                    const windowTitle = detectedWindow.title || detectedWindow.app_name || 'No title';
                    const windowProcessName = detectedWindow.process_name || detectedWindow.app_name || 'Unknown';
                    const windowId = detectedWindow.id || 'Unknown';

                    log.info(`🧠 [WINDOW_DETECT] ✅ Window detected:`);
                    log.info(`🧠 [WINDOW_DETECT]   - Title: "${windowTitle}"`);
                    log.info(`🧠 [WINDOW_DETECT]   - Process: "${windowProcessName}"`);
                    log.info(`🧠 [WINDOW_DETECT]   - ID: ${windowId}`);
                    log.info(`🧠 [WINDOW_DETECT]   - Position: (${detectedWindow.x || 'N/A'}, ${detectedWindow.y || 'N/A'})`);
                    log.info(`🧠 [WINDOW_DETECT]   - Size: ${detectedWindow.width || 'N/A'}x${detectedWindow.height || 'N/A'}`);

                    // 缓存检测到的窗口信息到状态管理器
                    appStateManager.selectedWindow = detectedWindow;

                    log.info(`🧠 [WINDOW_DETECT] Calling highlightWindow function...`);
                    highlightWindow(detectedWindow);
                    log.info(`🧠 [WINDOW_DETECT] highlightWindow function completed`);
                } else {
                    log.info(`🧠 [WINDOW_DETECT] ❌ No window detected at position (${x}, ${y})`);

                    // 清除缓存的窗口信息
                    appStateManager.selectedWindow = null;

                    log.info(`🧠 [WINDOW_DETECT] Calling hideHighlight function...`);
                    hideHighlight();
                    log.info(`🧠 [WINDOW_DETECT] hideHighlight function completed`);
                }

                log.info(`🧠 [WINDOW_DETECT] === WINDOW DETECTION COMPLETED ===`);
            } catch (error) {
                log.error('[WINDOW_DETECT] ❌ Window detection error:', error.message || error);
                log.error('[WINDOW_DETECT] ❌ Error stack:', error.stack);

                // 显示错误状态
                hideHighlight();
                appStateManager.selectedWindow = null;

                log.info(`🧠 [WINDOW_DETECT] === WINDOW DETECTION FAILED ===`);
            }
        }

        // 🔧 BUG FIX: 更新区域选择UI
        function updateRegionSelector(startX, startY, endX, endY) {
            const regionSelector = document.getElementById('regionSelector');
            const regionDimensions = document.getElementById('regionDimensions');

            if (!regionSelector || !regionDimensions) return;

            // 计算区域位置和大小
            const left = Math.min(startX, endX);
            const top = Math.min(startY, endY);
            const width = Math.abs(endX - startX);
            const height = Math.abs(endY - startY);

            // 更新区域选择器样式
            regionSelector.style.display = 'block';
            regionSelector.style.left = `${left}px`;
            regionSelector.style.top = `${top}px`;
            regionSelector.style.width = `${width}px`;
            regionSelector.style.height = `${height}px`;

            // 更新尺寸显示
            regionDimensions.textContent = `${width} x ${height}`;

            // 记录当前选择的区域
            appStateManager.selectedRegion = {
                x: left,
                y: top,
                width: width,
                height: height
            };

            // 不在这里添加调整手柄，避免重复添加
            // 调整手柄只在状态转换时添加

            // 更新工具栏位置
            // 🗑️ REMOVED: updateRegionToolbarPosition() - independent toolbar handles positioning

            logWithLevel(LOG_LEVELS.TRACE, `🔍 Region updated: ${left},${top} ${width}x${height}`);
        }

        // 添加调整手柄
        function addResizeHandles(regionElement) {
            // 如果正在调整大小，不要重新添加手柄
            if (isResizing) {
                log.debug('🔧 Skipping handle addition - resize in progress');
                return;
            }

            // 先移除已存在的手柄
            removeResizeHandles(regionElement);

            // 添加区域拖拽功能
            addRegionDragFunctionality(regionElement);

            const handles = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w'];

            handles.forEach(direction => {
                const handle = document.createElement('div');
                handle.className = `resize-handle resize-${direction}`;
                handle.style.cssText = `
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background: #2196f3;
                    border: 1px solid white;
                    cursor: ${direction}-resize;
                    z-index: 6200;
                    pointer-events: auto;
                `;

                // 设置手柄位置
                setHandlePosition(handle, direction);

                // 添加事件监听器
                handle.addEventListener('mousedown', (e) => handleResizeStart(e, direction));

                regionElement.appendChild(handle);
            });

            log.info('🔧 Resize handles added');
        }

        // 移除调整手柄
        function removeResizeHandles(regionElement) {
            const handles = regionElement.querySelectorAll('.resize-handle');
            handles.forEach(handle => handle.remove());

            // 同时移除区域拖拽功能
            removeRegionDragFunctionality(regionElement);
        }

        // 设置手柄位置
        function setHandlePosition(handle, direction) {
            switch (direction) {
                case 'nw':
                    handle.style.left = '-4px';
                    handle.style.top = '-4px';
                    break;
                case 'n':
                    handle.style.left = 'calc(50% - 4px)';
                    handle.style.top = '-4px';
                    break;
                case 'ne':
                    handle.style.right = '-4px';
                    handle.style.top = '-4px';
                    break;
                case 'e':
                    handle.style.right = '-4px';
                    handle.style.top = 'calc(50% - 4px)';
                    break;
                case 'se':
                    handle.style.right = '-4px';
                    handle.style.bottom = '-4px';
                    break;
                case 's':
                    handle.style.left = 'calc(50% - 4px)';
                    handle.style.bottom = '-4px';
                    break;
                case 'sw':
                    handle.style.left = '-4px';
                    handle.style.bottom = '-4px';
                    break;
                case 'w':
                    handle.style.left = '-4px';
                    handle.style.top = 'calc(50% - 4px)';
                    break;
            }
        }

        // 调整手柄拖拽变量
        let isResizing = false;
        let resizeDirection = null;
        let resizeStartX = 0;
        let resizeStartY = 0;
        let originalRegion = null;

        // 性能优化：节流控制变量
        let lastResizeUpdate = 0;
        let resizeUpdateThrottle = 16; // 60fps
        let pendingResizeUpdate = null;

        // 🔧 CRITICAL FIX: Drag throttling variables to prevent event loop saturation
        let lastDragUpdate = 0;
        let dragUpdateThrottle = 16; // 60fps (same as resize for consistency)
        let pendingDragUpdate = null;

        // 🔧 GLOBAL OPERATION MANAGER: Prevents race conditions in region operations
        const RegionOperationManager = {
            currentState: 'idle', // 'idle', 'dragging', 'resizing', 'recreating_toolbar'
            currentOperation: null, // Promise for current operation
            operationId: 0, // Unique ID for each operation

            // Execute operation with proper synchronization
            async executeOperation(operationType, operationHandler) {
                const opId = ++this.operationId;
                log.info(`🔒 [OPERATION-${opId}] Starting ${operationType} operation`);

                // Wait for any previous operation to complete
                if (this.currentOperation) {
                    log.info(`🔒 [OPERATION-${opId}] Waiting for previous operation to complete...`);
                    await this.currentOperation;
                }

                // Start new operation
                this.currentState = operationType;
                this.currentOperation = this._runOperation(opId, operationType, operationHandler);

                return this.currentOperation;
            },

            // Internal operation runner
            async _runOperation(opId, operationType, handler) {
                try {
                    log.info(`🔒 [OPERATION-${opId}] Executing ${operationType}...`);
                    await handler();
                    log.info(`🔒 [OPERATION-${opId}] ${operationType} completed successfully`);
                } catch (error) {
                    log.error(`🔒 [OPERATION-${opId}] ${operationType} failed:`, error);
                    throw error;
                } finally {
                    this.currentState = 'idle';
                    this.currentOperation = null;
                    log.info(`🔒 [OPERATION-${opId}] Operation cleanup completed`);
                }
            },

            // Check if system is busy
            isBusy() {
                return this.currentState !== 'idle';
            },

            // Get current state for debugging
            getState() {
                return {
                    state: this.currentState,
                    operationId: this.operationId,
                    isBusy: this.isBusy()
                };
            },

            // Debug function to log current state
            logState(context = '') {
                const state = this.getState();
                log.info(`🔒 [OPERATION-STATE] ${context} - State: ${state.state}, ID: ${state.operationId}, Busy: ${state.isBusy}`);
            }
        };

        // 🔒 DEBUG: Add periodic state monitoring
        setInterval(() => {
            if (RegionOperationManager.isBusy()) {
                RegionOperationManager.logState('PERIODIC-CHECK');
            }
        }, 5000); // Log every 5 seconds if busy

        // 区域拖拽相关变量
        let isRegionDragging = false;
        let regionDragStartX = 0;
        let regionDragStartY = 0;
        let regionDragOriginalRegion = null;

        // 🔧 ENHANCED: 开始调整大小（优雅方案）
        async function handleResizeStart(event, direction) {
            log.debug('🔧 Resize start called with direction:', direction);
            log.debug('🔧 Current state:', appStateManager.currentState);
            log.debug('🔧 Selected region before resize:', appStateManager.selectedRegion);

            if (appStateManager.currentState !== AppState.REGION_PREVIEW) {
                log.warn('🔧 Not in preview state, cannot resize');
                return;
            }

            // 🔒 CRITICAL FIX: Check if system is busy with another operation
            if (RegionOperationManager.isBusy()) {
                log.warn('🔒 System busy with another operation, skipping resize start');
                return;
            }

            event.preventDefault();
            event.stopPropagation();

            // 🔒 CRITICAL FIX: Use operation manager to prevent race conditions
            RegionOperationManager.executeOperation('resizing', async () => {
                // 🔒 INTELLIGENT SOLUTION: 智能处理快速连续调整
                await handleRapidRegionChange('resize');

                isResizing = true;
                resizeDirection = direction;
                resizeStartX = event.clientX;
                resizeStartY = event.clientY;
                originalRegion = { ...appStateManager.selectedRegion };

                // 验证原始区域数据
                if (!originalRegion || isNaN(originalRegion.x) || isNaN(originalRegion.y) ||
                    isNaN(originalRegion.width) || isNaN(originalRegion.height)) {
                    log.error('🔧 Invalid original region for resize:', originalRegion);
                    log.error('🔧 AppStateManager selectedRegion:', appStateManager.selectedRegion);
                    isResizing = false;
                    return;
                }

                log.debug('🔧 Resize started - originalRegion:', originalRegion);

                // 添加全局事件监听器
                document.addEventListener('mousemove', handleResizeMove);
                document.addEventListener('mouseup', handleResizeEnd);

                log.info('🔧 Resize started:', direction);

                // 🔒 CRITICAL: Return promise that resolves when resize ends
                return new Promise((resolve) => {
                    window.currentResizeResolve = resolve;
                });
            });
        }

        // 调整大小过程中（性能优化版本）
        function handleResizeMove(event) {
            if (!isResizing || !originalRegion) return;

            const now = Date.now();

            // 节流控制：限制更新频率到60fps
            if (now - lastResizeUpdate < resizeUpdateThrottle) {
                // 如果有待处理的更新，取消它
                if (pendingResizeUpdate) {
                    cancelAnimationFrame(pendingResizeUpdate);
                }

                // 安排下一次更新
                pendingResizeUpdate = requestAnimationFrame(() => {
                    performResizeUpdate(event);
                });
                return;
            }

            // 立即执行更新
            performResizeUpdate(event);
        }

        // 执行调整大小更新
        function performResizeUpdate(event) {
            const deltaX = event.clientX - resizeStartX;
            const deltaY = event.clientY - resizeStartY;

            let newRegion = { ...originalRegion };
            lastResizeUpdate = Date.now();

            // 根据拖拽方向调整区域
            switch (resizeDirection) {
                case 'nw':
                    newRegion.x += deltaX;
                    newRegion.y += deltaY;
                    newRegion.width -= deltaX;
                    newRegion.height -= deltaY;
                    break;
                case 'n':
                    newRegion.y += deltaY;
                    newRegion.height -= deltaY;
                    break;
                case 'ne':
                    newRegion.y += deltaY;
                    newRegion.width += deltaX;
                    newRegion.height -= deltaY;
                    break;
                case 'e':
                    newRegion.width += deltaX;
                    break;
                case 'se':
                    newRegion.width += deltaX;
                    newRegion.height += deltaY;
                    break;
                case 's':
                    newRegion.height += deltaY;
                    break;
                case 'sw':
                    newRegion.x += deltaX;
                    newRegion.width -= deltaX;
                    newRegion.height += deltaY;
                    break;
                case 'w':
                    newRegion.x += deltaX;
                    newRegion.width -= deltaX;
                    break;
            }

            // 确保最小尺寸
            if (newRegion.width < 20) {
                if (resizeDirection.includes('w')) {
                    newRegion.x = originalRegion.x + originalRegion.width - 20;
                }
                newRegion.width = 20;
            }
            if (newRegion.height < 20) {
                if (resizeDirection.includes('n')) {
                    newRegion.y = originalRegion.y + originalRegion.height - 20;
                }
                newRegion.height = 20;
            }

            // 验证新区域数据的有效性
            if (isNaN(newRegion.x) || isNaN(newRegion.y) || isNaN(newRegion.width) || isNaN(newRegion.height)) {
                log.error('🔧 Invalid resize calculation - NaN values detected:', newRegion);
                log.error('🔧 Original region:', originalRegion);
                log.error('🔧 Delta X/Y:', deltaX, deltaY);
                log.error('🔧 Resize direction:', resizeDirection);
                return;
            }

            // 更新区域显示
            updateRegionFromResize(newRegion);
        }

        // 🔧 ENHANCED: 结束调整大小（优雅方案）
        async function handleResizeEnd(event) {
            if (!isResizing) return;

            try {
                log.info('🔧 Resize ending - selectedRegion before cleanup:', appStateManager.selectedRegion);

                isResizing = false;
                resizeDirection = null;
                originalRegion = null;

                // 移除全局事件监听器
                document.removeEventListener('mousemove', handleResizeMove);
                document.removeEventListener('mouseup', handleResizeEnd);

                // 🔒 INTELLIGENT SOLUTION: 智能完成快速连续调整
                await completeRapidRegionChange('resize');

                log.info('🔧 Resize ended - selectedRegion after cleanup:', appStateManager.selectedRegion);
            } finally {
                // 🔒 CRITICAL FIX: Resolve the operation promise to release the lock
                if (window.currentResizeResolve) {
                    window.currentResizeResolve();
                    window.currentResizeResolve = null;
                }
            }
        }

        // 🗑️ REMOVED: 复杂的工具栏重新激活逻辑 - 已被优雅方案替代
        // 优雅方案：复用重绘时的工具栏生命周期管理
        // - closeToolbarBeforeRegionChange() 替代复杂的z-order管理
        // - recreateToolbarAfterRegionChange() 替代重新激活逻辑

        // 添加区域拖拽功能
        function addRegionDragFunctionality(regionElement) {
            // 移除可能存在的拖拽事件监听器
            removeRegionDragFunctionality(regionElement);

            // 添加鼠标按下事件监听器（仅在区域内部，不在手柄上）
            regionElement.addEventListener('mousedown', handleRegionDragStart);
        }

        // 移除区域拖拽功能
        function removeRegionDragFunctionality(regionElement) {
            regionElement.removeEventListener('mousedown', handleRegionDragStart);
        }

        // 🔧 ENHANCED: 开始区域拖拽（优雅方案）
        async function handleRegionDragStart(event) {
            // 检查是否点击在调整手柄上
            if (event.target.classList.contains('resize-handle')) {
                log.debug('🔧 Click on resize handle, skipping region drag');
                return; // 让调整手柄处理事件
            }

            // 检查是否已经在调整大小
            if (isResizing) {
                log.debug('🔧 Already resizing, skipping region drag');
                return;
            }

            // 只在预览状态下允许拖拽
            if (appStateManager.currentState !== AppState.REGION_PREVIEW) {
                log.debug('🔧 Not in preview state, skipping region drag');
                return;
            }

            // 🔒 CRITICAL FIX: Check if system is busy with another operation
            if (RegionOperationManager.isBusy()) {
                log.warn('🔒 System busy with another operation, skipping drag start');
                return;
            }

            event.preventDefault();
            event.stopPropagation();

            // 🔒 CRITICAL FIX: Use operation manager to prevent race conditions
            RegionOperationManager.executeOperation('dragging', async () => {
                log.info('🔧 Starting region drag - closing toolbar to prevent z-order issues');

                // 🔧 CRITICAL FIX: Force close toolbar before drag to prevent z-order issues
                await handleRapidRegionChange('drag');

                isRegionDragging = true;
                regionDragStartX = event.clientX;
                regionDragStartY = event.clientY;
                regionDragOriginalRegion = { ...appStateManager.selectedRegion };

                // 添加全局事件监听器
                document.addEventListener('mousemove', handleRegionDragMove);
                document.addEventListener('mouseup', handleRegionDragEnd);

                log.info('🔧 Region drag started');

                // 🔒 CRITICAL: Return promise that resolves when drag ends
                return new Promise((resolve) => {
                    window.currentDragResolve = resolve;
                });
            });
        }

        // 🔧 CRITICAL FIX: Throttled region drag move to prevent event loop saturation
        function handleRegionDragMove(event) {
            if (!isRegionDragging || !regionDragOriginalRegion) return;

            const now = Date.now();

            // 🚀 PERFORMANCE FIX: Throttle drag updates to 60fps (same as resize)
            if (now - lastDragUpdate < dragUpdateThrottle) {
                // 如果有待处理的更新，取消它
                if (pendingDragUpdate) {
                    cancelAnimationFrame(pendingDragUpdate);
                }

                // 安排下一次更新
                pendingDragUpdate = requestAnimationFrame(() => {
                    performDragUpdate(event);
                });
                return;
            }

            // 立即执行更新
            performDragUpdate(event);
        }

        // 🔧 CRITICAL FIX: Separated drag update logic with throttling
        function performDragUpdate(event) {
            if (!isRegionDragging || !regionDragOriginalRegion) return;

            const deltaX = event.clientX - regionDragStartX;
            const deltaY = event.clientY - regionDragStartY;

            let newRegion = { ...regionDragOriginalRegion };
            newRegion.x += deltaX;
            newRegion.y += deltaY;

            lastDragUpdate = Date.now();

            // 边界检查：确保区域不会超出屏幕边界
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;

            // 左边界
            if (newRegion.x < 0) {
                newRegion.x = 0;
            }
            // 右边界
            if (newRegion.x + newRegion.width > screenWidth) {
                newRegion.x = screenWidth - newRegion.width;
            }
            // 上边界
            if (newRegion.y < 0) {
                newRegion.y = 0;
            }
            // 下边界
            if (newRegion.y + newRegion.height > screenHeight) {
                newRegion.y = screenHeight - newRegion.height;
            }

            // 更新区域显示
            updateRegionFromResize(newRegion);
        }

        // 🔧 ENHANCED: 结束区域拖拽（优雅方案）
        async function handleRegionDragEnd(event) {
            if (!isRegionDragging) return;

            try {
                // 🔧 CRITICAL FIX: Cancel any pending drag updates to prevent race conditions
                if (pendingDragUpdate) {
                    cancelAnimationFrame(pendingDragUpdate);
                    pendingDragUpdate = null;
                }

                isRegionDragging = false;
                regionDragStartX = 0;
                regionDragStartY = 0;
                regionDragOriginalRegion = null;

                // 移除全局事件监听器
                document.removeEventListener('mousemove', handleRegionDragMove);
                document.removeEventListener('mouseup', handleRegionDragEnd);

                // 🔧 CRITICAL FIX: Recreate toolbar after drag completion
                await completeRapidRegionChange('drag');

                log.info('🔧 Region drag ended - toolbar will be recreated');
            } finally {
                // 🔒 CRITICAL FIX: Resolve the operation promise to release the lock
                if (window.currentDragResolve) {
                    window.currentDragResolve();
                    window.currentDragResolve = null;
                }
            }
        }

        // 从调整大小更新区域
        function updateRegionFromResize(newRegion) {
            const regionSelector = document.getElementById('regionSelector');
            const regionDimensions = document.getElementById('regionDimensions');

            if (!regionSelector || !regionDimensions) {
                log.warn('🔧 Missing DOM elements in updateRegionFromResize');
                return;
            }

            // 🔧 PERFORMANCE FIX: Reduce debug logging frequency to prevent log spam during rapid movements
            // Only log every 10th update or when coordinates change significantly
            const now = Date.now();
            if (!window.lastRegionLogTime || now - window.lastRegionLogTime > 100) {
                log.debug('🔧 Updating region from resize:', newRegion);
                window.lastRegionLogTime = now;
            }

            // 更新区域选择器样式
            regionSelector.style.left = `${newRegion.x}px`;
            regionSelector.style.top = `${newRegion.y}px`;
            regionSelector.style.width = `${newRegion.width}px`;
            regionSelector.style.height = `${newRegion.height}px`;

            // 更新尺寸显示
            regionDimensions.textContent = `${Math.round(newRegion.width)} x ${Math.round(newRegion.height)}`;

            // 更新状态管理器中的区域 - 使用深拷贝确保数据完整性
            appStateManager.selectedRegion = {
                x: newRegion.x,
                y: newRegion.y,
                width: newRegion.width,
                height: newRegion.height
            };

            // 🔧 PERFORMANCE FIX: Throttle this debug log too to prevent spam
            if (!window.lastStateLogTime || now - window.lastStateLogTime > 100) {
                log.debug('🔧 Updated appStateManager.selectedRegion:', appStateManager.selectedRegion);
                window.lastStateLogTime = now;
            }

            // 更新工具栏位置
            // 🗑️ REMOVED: updateRegionToolbarPosition() - independent toolbar handles positioning

            // 确保调整手柄位置正确（如果存在）
            updateResizeHandlePositions(regionSelector);
        }

        // 更新调整手柄位置
        function updateResizeHandlePositions(regionElement) {
            // 如果正在调整大小，不要更新手柄位置
            if (isResizing) {
                return;
            }

            const handles = regionElement.querySelectorAll('.resize-handle');
            handles.forEach(handle => {
                const direction = handle.className.split(' ').find(cls => cls.startsWith('resize-')).replace('resize-', '');
                setHandlePosition(handle, direction);
            });
        }

        // 隐藏区域选择器
        function hideRegionSelector() {
            const regionSelector = document.getElementById('regionSelector');
            if (regionSelector) {
                regionSelector.style.display = 'none';
                regionSelector.removeAttribute('data-tauri-drag-region');
                // 移除调整手柄
                removeResizeHandles(regionSelector);
            }

            // 隐藏工具栏
            // 🗑️ REMOVED: hideRegionToolbar() - independent toolbar system handles cleanup

            // 清除选中的区域
            appStateManager.selectedRegion = null;
            log.info('🔍 Region selector hidden');
        }

        async function handleMouseDragStart(startX, startY) {
            console.log('[FRONTEND] 🎯 handleMouseDragStart called with:', startX, startY);

            try {
                if (window.__TAURI__) {
                    // 🔧 CRITICAL FIX: 使用固定的窗口ID以匹配capabilities配置
                    const overlayId = 'window_highlight_overlay';
                    console.log('[FRONTEND] 🎯 Calling backend with overlay ID:', overlayId);
                    console.log('[FRONTEND] 🎯 Invoking handle_mouse_drag_start...');

                    // 🔧 CRITICAL FIX: 确保参数名称与后端匹配
                    const result = await window.__TAURI__.core.invoke('handle_mouse_drag_start', {
                        window_id: overlayId,  // 正确的参数名称是window_id，不是windowId
                        start_x: startX,
                        start_y: startY
                    });

                    console.log('[FRONTEND] 🎯 Backend response:', result);
                    console.log('[FRONTEND] 🎯 Mouse drag start handled - switching to region selection mode');

                    // 🔧 PRODUCTION: modeIndicator已移除，保持界面干净
                } else {
                    console.error('[FRONTEND] 🎯 Tauri API not available');
                }
            } catch (error) {
                console.error('[FRONTEND] 🎯 Failed to handle mouse drag start:', error);
                console.error('[FRONTEND] 🎯 Error details:', error.message || error);
            }
        }

        function closeOverlay() {
            if (window.__TAURI__) {
                window.__TAURI__.window.getCurrent().close();
            }
        }

        // 检查macOS权限
        async function checkMacOSPermissions() {
            try {
                if (!window.__TAURI__) {
                    console.log('[FRONTEND] 🔍 Tauri API not available, skipping permission check');
                    return;
                }

                console.log('[FRONTEND] 🔍 Checking macOS permissions...');

                // 使用安全的权限检查方式，避免调用受限API
                try {
                    const hasPermission = await window.__TAURI__.core.invoke('check_macos_permissions');

                    if (hasPermission) {
                        console.log('[FRONTEND] ✅ macOS screen recording permission granted');
                        updateStatus('API', 'Smart');
                    } else {
                        console.warn('[FRONTEND] ⚠️ macOS screen recording permission not granted');
                        updateStatus('API', 'Limited');
                        showPermissionWarning();
                    }
                } catch (permissionError) {
                    console.warn('[FRONTEND] ⚠️ Permission check API not available:', permissionError.message || permissionError);
                    // 降级处理：假设有权限，让用户在实际使用时发现问题
                    updateStatus('API', 'Unknown');
                }
            } catch (error) {
                console.error('[FRONTEND] ❌ Failed to check macOS permissions:', error);
                // 如果权限检查失败，可能是非macOS平台
                console.log('[FRONTEND] 🔍 Permission check failed, likely not macOS platform');
                updateStatus('API', 'Unknown');
            }
        }

        // 🔧 CRITICAL FIX: 添加缺失的updateStatus函数
        function updateStatus(type, value) {
            logWithLevel(LOG_LEVELS.INFO, `🔧 Status update: ${type} = ${value}`);
            // 更新UI状态（如果有相关元素）
            const statusElement = document.getElementById(`${type.toLowerCase()}Status`);
            if (statusElement) {
                statusElement.textContent = value;
            }
        }

        // 显示权限警告
        function showPermissionWarning() {
            // 🔧 PRODUCTION: 权限警告功能已简化，调试元素已移除
            log.warn('⚠️ macOS screen recording permission required for full functionality');
        }

        // 定期刷新窗口列表
        setInterval(() => {
            if (detectionEnabled) {
                loadWindows();
            }
        }, 2000);

        // 确保透明度设置
        document.body.style.setProperty('background-color', 'transparent', 'important');
        document.documentElement.style.setProperty('background-color', 'transparent', 'important');

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            log.error('🚨 Global error: ' + event.error);
            log.error('🚨 Error message: ' + event.message);
            log.error('🚨 Error filename: ' + event.filename);
            log.error('🚨 Error line: ' + event.lineno);
        });

        window.addEventListener('unhandledrejection', function(event) {
            log.error('🚨 Unhandled promise rejection: ' + event.reason);
        });

        console.log('[FRONTEND] 🎯 Window highlight overlay loaded');
        console.log('[FRONTEND] 🎯 Body background:', window.getComputedStyle(document.body).backgroundColor);
        console.log('[FRONTEND] 🎯 HTML background:', window.getComputedStyle(document.documentElement).backgroundColor);

        // 立即检查macOS权限（不等待）
        setTimeout(() => {
            checkMacOSPermissions().catch(error => {
                console.error('[FRONTEND] 🚨 macOS permission check failed:', error);
            });
        }, 100);

        // 检查窗口焦点状态
        console.log('[FRONTEND] 🎯 Document has focus:', document.hasFocus());
        console.log('[FRONTEND] 🎯 Active element:', document.activeElement.tagName);
        console.log('[FRONTEND] 🎯 Tauri available:', !!window.__TAURI__);

        // 强制设置焦点到document
        if (!document.hasFocus()) {
            console.log('[FRONTEND] 🎯 Document does not have focus, attempting to focus...');
            window.focus();
            document.body.focus();
        }

        // 测试键盘事件监听
        console.log('[FRONTEND] 🎯 Testing keyboard event listener...');
        console.log('[FRONTEND] 🎯 Event listeners attached to document');

        // 立即测试一个键盘事件
        document.addEventListener('keydown', (e) => {
            console.log('[FRONTEND] 🎯 IMMEDIATE KEY TEST - Key pressed:', e.key);
        }, { once: true });

        setTimeout(() => {
            console.log('[FRONTEND] 🎯 Please press ESC key to test...');
            console.log('[FRONTEND] 🎯 Current focus element:', document.activeElement);
            console.log('[FRONTEND] 🎯 Document visibility:', document.visibilityState);
        }, 1000);

        // 🆕 测试函数 - 用于验证共享工具栏功能
        window.testSharedToolbar = function() {
            log.info('🧪 Starting shared toolbar test');

            // 模拟区域选择
            const testRegion = {
                x: 200,
                y: 150,
                width: 400,
                height: 300
            };

            // 设置测试区域
            appStateManager.selectedRegion = testRegion;

            // 显示区域选择器
            const regionSelector = document.getElementById('regionSelector');
            if (regionSelector) {
                regionSelector.style.display = 'block';
                regionSelector.style.left = `${testRegion.x}px`;
                regionSelector.style.top = `${testRegion.y}px`;
                regionSelector.style.width = `${testRegion.width}px`;
                regionSelector.style.height = `${testRegion.height}px`;
                regionSelector.style.border = '2px solid #673AB7';
                regionSelector.style.background = 'rgba(103, 58, 183, 0.1)';
            }

            // 切换到标注模式
            appStateManager.setState(AppState.REGION_ANNOTATION);

            log.info('🧪 Test region created and annotation mode activated');
            log.info('🧪 Test region:', testRegion);

            // 5秒后自动清理测试
            setTimeout(() => {
                log.info('🧪 Cleaning up test');
                appStateManager.setState(AppState.DETECTING);
            }, 5000);
        };

        // 🆕 测试工具栏定位
        window.testToolbarPositioning = function() {
            log.info('🧪 Testing toolbar positioning');

            const testRegions = [
                { x: 100, y: 100, width: 300, height: 200, name: 'Top-Left' },
                { x: window.innerWidth - 400, y: 100, width: 300, height: 200, name: 'Top-Right' },
                { x: 100, y: window.innerHeight - 300, width: 300, height: 200, name: 'Bottom-Left' },
                { x: window.innerWidth - 400, y: window.innerHeight - 300, width: 300, height: 200, name: 'Bottom-Right' },
                { x: window.innerWidth / 2 - 150, y: window.innerHeight / 2 - 100, width: 300, height: 200, name: 'Center' }
            ];

            let currentIndex = 0;

            function testNextPosition() {
                if (currentIndex >= testRegions.length) {
                    log.info('🧪 Positioning test completed');
                    appStateManager.setState(AppState.DETECTING);
                    return;
                }

                const region = testRegions[currentIndex];
                log.info(`🧪 Testing position: ${region.name}`);

                // 设置测试区域
                appStateManager.selectedRegion = region;

                // 显示区域选择器
                const regionSelector = document.getElementById('regionSelector');
                if (regionSelector) {
                    regionSelector.style.display = 'block';
                    regionSelector.style.left = `${region.x}px`;
                    regionSelector.style.top = `${region.y}px`;
                    regionSelector.style.width = `${region.width}px`;
                    regionSelector.style.height = `${region.height}px`;
                    regionSelector.style.border = '2px solid #673AB7';
                    regionSelector.style.background = 'rgba(103, 58, 183, 0.1)';
                }

                // 切换到标注模式
                appStateManager.setState(AppState.REGION_ANNOTATION);

                currentIndex++;
                setTimeout(testNextPosition, 2000);
            }

            testNextPosition();
        };

        // 🆕 测试工具栏交互
        window.testToolbarInteraction = function() {
            log.info('🧪 Testing toolbar interaction');

            // 首先创建测试区域
            const testRegion = {
                x: 300,
                y: 200,
                width: 350,
                height: 250
            };

            appStateManager.selectedRegion = testRegion;

            // 显示区域选择器
            const regionSelector = document.getElementById('regionSelector');
            if (regionSelector) {
                regionSelector.style.display = 'block';
                regionSelector.style.left = `${testRegion.x}px`;
                regionSelector.style.top = `${testRegion.y}px`;
                regionSelector.style.width = `${testRegion.width}px`;
                regionSelector.style.height = `${testRegion.height}px`;
                regionSelector.style.border = '2px solid #673AB7';
                regionSelector.style.background = 'rgba(103, 58, 183, 0.1)';
            }

            // 切换到标注模式
            appStateManager.setState(AppState.REGION_ANNOTATION);

            // 模拟工具选择
            setTimeout(() => {
                const tools = ['text', 'arrow', 'rectangle', 'circle', 'brush'];
                let toolIndex = 0;

                function selectNextTool() {
                    if (toolIndex < tools.length) {
                        const tool = tools[toolIndex];
                        const button = document.querySelector(`[data-tool="${tool}"]`);

                        if (button) {
                            log.info(`🧪 Simulating ${tool} tool selection`);
                            button.click();
                        }

                        toolIndex++;
                        setTimeout(selectNextTool, 1000);
                    } else {
                        log.info('🧪 Tool interaction test completed');
                        setTimeout(() => {
                            appStateManager.setState(AppState.DETECTING);
                        }, 2000);
                    }
                }

                selectNextTool();
            }, 1000);
        };

        // 🆕 完整的测试套件
        window.runCompleteTestSuite = async function() {
            log.info('🧪 Starting complete test suite');

            try {
                // 1. 状态转换测试
                await StateTransitionTester.testAllStateTransitions();
                await StateTransitionTester.delay(1000);

                // 2. UI状态一致性测试
                await StateTransitionTester.testUIStateConsistency();
                await StateTransitionTester.delay(1000);

                // 3. 事件处理器测试
                await testEventHandlers();
                await StateTransitionTester.delay(1000);

                // 4. 区域调整功能测试
                await testRegionAdjustmentFeatures();
                await StateTransitionTester.delay(1000);

                // 5. 后端集成测试
                await BackendIntegrationTester.runCompleteBackendTests();

                log.info('🧪 Complete test suite finished');

            } catch (error) {
                log.error('🧪 Test suite failed:', error);
            }
        };

        // 🆕 事件处理器测试
        async function testEventHandlers() {
            log.info('🧪 Testing event handlers');

            const states = [AppState.DETECTING, AppState.REGION_ANNOTATION];

            for (const state of states) {
                appStateManager.setState(state);
                await StateTransitionTester.delay(200);

                // 检查事件处理器是否正确设置
                if (typeof EventHandlerManager !== 'undefined') {
                    const activeHandlers = EventHandlerManager.getActiveHandlers();
                    log.info(`🧪 State ${state} - Active handlers:`, activeHandlers);

                    if (state === AppState.REGION_ANNOTATION) {
                        const hasAnnotationHandlers = activeHandlers.some(h => h.includes('annotation'));
                        if (hasAnnotationHandlers) {
                            log.info('✅ Annotation event handlers correctly active');
                        } else {
                            log.warn('❌ Annotation event handlers not active');
                        }
                    }
                }
            }
        }

        // 🆕 后端集成测试
        const BackendIntegrationTester = {
            // 测试后端状态管理
            async testBackendStateManagement() {
                log.info('🧪 Testing backend state management');

                try {
                    // 测试获取overlay标注状态
                    const initialState = await window.__TAURI__.core.invoke('get_overlay_annotation_state');
                    log.info('✅ Initial overlay annotation state:', initialState);

                    // 测试开始标注会话
                    const testRegion = {
                        x: 100,
                        y: 100,
                        width: 400,
                        height: 300,
                        screen_width: 1920,
                        screen_height: 1080
                    };

                    await window.__TAURI__.core.invoke('start_overlay_annotation_session', {
                        overlayId: 'test-overlay',
                        region: testRegion
                    });
                    log.info('✅ Overlay annotation session started');

                    // 测试工具更新
                    await window.__TAURI__.core.invoke('update_overlay_annotation_tool', {
                        tool: 'brush'
                    });
                    log.info('✅ Tool updated to brush');

                    // 测试状态获取
                    const updatedState = await window.__TAURI__.core.invoke('get_overlay_annotation_state');
                    log.info('✅ Updated overlay annotation state:', updatedState);

                    // 测试结束会话
                    await window.__TAURI__.core.invoke('end_overlay_annotation_session');
                    log.info('✅ Overlay annotation session ended');

                    return true;

                } catch (error) {
                    log.error('❌ Backend state management test failed:', error);
                    return false;
                }
            },

            // 测试工具栏定位逻辑
            async testToolbarPositioning() {
                log.info('🧪 Testing toolbar positioning logic');

                try {
                    const testRegion = {
                        x: 200,
                        y: 150,
                        width: 350,
                        height: 250
                    };

                    const position = await window.__TAURI__.core.invoke('calculate_overlay_toolbar_position', {
                        region: testRegion,
                        screenWidth: 1920,
                        screenHeight: 1080
                    });

                    log.info('✅ Toolbar position calculated:', position);

                    // 验证位置数据
                    if (typeof position.x === 'number' &&
                        typeof position.y === 'number' &&
                        position.layout &&
                        position.position) {
                        log.info('✅ Position data structure valid');
                        return true;
                    } else {
                        log.error('❌ Invalid position data structure');
                        return false;
                    }

                } catch (error) {
                    log.error('❌ Toolbar positioning test failed:', error);
                    return false;
                }
            },

            // 测试保存/导出功能
            async testSaveExportFunctionality() {
                log.info('🧪 Testing save/export functionality');

                try {
                    // 测试获取保存选项
                    const saveOptions = await window.__TAURI__.core.invoke('get_save_options');
                    log.info('✅ Save options retrieved:', saveOptions);

                    // 创建测试Canvas数据
                    const testCanvas = document.createElement('canvas');
                    testCanvas.width = 400;
                    testCanvas.height = 300;
                    const ctx = testCanvas.getContext('2d');

                    // 绘制测试内容
                    ctx.fillStyle = '#ff6b35';
                    ctx.fillRect(50, 50, 100, 100);
                    ctx.fillStyle = '#673AB7';
                    ctx.font = '20px Arial';
                    ctx.fillText('Test Annotation', 200, 150);

                    const testCanvasData = testCanvas.toDataURL('image/png');

                    // 测试保存功能（使用默认路径）
                    const savedPath = await window.__TAURI__.core.invoke('save_annotated_image', {
                        canvasData: testCanvasData,
                        originalImagePath: null,
                        savePath: null
                    });
                    log.info('✅ Test image saved:', savedPath);

                    // 测试剪贴板导出
                    await window.__TAURI__.core.invoke('export_annotated_image_to_clipboard', {
                        canvasData: testCanvasData
                    });
                    log.info('✅ Test image exported to clipboard');

                    return true;

                } catch (error) {
                    log.error('❌ Save/export functionality test failed:', error);
                    return false;
                }
            },

            // 运行完整的后端集成测试套件
            async runCompleteBackendTests() {
                log.info('🧪 Starting complete backend integration tests');

                const results = {
                    stateManagement: false,
                    toolbarPositioning: false,
                    saveExport: false
                };

                try {
                    // 运行所有测试
                    results.stateManagement = await this.testBackendStateManagement();
                    await new Promise(resolve => setTimeout(resolve, 500));

                    results.toolbarPositioning = await this.testToolbarPositioning();
                    await new Promise(resolve => setTimeout(resolve, 500));

                    results.saveExport = await this.testSaveExportFunctionality();

                    // 生成测试报告
                    this.generateBackendTestReport(results);

                    return results;

                } catch (error) {
                    log.error('🧪 Backend integration tests failed:', error);
                    return results;
                }
            },

            // 生成后端测试报告
            generateBackendTestReport(results) {
                const totalTests = Object.keys(results).length;
                const passedTests = Object.values(results).filter(r => r).length;
                const failedTests = totalTests - passedTests;

                log.info('📊 Backend Integration Test Report:');
                log.info(`📊 Total Tests: ${totalTests}`);
                log.info(`✅ Passed: ${passedTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
                log.info(`❌ Failed: ${failedTests} (${(failedTests/totalTests*100).toFixed(1)}%)`);

                // 详细结果
                Object.entries(results).forEach(([testName, passed]) => {
                    const status = passed ? '✅' : '❌';
                    log.info(`  ${status} ${testName}: ${passed ? 'PASSED' : 'FAILED'}`);
                });

                // 保存结果到window对象
                window.backendIntegrationTestResults = results;

                if (passedTests === totalTests) {
                    log.info('🎉 All backend integration tests passed!');
                } else {
                    log.warn(`⚠️ ${failedTests} backend integration tests failed`);
                }
            }
        };

        // 🆕 区域调整功能测试
        async function testRegionAdjustmentFeatures() {
            log.info('🧪 Testing region adjustment features');

            // 创建测试区域
            const testRegion = { x: 200, y: 150, width: 300, height: 200 };
            appStateManager.selectedRegion = testRegion;

            // 测试不同状态下的区域调整功能
            const testStates = [
                { state: AppState.REGION_PREVIEW, shouldAllow: true },
                { state: AppState.REGION_ANNOTATION, shouldAllow: false },
                { state: AppState.CAPTURING, shouldAllow: false }
            ];

            for (const test of testStates) {
                appStateManager.setState(test.state);
                await StateTransitionTester.delay(200);

                if (typeof RegionAdjustmentManager !== 'undefined') {
                    const isEnabled = RegionAdjustmentManager.isRegionAdjustmentEnabled();

                    if (isEnabled === test.shouldAllow) {
                        log.info(`✅ ${test.state}: Region adjustment correctly ${test.shouldAllow ? 'enabled' : 'disabled'}`);
                    } else {
                        log.warn(`❌ ${test.state}: Region adjustment should be ${test.shouldAllow ? 'enabled' : 'disabled'}, but is ${isEnabled ? 'enabled' : 'disabled'}`);
                    }
                }

                if (typeof NewRegionCreationManager !== 'undefined') {
                    const canCreate = NewRegionCreationManager.canCreateNewRegion();
                    const shouldCreate = [AppState.DETECTING, AppState.REGION_SELECTING].includes(test.state);

                    if (canCreate === shouldCreate) {
                        log.info(`✅ ${test.state}: New region creation correctly ${shouldCreate ? 'enabled' : 'disabled'}`);
                    } else {
                        log.warn(`❌ ${test.state}: New region creation should be ${shouldCreate ? 'enabled' : 'disabled'}, but is ${canCreate ? 'enabled' : 'disabled'}`);
                    }
                }
            }
        }

        // 🆕 添加测试快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey) {
                switch (e.key) {
                    case 'T':
                        e.preventDefault();
                        window.testSharedToolbar();
                        break;
                    case 'P':
                        e.preventDefault();
                        window.testToolbarPositioning();
                        break;
                    case 'I':
                        e.preventDefault();
                        window.testToolbarInteraction();
                        break;
                    case 'S':
                        e.preventDefault();
                        StateTransitionTester.testAllStateTransitions();
                        break;
                    case 'U':
                        e.preventDefault();
                        StateTransitionTester.testUIStateConsistency();
                        break;
                    case 'A':
                        e.preventDefault();
                        window.runCompleteTestSuite();
                        break;
                    case 'B':
                        e.preventDefault();
                        BackendIntegrationTester.runCompleteBackendTests();
                        break;
                }
            }
        });

        // 🆕 状态转换测试框架
        const StateTransitionTester = {
            // 测试结果存储
            testResults: [],

            // 当前测试状态
            currentTest: null,

            // 测试所有状态转换
            async testAllStateTransitions() {
                log.info('🧪 Starting comprehensive state transition tests');
                this.testResults = [];

                const states = Object.values(AppState);
                const testCases = this.generateStateTransitionTestCases(states);

                for (const testCase of testCases) {
                    await this.runStateTransitionTest(testCase);
                    await this.delay(500); // 等待状态稳定
                }

                this.generateTestReport();
                log.info('🧪 State transition tests completed');
            },

            // 生成状态转换测试用例
            generateStateTransitionTestCases(states) {
                const testCases = [];

                // 测试所有可能的状态转换
                for (const fromState of states) {
                    for (const toState of states) {
                        if (fromState !== toState) {
                            testCases.push({
                                id: `${fromState}_to_${toState}`,
                                fromState,
                                toState,
                                expected: appStateManager.isValidTransition(fromState, toState)
                            });
                        }
                    }
                }

                return testCases;
            },

            // 运行单个状态转换测试
            async runStateTransitionTest(testCase) {
                this.currentTest = testCase;
                const startTime = performance.now();

                try {
                    // 设置初始状态
                    appStateManager.currentState = testCase.fromState;

                    // 尝试转换到目标状态
                    const transitionResult = this.attemptStateTransition(testCase.fromState, testCase.toState);

                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    // 验证结果
                    const testResult = {
                        ...testCase,
                        actual: transitionResult.success,
                        passed: transitionResult.success === testCase.expected,
                        duration,
                        error: transitionResult.error,
                        timestamp: new Date().toISOString()
                    };

                    this.testResults.push(testResult);

                    if (testResult.passed) {
                        log.debug(`✅ ${testCase.id}: PASSED (${duration.toFixed(2)}ms)`);
                    } else {
                        log.warn(`❌ ${testCase.id}: FAILED - Expected: ${testCase.expected}, Actual: ${transitionResult.success}`);
                    }

                } catch (error) {
                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    this.testResults.push({
                        ...testCase,
                        actual: false,
                        passed: false,
                        duration,
                        error: error.message,
                        timestamp: new Date().toISOString()
                    });

                    log.error(`💥 ${testCase.id}: ERROR - ${error.message}`);
                }
            },

            // 尝试状态转换
            attemptStateTransition(fromState, toState) {
                try {
                    // 检查转换是否被允许
                    const isValid = appStateManager.isValidTransition(fromState, toState);

                    if (isValid) {
                        // 执行状态转换
                        appStateManager.setState(toState);
                        return { success: true, error: null };
                    } else {
                        // 转换不被允许
                        return { success: false, error: 'Transition not allowed' };
                    }

                } catch (error) {
                    return { success: false, error: error.message };
                }
            },

            // 生成测试报告
            generateTestReport() {
                const totalTests = this.testResults.length;
                const passedTests = this.testResults.filter(r => r.passed).length;
                const failedTests = totalTests - passedTests;
                const averageDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0) / totalTests;

                log.info('📊 State Transition Test Report:');
                log.info(`📊 Total Tests: ${totalTests}`);
                log.info(`✅ Passed: ${passedTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
                log.info(`❌ Failed: ${failedTests} (${(failedTests/totalTests*100).toFixed(1)}%)`);
                log.info(`⏱️ Average Duration: ${averageDuration.toFixed(2)}ms`);

                // 显示失败的测试
                const failures = this.testResults.filter(r => !r.passed);
                if (failures.length > 0) {
                    log.warn('❌ Failed Tests:');
                    failures.forEach(failure => {
                        log.warn(`  ${failure.id}: Expected ${failure.expected}, got ${failure.actual} - ${failure.error || 'No error'}`);
                    });
                }

                // 保存详细报告到window对象供调试使用
                window.stateTransitionTestResults = this.testResults;
            },

            // 延迟函数
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            },

            // 🆕 测试UI状态一致性
            async testUIStateConsistency() {
                log.info('🧪 Testing UI state consistency');

                const states = Object.values(AppState);
                const uiTestResults = [];

                for (const state of states) {
                    const testResult = await this.testUIForState(state);
                    uiTestResults.push(testResult);
                    await this.delay(300);
                }

                this.generateUITestReport(uiTestResults);
            },

            // 测试特定状态的UI
            async testUIForState(state) {
                const startTime = performance.now();

                try {
                    // 设置状态
                    appStateManager.setState(state);
                    await this.delay(100); // 等待UI更新

                    // 检查UI元素
                    const uiChecks = this.checkUIElementsForState(state);

                    const endTime = performance.now();

                    return {
                        state,
                        passed: uiChecks.allPassed,
                        checks: uiChecks.checks,
                        duration: endTime - startTime,
                        timestamp: new Date().toISOString()
                    };

                } catch (error) {
                    const endTime = performance.now();

                    return {
                        state,
                        passed: false,
                        checks: [],
                        error: error.message,
                        duration: endTime - startTime,
                        timestamp: new Date().toISOString()
                    };
                }
            },

            // 检查UI元素状态
            checkUIElementsForState(state) {
                const checks = [];

                // 检查状态指示器
                const stateIndicator = document.getElementById('stateIndicator');
                if (stateIndicator) {
                    checks.push({
                        element: 'stateIndicator',
                        property: 'visibility',
                        expected: 'visible',
                        actual: stateIndicator.style.display !== 'none' ? 'visible' : 'hidden',
                        passed: stateIndicator.style.display !== 'none'
                    });
                }

                // 检查区域选择器
                const regionSelector = document.getElementById('regionSelector');
                if (regionSelector) {
                    const shouldBeVisible = [AppState.REGION_PREVIEW, AppState.REGION_EDITING, AppState.REGION_ANNOTATION].includes(state);
                    const isVisible = regionSelector.style.display !== 'none';

                    checks.push({
                        element: 'regionSelector',
                        property: 'visibility',
                        expected: shouldBeVisible ? 'visible' : 'hidden',
                        actual: isVisible ? 'visible' : 'hidden',
                        passed: shouldBeVisible === isVisible
                    });
                }

                // 检查共享工具栏
                const sharedToolbar = document.getElementById('overlaySharedToolbar');
                if (sharedToolbar) {
                    const shouldBeVisible = state === AppState.REGION_ANNOTATION;
                    const isVisible = sharedToolbar.style.display !== 'none' && sharedToolbar.classList.contains('active');

                    checks.push({
                        element: 'overlaySharedToolbar',
                        property: 'visibility',
                        expected: shouldBeVisible ? 'visible' : 'hidden',
                        actual: isVisible ? 'visible' : 'hidden',
                        passed: shouldBeVisible === isVisible
                    });
                }

                // 检查标注Canvas
                const annotationCanvas = document.getElementById('annotationCanvas');
                if (annotationCanvas) {
                    const shouldBeVisible = state === AppState.REGION_ANNOTATION;
                    const isVisible = annotationCanvas.style.display !== 'none' && annotationCanvas.classList.contains('active');

                    checks.push({
                        element: 'annotationCanvas',
                        property: 'visibility',
                        expected: shouldBeVisible ? 'visible' : 'hidden',
                        actual: isVisible ? 'visible' : 'hidden',
                        passed: shouldBeVisible === isVisible
                    });
                }

                const allPassed = checks.every(check => check.passed);

                return { checks, allPassed };
            },

            // 生成UI测试报告
            generateUITestReport(results) {
                const totalTests = results.length;
                const passedTests = results.filter(r => r.passed).length;
                const failedTests = totalTests - passedTests;

                log.info('📊 UI State Consistency Test Report:');
                log.info(`📊 Total States Tested: ${totalTests}`);
                log.info(`✅ Passed: ${passedTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
                log.info(`❌ Failed: ${failedTests} (${(failedTests/totalTests*100).toFixed(1)}%)`);

                // 显示失败的测试
                const failures = results.filter(r => !r.passed);
                if (failures.length > 0) {
                    log.warn('❌ Failed UI Tests:');
                    failures.forEach(failure => {
                        log.warn(`  ${failure.state}:`);
                        if (failure.error) {
                            log.warn(`    Error: ${failure.error}`);
                        } else {
                            failure.checks.filter(c => !c.passed).forEach(check => {
                                log.warn(`    ${check.element}.${check.property}: Expected ${check.expected}, got ${check.actual}`);
                            });
                        }
                    });
                }

                window.uiStateTestResults = results;
            }
        };

        log.info('🧪 Test functions loaded. Test shortcuts:');
        log.info('🧪   Ctrl+Shift+T - Basic toolbar test');
        log.info('🧪   Ctrl+Shift+P - Positioning test');
        log.info('🧪   Ctrl+Shift+I - Interaction test');
        log.info('🧪   Ctrl+Shift+S - State transition test');
        log.info('🧪   Ctrl+Shift+U - UI consistency test');
        log.info('🧪   Ctrl+Shift+A - Complete test suite');
        log.info('🧪   Ctrl+Shift+B - Backend integration tests');
    </script>

    <!-- 🆕 引入共享工具栏组件 -->
    <script src="shared/toolbar-component.js"></script>
    <script src="shared/toolbar-functions.js"></script>
</body>
</html>
