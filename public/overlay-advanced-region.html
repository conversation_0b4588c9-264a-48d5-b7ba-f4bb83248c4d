<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Advanced Region Selector</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.3);
            cursor: crosshair;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
            transition: cursor 0.2s ease;
        }

        /* P1-T7: 用户体验优化 - 光标样式 */
        body.mode-select { cursor: crosshair; }
        body.mode-drag { cursor: move; }
        body.mode-resize { cursor: nw-resize; }
        body.mode-ready { cursor: pointer; }
        body.mode-disabled { cursor: not-allowed; }

        .overlay-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .selection-area {
            position: absolute;
            border: 2px solid var(--selection-color, #007AFF);
            background: rgba(0, 122, 255, 0.1);
            display: none;
            pointer-events: none;
            box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
        }

        .selection-handles {
            position: absolute;
            width: 8px;
            height: 8px;
            background: var(--selection-color, #007AFF);
            border: 1px solid white;
            border-radius: 50%;
            display: none;
            cursor: pointer;
            pointer-events: auto;
        }

        .handle-nw { top: -4px; left: -4px; cursor: nw-resize; }
        .handle-ne { top: -4px; right: -4px; cursor: ne-resize; }
        .handle-sw { bottom: -4px; left: -4px; cursor: sw-resize; }
        .handle-se { bottom: -4px; right: -4px; cursor: se-resize; }
        .handle-n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
        .handle-s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
        .handle-w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
        .handle-e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.2;
            display: none;
        }

        .grid-line {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
        }

        .grid-line.vertical {
            width: 1px;
            height: 100%;
        }

        .grid-line.horizontal {
            width: 100%;
            height: 1px;
        }

        .magnifier {
            position: absolute;
            width: 120px;
            height: 120px;
            border: 2px solid var(--selection-color, #007AFF);
            border-radius: 50%;
            background: white;
            display: none;
            pointer-events: none;
            z-index: 1000;
            overflow: hidden;
        }

        .magnifier canvas {
            width: 100%;
            height: 100%;
        }

        .coordinates-display {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-family: monospace;
            display: none;
            min-width: 250px;
        }

        .coordinates-display .label {
            color: var(--selection-color, #007AFF);
            font-weight: bold;
        }

        .toolbar {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 8px;
            padding: 8px;
            display: none;
            gap: 8px;
            align-items: center;
            backdrop-filter: blur(10px);
        }

        .toolbar-btn {
            background: var(--selection-color, #007AFF);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .toolbar-btn:hover {
            background: #0056CC;
            transform: translateY(-1px);
        }

        .toolbar-btn.secondary {
            background: rgba(255, 255, 255, 0.2);
        }

        .toolbar-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .toolbar-btn.danger {
            background: #FF3B30;
        }

        .toolbar-btn.danger:hover {
            background: #D70015;
        }

        .instructions {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 24px 32px;
            border-radius: 12px;
            text-align: center;
            font-size: 16px;
            backdrop-filter: blur(10px);
        }

        .instructions h2 {
            margin-bottom: 16px;
            color: var(--selection-color, #007AFF);
            font-size: 20px;
        }

        .instructions p {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .instructions .shortcut {
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }

        .mode-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--selection-color, #007AFF);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* P1-T7: 模式指示器状态 */
        .mode-indicator.selecting { background: #007AFF; }
        .mode-indicator.dragging { background: #FF9500; }
        .mode-indicator.resizing { background: #FF3B30; }
        .mode-indicator.ready { background: #34C759; }

        .selection-info {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            font-family: monospace;
            display: none;
            pointer-events: none;
            backdrop-filter: blur(10px);
        }

        .crosshair {
            position: absolute;
            pointer-events: none;
            z-index: 999;
            display: none;
        }

        .crosshair::before,
        .crosshair::after {
            content: '';
            position: absolute;
            background: var(--selection-color, #007AFF);
            opacity: 0.6;
        }

        .crosshair::before {
            width: 100vw;
            height: 1px;
            top: 0;
            left: -50vw;
        }

        .crosshair::after {
            width: 1px;
            height: 100vh;
            top: -50vh;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="overlay-container">
        <div class="grid-overlay" id="gridOverlay"></div>

        <div class="instructions" id="instructions">
            <h2>🎯 Advanced Region Selector</h2>
            <p>Click and drag to select a region</p>
            <p>Press <span class="shortcut">G</span> to toggle grid | <span class="shortcut">M</span> to toggle magnifier</p>
            <p>Press <span class="shortcut">C</span> to show crosshair | <span class="shortcut">ESC</span> to exit</p>
        </div>

        <div class="mode-indicator">
            ADVANCED REGION SELECT
        </div>

        <div class="selection-area" id="selectionArea">
            <div class="selection-handles handle-nw" data-handle="nw"></div>
            <div class="selection-handles handle-ne" data-handle="ne"></div>
            <div class="selection-handles handle-sw" data-handle="sw"></div>
            <div class="selection-handles handle-se" data-handle="se"></div>
            <div class="selection-handles handle-n" data-handle="n"></div>
            <div class="selection-handles handle-s" data-handle="s"></div>
            <div class="selection-handles handle-w" data-handle="w"></div>
            <div class="selection-handles handle-e" data-handle="e"></div>
        </div>

        <div class="selection-info" id="selectionInfo">
            Region: 0×0
        </div>

        <div class="magnifier" id="magnifier">
            <canvas id="magnifierCanvas" width="120" height="120"></canvas>
        </div>

        <div class="crosshair" id="crosshair"></div>

        <div class="coordinates-display" id="coordinates">
            <div><span class="label">Mouse:</span> <span id="mousePos">(0, 0)</span></div>
            <div><span class="label">Start:</span> <span id="startPos">(0, 0)</span></div>
            <div><span class="label">End:</span> <span id="endPos">(0, 0)</span></div>
            <div><span class="label">Size:</span> <span id="sizeInfo">0×0</span></div>
            <div><span class="label">Area:</span> <span id="areaInfo">0 px²</span></div>
            <div><span class="label">Ratio:</span> <span id="ratioInfo">0:0</span></div>
        </div>

        <div class="toolbar" id="toolbar">
            <button class="toolbar-btn" onclick="captureSelection()">
                📸 Capture
            </button>
            <button class="toolbar-btn secondary" onclick="copySelection()">
                📋 Copy
            </button>
            <button class="toolbar-btn secondary" onclick="saveSelection()">
                💾 Save
            </button>
            <button class="toolbar-btn danger" onclick="cancelSelection()">
                ❌ Cancel
            </button>
        </div>
    </div>

    <script>
        let isSelecting = false;
        let isResizing = false;
        let startX = 0, startY = 0;
        let currentX = 0, currentY = 0;
        let currentHandle = null;

        // P1-T7: 用户体验优化 - 模式管理
        let currentMode = 'select';
        let modeHistory = [];
        let escapePressed = false;
        let uiConfig = {
            show_coordinates: true,
            show_grid: true,
            show_magnifier: true,
            grid_size: 20,
            selection_color: '#007AFF',
            highlight_color: '#FF6B35'
        };

        const selectionArea = document.getElementById('selectionArea');
        const selectionInfo = document.getElementById('selectionInfo');
        const gridOverlay = document.getElementById('gridOverlay');
        const magnifier = document.getElementById('magnifier');
        const crosshair = document.getElementById('crosshair');
        const coordinates = document.getElementById('coordinates');
        const instructions = document.getElementById('instructions');
        const toolbar = document.getElementById('toolbar');

        // 元素引用
        const mousePos = document.getElementById('mousePos');
        const startPos = document.getElementById('startPos');
        const endPos = document.getElementById('endPos');
        const sizeInfo = document.getElementById('sizeInfo');
        const areaInfo = document.getElementById('areaInfo');
        const ratioInfo = document.getElementById('ratioInfo');

        // 初始化
        initializeOverlay();

        function initializeOverlay() {
            createGrid();
            setupEventListeners();
            setMode('select'); // P1-T7: 设置初始模式

            // 监听UI配置更新
            if (window.__TAURI__) {
                window.__TAURI__.event.listen('ui-config', (event) => {
                    uiConfig = event.payload;
                    applyUIConfig();
                });
            }
        }

        // P1-T7: 模式管理系统
        function setMode(mode) {
            if (currentMode !== mode) {
                modeHistory.push(currentMode);
                currentMode = mode;
                updateVisualFeedback();
                console.log(`[UX] Mode changed to: ${mode}`);
            }
        }

        function updateVisualFeedback() {
            // 更新光标样式
            document.body.className = `mode-${currentMode}`;

            // 更新模式指示器
            const modeIndicator = document.querySelector('.mode-indicator');
            if (modeIndicator) {
                modeIndicator.className = `mode-indicator ${currentMode}`;

                const modeTexts = {
                    'select': 'SELECT MODE',
                    'drag': 'DRAG MODE',
                    'resize': 'RESIZE MODE',
                    'ready': 'READY TO CAPTURE'
                };

                modeIndicator.textContent = modeTexts[currentMode] || 'ADVANCED REGION SELECT';
            }

            // 更新工具栏状态
            updateToolbarState();
        }

        function updateToolbarState() {
            const toolbar = document.getElementById('toolbar');
            if (toolbar) {
                if (currentMode === 'ready') {
                    toolbar.style.display = 'flex';
                } else if (currentMode === 'select') {
                    toolbar.style.display = 'none';
                }
            }
        }

        function previousMode() {
            if (modeHistory.length > 0) {
                const prevMode = modeHistory.pop();
                currentMode = prevMode;
                updateVisualFeedback();
                console.log(`[UX] Reverted to mode: ${prevMode}`);
            }
        }

        function applyUIConfig() {
            // 应用颜色配置
            document.documentElement.style.setProperty('--selection-color', uiConfig.selection_color);
            document.documentElement.style.setProperty('--highlight-color', uiConfig.highlight_color);
            
            // 显示/隐藏组件
            coordinates.style.display = uiConfig.show_coordinates ? 'block' : 'none';
            gridOverlay.style.display = uiConfig.show_grid ? 'block' : 'none';
            
            // 重新创建网格
            if (uiConfig.show_grid) {
                createGrid();
            }
        }

        function createGrid() {
            gridOverlay.innerHTML = '';
            const gridSize = uiConfig.grid_size;
            const width = window.innerWidth;
            const height = window.innerHeight;

            // 创建垂直线
            for (let x = 0; x <= width; x += gridSize) {
                const line = document.createElement('div');
                line.className = 'grid-line vertical';
                line.style.left = x + 'px';
                gridOverlay.appendChild(line);
            }

            // 创建水平线
            for (let y = 0; y <= height; y += gridSize) {
                const line = document.createElement('div');
                line.className = 'grid-line horizontal';
                line.style.top = y + 'px';
                gridOverlay.appendChild(line);
            }
        }

        function setupEventListeners() {
            document.addEventListener('mousedown', startSelection);
            document.addEventListener('mousemove', updateMouse);
            document.addEventListener('mouseup', endSelection);
            document.addEventListener('keydown', handleKeyboard);

            // 处理手柄拖拽
            document.querySelectorAll('.selection-handles').forEach(handle => {
                handle.addEventListener('mousedown', startResize);
            });
        }

        function startSelection(event) {
            if (event.target.closest('.toolbar') || event.target.closest('.selection-handles')) {
                return;
            }

            isSelecting = true;
            startX = event.clientX;
            startY = event.clientY;
            currentX = startX;
            currentY = startY;

            // P1-T7: 模式切换
            setMode('drag');

            instructions.style.display = 'none';
            if (uiConfig.show_coordinates) {
                coordinates.style.display = 'block';
            }

            startPos.textContent = `(${startX}, ${startY})`;
            updateSelectionArea();
        }

        function updateMouse(event) {
            currentX = event.clientX;
            currentY = event.clientY;
            mousePos.textContent = `(${currentX}, ${currentY})`;

            // 更新十字线位置
            crosshair.style.left = currentX + 'px';
            crosshair.style.top = currentY + 'px';

            // 更新放大镜
            if (uiConfig.show_magnifier && magnifier.style.display === 'block') {
                updateMagnifier(currentX, currentY);
            }

            if (isSelecting && !isResizing) {
                updateSelectionArea();
                updateInfo();
            } else if (isResizing) {
                resizeSelection(event);
            }
        }

        function endSelection(event) {
            if (!isSelecting && !isResizing) return;

            if (isResizing) {
                isResizing = false;
                currentHandle = null;
                setMode('ready'); // P1-T7: 调整完成，准备捕获
                return;
            }

            isSelecting = false;
            endPos.textContent = `(${currentX}, ${currentY})`;

            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            if (width > 10 && height > 10) {
                setMode('ready'); // P1-T7: 选择完成，准备捕获
                showToolbar();
                showSelectionHandles();
            } else {
                setMode('select'); // P1-T7: 选择太小，回到选择模式
                cancelSelection();
            }
        }

        function updateSelectionArea() {
            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            selectionArea.style.left = left + 'px';
            selectionArea.style.top = top + 'px';
            selectionArea.style.width = width + 'px';
            selectionArea.style.height = height + 'px';
            selectionArea.style.display = 'block';

            // 更新信息显示位置
            selectionInfo.style.left = (left + width + 10) + 'px';
            selectionInfo.style.top = top + 'px';
            selectionInfo.style.display = 'block';
        }

        function updateInfo() {
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);
            const area = width * height;
            const ratio = height > 0 ? (width / height).toFixed(2) : '0';

            sizeInfo.textContent = `${width}×${height}`;
            areaInfo.textContent = `${area.toLocaleString()} px²`;
            ratioInfo.textContent = `${ratio}:1`;
            selectionInfo.textContent = `Region: ${width}×${height}`;
        }

        function showToolbar() {
            const rect = selectionArea.getBoundingClientRect();
            toolbar.style.left = (rect.left + rect.width / 2 - 150) + 'px';
            toolbar.style.top = (rect.bottom + 15) + 'px';
            toolbar.style.display = 'flex';
        }

        function showSelectionHandles() {
            document.querySelectorAll('.selection-handles').forEach(handle => {
                handle.style.display = 'block';
            });
        }

        function startResize(event) {
            event.stopPropagation();
            isResizing = true;
            currentHandle = event.target.dataset.handle;
            setMode('resize'); // P1-T7: 进入调整模式
        }

        function resizeSelection(event) {
            if (!currentHandle) return;

            const rect = selectionArea.getBoundingClientRect();
            let newLeft = rect.left;
            let newTop = rect.top;
            let newWidth = rect.width;
            let newHeight = rect.height;

            switch (currentHandle) {
                case 'nw':
                    newWidth = rect.right - event.clientX;
                    newHeight = rect.bottom - event.clientY;
                    newLeft = event.clientX;
                    newTop = event.clientY;
                    break;
                case 'ne':
                    newWidth = event.clientX - rect.left;
                    newHeight = rect.bottom - event.clientY;
                    newTop = event.clientY;
                    break;
                case 'sw':
                    newWidth = rect.right - event.clientX;
                    newHeight = event.clientY - rect.top;
                    newLeft = event.clientX;
                    break;
                case 'se':
                    newWidth = event.clientX - rect.left;
                    newHeight = event.clientY - rect.top;
                    break;
                case 'n':
                    newHeight = rect.bottom - event.clientY;
                    newTop = event.clientY;
                    break;
                case 's':
                    newHeight = event.clientY - rect.top;
                    break;
                case 'w':
                    newWidth = rect.right - event.clientX;
                    newLeft = event.clientX;
                    break;
                case 'e':
                    newWidth = event.clientX - rect.left;
                    break;
            }

            if (newWidth > 10 && newHeight > 10) {
                selectionArea.style.left = newLeft + 'px';
                selectionArea.style.top = newTop + 'px';
                selectionArea.style.width = newWidth + 'px';
                selectionArea.style.height = newHeight + 'px';

                // 更新坐标信息
                startX = newLeft;
                startY = newTop;
                currentX = newLeft + newWidth;
                currentY = newTop + newHeight;
                updateInfo();
            }
        }

        function updateMagnifier(x, y) {
            magnifier.style.left = (x + 20) + 'px';
            magnifier.style.top = (y - 140) + 'px';
            
            // TODO: 实现放大镜功能
            // 这需要截取鼠标周围的屏幕内容并放大显示
        }

        function handleKeyboard(event) {
            // P1-T7: 统一的Escape退出机制
            if (event.key === 'Escape') {
                handleEscapeKey();
                return;
            }

            switch (event.key.toLowerCase()) {
                case 'g':
                    toggleGrid();
                    break;
                case 'm':
                    toggleMagnifier();
                    break;
                case 'c':
                    toggleCrosshair();
                    break;
                case 'enter':
                    if (currentMode === 'ready') {
                        captureSelection();
                    }
                    break;
                case 'backspace':
                case 'delete':
                    if (currentMode === 'ready') {
                        cancelSelection();
                    }
                    break;
                case 'tab':
                    event.preventDefault();
                    cycleThroughModes();
                    break;
            }
        }

        // P1-T7: 智能Escape处理 - 统一退出机制
        async function handleEscapeKey() {
            escapePressed = true;

            try {
                if (window.__TAURI__) {
                    const overlayId = window.location.hash.substring(1) || 'advanced_region_overlay';
                    const result = await window.__TAURI__.core.invoke('handle_escape_key', {
                        window_id: overlayId
                    });
                    console.log('ESC key handled:', result);

                    // 根据设计文档：ESC总是退出截图功能
                    if (result === 'close') {
                        closeOverlay();
                    }
                } else {
                    // 降级处理：直接关闭覆盖层
                    closeOverlay();
                }
            } catch (error) {
                console.error('Failed to handle escape key:', error);
                // 降级处理：直接关闭覆盖层
                closeOverlay();
            }

            setTimeout(() => { escapePressed = false; }, 100);
        }

        function cancelCurrentOperation() {
            isSelecting = false;
            isResizing = false;
            currentHandle = null;

            // 隐藏选择区域
            selectionArea.style.display = 'none';
            toolbar.style.display = 'none';
            coordinates.style.display = 'none';
            selectionInfo.style.display = 'none';

            // 隐藏选择手柄
            document.querySelectorAll('.selection-handles').forEach(handle => {
                handle.style.display = 'none';
            });
        }

        function cycleThroughModes() {
            const modes = ['select', 'ready'];
            const currentIndex = modes.indexOf(currentMode);
            const nextIndex = (currentIndex + 1) % modes.length;
            setMode(modes[nextIndex]);
        }

        function toggleGrid() {
            uiConfig.show_grid = !uiConfig.show_grid;
            gridOverlay.style.display = uiConfig.show_grid ? 'block' : 'none';
        }

        function toggleMagnifier() {
            uiConfig.show_magnifier = !uiConfig.show_magnifier;
            magnifier.style.display = uiConfig.show_magnifier ? 'block' : 'none';
        }

        function toggleCrosshair() {
            const isVisible = crosshair.style.display === 'block';
            crosshair.style.display = isVisible ? 'none' : 'block';
        }

        function captureSelection() {
            const rect = selectionArea.getBoundingClientRect();
            const selection = {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height,
                timestamp: Date.now(),
                screen_width: window.innerWidth,
                screen_height: window.innerHeight
            };

            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('handle_region_selection_complete', {
                    selection: selection,
                    action: 'capture'
                }).then(result => {
                    console.log('Region capture completed:', result);
                }).catch(error => {
                    console.error('Failed to capture region:', error);
                });
            }
        }

        function copySelection() {
            const rect = selectionArea.getBoundingClientRect();
            const selection = {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height,
                timestamp: Date.now(),
                screen_width: window.innerWidth,
                screen_height: window.innerHeight
            };

            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('handle_region_selection_complete', {
                    selection: selection,
                    action: 'copy'
                }).then(result => {
                    console.log('Region copy completed:', result);
                }).catch(error => {
                    console.error('Failed to copy region:', error);
                });
            }
        }

        function saveSelection() {
            const rect = selectionArea.getBoundingClientRect();
            const selection = {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height,
                timestamp: Date.now(),
                screen_width: window.innerWidth,
                screen_height: window.innerHeight
            };

            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('handle_region_selection_complete', {
                    selection: selection,
                    action: 'save'
                }).then(result => {
                    console.log('Region save completed:', result);
                }).catch(error => {
                    console.error('Failed to save region:', error);
                });
            }
        }

        function cancelSelection() {
            selectionArea.style.display = 'none';
            toolbar.style.display = 'none';
            coordinates.style.display = 'none';
            selectionInfo.style.display = 'none';
            instructions.style.display = 'block';

            document.querySelectorAll('.selection-handles').forEach(handle => {
                handle.style.display = 'none';
            });

            // P1-T7: 重置到选择模式
            setMode('select');
        }

        function closeOverlay() {
            if (window.__TAURI__) {
                // 重新显示主窗口
                window.__TAURI__.core.invoke('show_main_window').catch(error => {
                    console.error('Failed to show main window:', error);
                });

                // 关闭当前覆盖层
                window.__TAURI__.window.getCurrent().close();
            }
        }

        console.log('Advanced region selector overlay loaded');
    </script>
</body>
</html>
