<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享工具栏组件测试</title>
    
    <!-- 引入共享样式 -->
    <link rel="stylesheet" href="toolbar-styles.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .test-title {
            color: white;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }

        .test-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            font-size: 14px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .control-button {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            margin: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .control-button:hover {
            background: white;
            transform: translateY(-1px);
        }

        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            padding: 10px;
            color: white;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }

        /* 测试区域样式 */
        .test-area {
            position: relative;
            min-height: 400px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }

        /* 模拟overlay环境 */
        .overlay-simulation {
            position: relative;
            width: 100%;
            height: 300px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            overflow: hidden;
        }

        .region-selector {
            position: absolute;
            border: 2px solid #ff6b35;
            background: rgba(255, 107, 53, 0.1);
            left: 50px;
            top: 50px;
            width: 200px;
            height: 100px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🛠️ 共享工具栏组件测试</h1>
        <p class="test-description">测试提取的工具栏组件在不同环境下的可复用性和功能完整性</p>
    </div>

    <!-- 测试1: 独立工具栏模式 -->
    <div class="test-container">
        <h2 class="test-title">测试1: 独立工具栏模式 (Independent Toolbar)</h2>
        <p class="test-description">模拟独立窗口中的工具栏，包含拖拽功能</p>
        
        <div class="control-panel">
            <button class="control-button" onclick="createIndependentToolbar()">创建独立工具栏</button>
            <button class="control-button" onclick="toggleLayout('independent')">切换布局</button>
            <button class="control-button" onclick="removeToolbar('independentToolbar')">移除工具栏</button>
        </div>
        
        <div class="test-area" id="independentTestArea">
            <!-- 独立工具栏将在这里创建 -->
        </div>
        
        <div class="status-display" id="independentStatus">等待创建独立工具栏...</div>
    </div>

    <!-- 测试2: Overlay工具栏模式 -->
    <div class="test-container">
        <h2 class="test-title">测试2: Overlay工具栏模式 (Overlay Toolbar)</h2>
        <p class="test-description">模拟overlay窗口中的工具栏，无拖拽功能，智能定位</p>
        
        <div class="control-panel">
            <button class="control-button" onclick="createOverlayToolbar()">创建Overlay工具栏</button>
            <button class="control-button" onclick="positionToolbar()">重新定位</button>
            <button class="control-button" onclick="removeToolbar('overlayToolbar')">移除工具栏</button>
        </div>
        
        <div class="test-area">
            <div class="overlay-simulation" id="overlayTestArea">
                <div class="region-selector" id="testRegion">
                    <div style="color: white; padding: 10px; font-size: 12px;">模拟选中区域</div>
                </div>
                <!-- Overlay工具栏将在这里创建 -->
            </div>
        </div>
        
        <div class="status-display" id="overlayStatus">等待创建Overlay工具栏...</div>
    </div>

    <!-- 测试3: 功能测试 -->
    <div class="test-container">
        <h2 class="test-title">测试3: 功能测试 (Function Testing)</h2>
        <p class="test-description">测试工具选择、事件处理、状态管理等功能</p>
        
        <div class="control-panel">
            <button class="control-button" onclick="testToolSelection()">测试工具选择</button>
            <button class="control-button" onclick="testKeyboardShortcuts()">测试键盘快捷键</button>
            <button class="control-button" onclick="testEventHandling()">测试事件处理</button>
            <button class="control-button" onclick="clearFunctionTests()">清除测试</button>
        </div>
        
        <div class="status-display" id="functionStatus">等待功能测试...</div>
    </div>

    <!-- 引入共享组件 -->
    <script src="toolbar-component.js"></script>
    <script src="toolbar-functions.js"></script>

    <script>
        // 测试状态
        let testState = {
            independentToolbar: null,
            overlayToolbar: null,
            currentLayout: 'vertical'
        };

        // 日志函数
        function log(message, containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                const timestamp = new Date().toLocaleTimeString();
                container.innerHTML += `[${timestamp}] ${message}\n`;
                container.scrollTop = container.scrollHeight;
            }
        }

        // 创建独立工具栏
        function createIndependentToolbar() {
            const testArea = document.getElementById('independentTestArea');
            
            // 清除现有工具栏
            const existing = document.getElementById('independentToolbar');
            if (existing) existing.remove();

            try {
                const toolbar = window.SharedToolbarComponent.create({
                    containerId: 'independentToolbar',
                    layout: testState.currentLayout,
                    isOverlayMode: false
                });

                testArea.appendChild(toolbar);
                testState.independentToolbar = toolbar;

                // 初始化工具栏功能
                window.SharedToolbar.initialize({
                    isOverlayMode: false,
                    containerId: 'independentToolbar',
                    layout: testState.currentLayout
                });

                log('✅ 独立工具栏创建成功', 'independentStatus');
                log(`📐 布局: ${testState.currentLayout}`, 'independentStatus');
                log('🎯 拖拽功能已启用', 'independentStatus');

            } catch (error) {
                log(`❌ 创建失败: ${error.message}`, 'independentStatus');
            }
        }

        // 创建Overlay工具栏
        function createOverlayToolbar() {
            const testArea = document.getElementById('overlayTestArea');
            
            // 清除现有工具栏
            const existing = document.getElementById('overlayToolbar');
            if (existing) existing.remove();

            try {
                const toolbar = window.SharedToolbarComponent.create({
                    containerId: 'overlayToolbar',
                    layout: 'vertical',
                    isOverlayMode: true
                });

                testArea.appendChild(toolbar);
                testState.overlayToolbar = toolbar;

                // 初始化工具栏功能
                window.SharedToolbar.initialize({
                    isOverlayMode: true,
                    containerId: 'overlayToolbar',
                    layout: 'vertical'
                });

                // 定位到模拟区域旁边
                positionToolbar();

                log('✅ Overlay工具栏创建成功', 'overlayStatus');
                log('📐 布局: vertical (固定)', 'overlayStatus');
                log('🚫 拖拽功能已禁用', 'overlayStatus');

            } catch (error) {
                log(`❌ 创建失败: ${error.message}`, 'overlayStatus');
            }
        }

        // 定位工具栏
        function positionToolbar() {
            const region = document.getElementById('testRegion');
            const toolbar = document.getElementById('overlayToolbar');
            
            if (region && toolbar) {
                const rect = region.getBoundingClientRect();
                const containerRect = document.getElementById('overlayTestArea').getBoundingClientRect();
                
                // 相对于容器的位置
                const x = rect.right - containerRect.left + 10;
                const y = rect.top - containerRect.top;
                
                toolbar.style.left = `${x}px`;
                toolbar.style.top = `${y}px`;
                
                log(`📍 工具栏定位到: (${x}, ${y})`, 'overlayStatus');
            }
        }

        // 切换布局
        function toggleLayout(type) {
            if (type === 'independent' && testState.independentToolbar) {
                testState.currentLayout = testState.currentLayout === 'vertical' ? 'horizontal' : 'vertical';
                window.SharedToolbar.changeLayout(testState.currentLayout, 'independentToolbar');
                log(`🔄 布局切换到: ${testState.currentLayout}`, 'independentStatus');
            }
        }

        // 移除工具栏
        function removeToolbar(toolbarId) {
            window.SharedToolbarComponent.remove(toolbarId);
            
            if (toolbarId === 'independentToolbar') {
                testState.independentToolbar = null;
                log('🗑️ 独立工具栏已移除', 'independentStatus');
            } else if (toolbarId === 'overlayToolbar') {
                testState.overlayToolbar = null;
                log('🗑️ Overlay工具栏已移除', 'overlayStatus');
            }
        }

        // 测试工具选择
        function testToolSelection() {
            log('🧪 开始测试工具选择...', 'functionStatus');
            
            const tools = ['text', 'arrow', 'rectangle', 'circle', 'brush'];
            let index = 0;
            
            const testInterval = setInterval(() => {
                if (index < tools.length) {
                    const tool = tools[index];
                    const button = document.querySelector(`[data-tool="${tool}"]`);
                    
                    if (button) {
                        window.SharedToolbar.handleToolSelection(tool, button);
                        log(`🎯 选择工具: ${tool}`, 'functionStatus');
                    }
                    
                    index++;
                } else {
                    clearInterval(testInterval);
                    log('✅ 工具选择测试完成', 'functionStatus');
                }
            }, 1000);
        }

        // 测试键盘快捷键
        function testKeyboardShortcuts() {
            log('⌨️ 键盘快捷键已设置，请尝试:', 'functionStatus');
            log('  T - 文字工具', 'functionStatus');
            log('  A - 箭头工具', 'functionStatus');
            log('  R - 矩形工具', 'functionStatus');
            log('  C - 圆形工具', 'functionStatus');
            log('  B - 画笔工具', 'functionStatus');
            log('  Ctrl+Z - 撤销', 'functionStatus');
            log('  Ctrl+S - 保存', 'functionStatus');
            log('  ESC - 关闭', 'functionStatus');
        }

        // 测试事件处理
        function testEventHandling() {
            log('📡 测试事件处理...', 'functionStatus');
            
            // 模拟工具栏事件
            if (window.__TAURI__ && window.__TAURI__.event) {
                log('✅ Tauri事件系统可用', 'functionStatus');
            } else {
                log('⚠️ Tauri事件系统不可用 (正常，在浏览器环境中)', 'functionStatus');
            }
            
            log('🔄 事件监听器已设置', 'functionStatus');
        }

        // 清除功能测试
        function clearFunctionTests() {
            document.getElementById('functionStatus').innerHTML = '等待功能测试...';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 共享工具栏组件测试页面已加载', 'independentStatus');
            log('📦 组件文件已引入', 'overlayStatus');
            log('🧪 测试环境准备就绪', 'functionStatus');
        });
    </script>
</body>
</html>
