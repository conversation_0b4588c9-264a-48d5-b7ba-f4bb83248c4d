/**
 * 共享工具栏组件JavaScript函数
 * 从 independent-toolbar.html 提取的可复用JavaScript代码
 * 用于overlay和独立工具栏窗口
 */

// 工具栏状态管理
class SharedToolbarState {
    constructor() {
        this.currentTool = null;
        this.previewWindowId = null;
        this.isConnected = false;
        this.isDragging = false;
        this.toolbarLayout = 'vertical';
        this.dragStartTime = 0;
        this.isOverlayMode = false; // 区分overlay模式和独立窗口模式
        this.eventHandlers = new Map();
    }

    setOverlayMode(enabled) {
        this.isOverlayMode = enabled;
    }

    getCurrentTool() {
        return this.currentTool;
    }

    setCurrentTool(tool) {
        this.currentTool = tool;
    }

    getLayout() {
        return this.toolbarLayout;
    }

    setLayout(layout) {
        this.toolbarLayout = layout;
    }
}

// 全局工具栏状态实例
const sharedToolbarState = new SharedToolbarState();

/**
 * 初始化共享工具栏
 * @param {Object} options - 配置选项
 * @param {boolean} options.isOverlayMode - 是否为overlay模式
 * @param {string} options.containerId - 容器ID
 * @param {string} options.layout - 布局方向 ('vertical' | 'horizontal')
 */
async function initializeSharedToolbar(options = {}) {
    const {
        isOverlayMode = false,
        containerId = 'sharedToolbarContainer',
        layout = 'vertical'
    } = options;

    sharedToolbarState.setOverlayMode(isOverlayMode);
    sharedToolbarState.setLayout(layout);

    // 设置工具按钮事件
    setupSharedToolButtons(containerId);

    // 设置键盘快捷键
    setupSharedKeyboardShortcuts();

    // 设置窗口事件
    setupSharedWindowEvents();

    // 如果不是overlay模式，设置拖拽功能
    if (!isOverlayMode) {
        setupSharedDragFunctionality(containerId);
    }

    // 建立连接
    await establishSharedPreviewConnection();

    console.log('🛠️ Shared toolbar initialized:', {
        isOverlayMode,
        containerId,
        layout
    });
}

/**
 * 设置工具按钮事件
 */
function setupSharedToolButtons(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const buttons = container.querySelectorAll('.shared-toolbar-button');

    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // 检查是否在拖拽过程中或刚完成拖拽
            const timeSinceStart = Date.now() - (sharedToolbarState.dragStartTime || 0);
            if (sharedToolbarState.isDragging || timeSinceStart < 200) {
                e.preventDefault();
                e.stopPropagation();
                return;
            }

            const tool = this.getAttribute('data-tool');
            handleSharedToolSelection(tool, this);
        });
    });
}

/**
 * 处理工具选择
 */
async function handleSharedToolSelection(tool, buttonElement) {
    const buttons = document.querySelectorAll('.shared-toolbar-button');

    // 移除其他按钮的active状态
    buttons.forEach(btn => btn.classList.remove('active'));

    // 🆕 检查是否在区域选择阶段 - 如果是，先触发截图捕获
    if (sharedToolbarState.isOverlayMode && 
        window.appStateManager && 
        window.appStateManager.currentState === 'REGION_PREVIEW') {
        
        console.log('🛠️ Tool selected during region selection phase, triggering annotation mode');
        
        try {
            // 在overlay模式下，直接切换到标注模式
            if (window.appStateManager.setState) {
                window.appStateManager.setState('REGION_ANNOTATION', {
                    selectedTool: tool
                });
            }
            
            // 设置当前工具
            sharedToolbarState.setCurrentTool(tool);
            buttonElement.classList.add('active');
            
            // 触发工具选择事件
            await notifySharedPreviewWindow('tool-selected', { tool: tool });
            
            console.log('🛠️ Switched to annotation mode with tool:', tool);
            return;
            
        } catch (error) {
            console.error('🛠️ Failed to switch to annotation mode:', error);
        }
    }

    // 处理不同类型的工具
    if (['text', 'arrow', 'rectangle', 'circle', 'brush'].includes(tool)) {
        // 绘图工具 - 设置为active状态
        buttonElement.classList.add('active');
        sharedToolbarState.setCurrentTool(tool);

        // 通知预览窗口切换工具
        await notifySharedPreviewWindow('tool-selected', { tool: tool });

    } else if (['undo', 'redo', 'save', 'copy', 'close'].includes(tool)) {
        // 操作工具 - 执行操作
        await executeSharedAction(tool);
    }
}

/**
 * 执行操作
 */
async function executeSharedAction(action) {
    try {
        switch (action) {
            case 'undo':
                await notifySharedPreviewWindow('action-undo', {});
                break;
            case 'redo':
                await notifySharedPreviewWindow('action-redo', {});
                break;
            case 'save':
                await notifySharedPreviewWindow('action-save', {});
                break;
            case 'copy':
                await notifySharedPreviewWindow('action-copy', {});
                break;
            case 'close':
                await notifySharedPreviewWindow('action-close', {});
                
                // 在overlay模式下，返回到检测模式
                if (sharedToolbarState.isOverlayMode && window.appStateManager) {
                    window.appStateManager.setState('DETECTING');
                } else {
                    // 独立窗口模式，关闭窗口
                    if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
                        const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                        await currentWindow.close();
                    }
                }
                break;
        }
    } catch (error) {
        console.error('🛠️ Action execution failed:', error);
    }
}

/**
 * 通知预览窗口
 */
async function notifySharedPreviewWindow(eventType, data) {
    if (!window.__TAURI__ || !window.__TAURI__.event) {
        return;
    }

    try {
        const eventData = {
            type: eventType,
            data: data,
            timestamp: Date.now(),
            toolbarId: sharedToolbarState.isOverlayMode ? 'overlay-toolbar' : 'independent-toolbar',
            isOverlayMode: sharedToolbarState.isOverlayMode
        };

        await window.__TAURI__.event.emit('toolbar-event', eventData);
        
        console.log('🛠️ Notified preview window:', eventData);
    } catch (error) {
        console.error('🛠️ Failed to notify preview window:', error);
    }
}

/**
 * 建立与预览窗口的连接
 */
async function establishSharedPreviewConnection() {
    if (!window.__TAURI__ || !window.__TAURI__.event) {
        return;
    }

    try {
        // 监听来自预览窗口的事件
        await window.__TAURI__.event.listen('preview-event', (event) => {
            handleSharedPreviewEvent(event.payload);
        });

        // 发送连接请求
        await notifySharedPreviewWindow('toolbar-connected', {
            toolbarId: sharedToolbarState.isOverlayMode ? 'overlay-toolbar' : 'independent-toolbar',
            capabilities: ['text', 'arrow', 'rectangle', 'circle', 'brush', 'undo', 'redo', 'save', 'copy'],
            layout: sharedToolbarState.getLayout(),
            isOverlayMode: sharedToolbarState.isOverlayMode
        });

        sharedToolbarState.isConnected = true;
        updateSharedConnectionStatus();
        
        console.log('🛠️ Preview connection established');
    } catch (error) {
        console.error('🛠️ Failed to establish connection:', error);
    }
}

/**
 * 处理来自预览窗口的事件
 */
function handleSharedPreviewEvent(payload) {
    switch (payload.type) {
        case 'tool-feedback':
            updateSharedToolStatus(payload.data);
            break;
        case 'connection-ack':
            sharedToolbarState.isConnected = true;
            sharedToolbarState.previewWindowId = payload.data.windowId;
            updateSharedConnectionStatus();
            break;
        case 'annotation-completed':
            // 标注完成，可以更新工具状态
            break;
    }
}

/**
 * 更新工具状态
 */
function updateSharedToolStatus(statusData) {
    const buttons = document.querySelectorAll('.shared-toolbar-button');
    buttons.forEach(button => {
        const tool = button.getAttribute('data-tool');
        const indicator = button.querySelector('.shared-status-indicator');
        
        if (statusData[tool]) {
            button.classList.add('connected');
        } else {
            button.classList.remove('connected');
        }
    });
}

/**
 * 更新连接状态
 */
function updateSharedConnectionStatus() {
    const buttons = document.querySelectorAll('.shared-toolbar-button');
    buttons.forEach(button => {
        if (sharedToolbarState.isConnected) {
            button.classList.add('connected');
        } else {
            button.classList.remove('connected');
        }
    });
}

/**
 * 改变工具栏布局
 */
function changeSharedLayout(newLayout, containerId = 'sharedToolbarContainer') {
    const container = document.getElementById(containerId);
    if (!container) return;

    if (newLayout === 'horizontal' && sharedToolbarState.getLayout() !== 'horizontal') {
        container.classList.remove('vertical');
        container.classList.add('horizontal');
        sharedToolbarState.setLayout('horizontal');
    } else if (newLayout === 'vertical' && sharedToolbarState.getLayout() !== 'vertical') {
        container.classList.remove('horizontal');
        container.classList.add('vertical');
        sharedToolbarState.setLayout('vertical');
    }
}

/**
 * 设置拖拽功能 (仅独立窗口模式)
 */
function setupSharedDragFunctionality(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const dragHandle = container.querySelector('.shared-drag-handle');
    if (!dragHandle) return;

    // 显示拖拽手柄
    dragHandle.style.display = 'block';

    // 添加拖拽手柄的视觉提示
    dragHandle.style.cursor = 'move';

    // 添加拖拽手柄的悬停效果
    dragHandle.addEventListener('mouseenter', () => {
        dragHandle.style.background = 'rgba(255, 255, 255, 0.1)';
    });

    dragHandle.addEventListener('mouseleave', () => {
        dragHandle.style.background = 'rgba(255, 255, 255, 0.05)';
    });
}

/**
 * 设置键盘快捷键
 */
function setupSharedKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // 在overlay模式下，某些快捷键可能需要不同的处理
        if (sharedToolbarState.isOverlayMode) {
            // overlay模式下的特殊处理
            if (e.key.toLowerCase() === 'escape') {
                e.preventDefault();
                executeSharedAction('close');
                return;
            }
        }

        if (e.ctrlKey || e.metaKey) {
            switch (e.key.toLowerCase()) {
                case 'z':
                    e.preventDefault();
                    if (e.shiftKey) {
                        executeSharedAction('redo');
                    } else {
                        executeSharedAction('undo');
                    }
                    break;
                case 'y':
                    e.preventDefault();
                    executeSharedAction('redo');
                    break;
                case 's':
                    e.preventDefault();
                    executeSharedAction('save');
                    break;
                case 'c':
                    e.preventDefault();
                    executeSharedAction('copy');
                    break;
            }
        } else {
            switch (e.key.toLowerCase()) {
                case 'escape':
                    if (!sharedToolbarState.isOverlayMode) {
                        executeSharedAction('close');
                    }
                    break;
                case 't':
                    handleSharedToolSelection('text', document.querySelector('[data-tool="text"]'));
                    break;
                case 'a':
                    handleSharedToolSelection('arrow', document.querySelector('[data-tool="arrow"]'));
                    break;
                case 'r':
                    handleSharedToolSelection('rectangle', document.querySelector('[data-tool="rectangle"]'));
                    break;
                case 'c':
                    handleSharedToolSelection('circle', document.querySelector('[data-tool="circle"]'));
                    break;
                case 'b':
                    handleSharedToolSelection('brush', document.querySelector('[data-tool="brush"]'));
                    break;
            }
        }
    });
}

/**
 * 设置窗口事件监听
 */
function setupSharedWindowEvents() {
    // 窗口关闭前清理
    window.addEventListener('beforeunload', function() {
        if (sharedToolbarState.isConnected) {
            notifySharedPreviewWindow('toolbar-disconnected', {
                toolbarId: sharedToolbarState.isOverlayMode ? 'overlay-toolbar' : 'independent-toolbar'
            });
        }
    });

    // 窗口失去焦点时的处理
    window.addEventListener('blur', function() {
        // Window lost focus - 可以在这里添加特定逻辑
    });

    // 窗口获得焦点时的处理
    window.addEventListener('focus', function() {
        // Window gained focus - 可以在这里添加特定逻辑
    });
}

/**
 * 显示工具栏
 */
function showSharedToolbar(containerId = 'sharedToolbarContainer') {
    const container = document.getElementById(containerId);
    if (container) {
        container.classList.remove('hidden');
        container.classList.add('animate-in');
    }
}

/**
 * 隐藏工具栏
 */
function hideSharedToolbar(containerId = 'sharedToolbarContainer') {
    const container = document.getElementById(containerId);
    if (container) {
        container.classList.add('hidden');
        container.classList.remove('animate-in');
    }
}

/**
 * 定位工具栏到指定区域附近
 */
function positionSharedToolbar(region, containerId = 'sharedToolbarContainer') {
    const container = document.getElementById(containerId);
    if (!container || !region) return;

    // 计算工具栏位置（区域右侧，垂直居中）
    const toolbarWidth = 60; // 垂直布局宽度
    const toolbarHeight = 300; // 估计高度
    const spacing = 20; // 间距

    let x = region.x + region.width + spacing;
    let y = region.y + (region.height - toolbarHeight) / 2;

    // 确保工具栏不超出屏幕边界
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    if (x + toolbarWidth > screenWidth) {
        // 放在左侧
        x = region.x - toolbarWidth - spacing;
    }

    if (y < 0) {
        y = 0;
    } else if (y + toolbarHeight > screenHeight) {
        y = screenHeight - toolbarHeight;
    }

    // 应用位置
    container.style.left = `${x}px`;
    container.style.top = `${y}px`;

    console.log('🛠️ Toolbar positioned at:', { x, y, region });
}

// 导出函数供外部使用
if (typeof window !== 'undefined') {
    window.SharedToolbar = {
        initialize: initializeSharedToolbar,
        handleToolSelection: handleSharedToolSelection,
        executeAction: executeSharedAction,
        changeLayout: changeSharedLayout,
        show: showSharedToolbar,
        hide: hideSharedToolbar,
        position: positionSharedToolbar,
        setupKeyboardShortcuts: setupSharedKeyboardShortcuts,
        state: sharedToolbarState
    };
}
