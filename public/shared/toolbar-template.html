<!-- 
  共享工具栏组件HTML模板
  从 independent-toolbar.html 提取的可复用HTML结构
  用于overlay和独立工具栏窗口
-->

<!-- 主工具栏容器 -->
<div class="shared-toolbar-container vertical" id="sharedToolbarContainer">
    <!-- 拖拽手柄 (仅独立窗口使用) -->
    <div class="shared-drag-handle" id="sharedDragHandle" data-tauri-drag-region style="display: none;"></div>
    
    <!-- 绘图工具组 -->
    <div class="shared-toolbar-group" id="drawingToolsGroup">
        <button class="shared-toolbar-button" data-tool="text" title="文字标注">
            T
            <div class="shared-status-indicator"></div>
        </button>
        <button class="shared-toolbar-button" data-tool="arrow" title="箭头">
            ↗
            <div class="shared-status-indicator"></div>
        </button>
        <button class="shared-toolbar-button" data-tool="rectangle" title="矩形">
            ▢
            <div class="shared-status-indicator"></div>
        </button>
        <button class="shared-toolbar-button" data-tool="circle" title="圆形">
            ○
            <div class="shared-status-indicator"></div>
        </button>
        <button class="shared-toolbar-button" data-tool="brush" title="画笔">
            ✏
            <div class="shared-status-indicator"></div>
        </button>
    </div>

    <div class="shared-toolbar-separator"></div>

    <!-- 操作工具组 -->
    <div class="shared-toolbar-group" id="actionToolsGroup">
        <button class="shared-toolbar-button" data-tool="undo" title="撤销">
            ↶
            <div class="shared-status-indicator"></div>
        </button>
        <button class="shared-toolbar-button" data-tool="redo" title="重做">
            ↷
            <div class="shared-status-indicator"></div>
        </button>
        <button class="shared-toolbar-button" data-tool="save" title="保存">
            💾
            <div class="shared-status-indicator"></div>
        </button>
        <button class="shared-toolbar-button" data-tool="copy" title="复制">
            📋
            <div class="shared-status-indicator"></div>
        </button>
        <button class="shared-toolbar-button" data-tool="close" title="关闭">
            ✕
            <div class="shared-status-indicator"></div>
        </button>
    </div>
</div>

<!-- 工具栏配置模板 (用于动态创建) -->
<template id="toolbarButtonTemplate">
    <button class="shared-toolbar-button" data-tool="" title="">
        <span class="button-icon"></span>
        <div class="shared-status-indicator"></div>
    </button>
</template>

<!-- 工具栏组模板 -->
<template id="toolbarGroupTemplate">
    <div class="shared-toolbar-group">
        <!-- 按钮将动态添加到这里 -->
    </div>
</template>

<!-- 分隔符模板 -->
<template id="toolbarSeparatorTemplate">
    <div class="shared-toolbar-separator"></div>
</template>

<!-- 工具栏配置数据 (JavaScript对象) -->
<script type="application/json" id="toolbarConfig">
{
    "drawingTools": [
        {
            "tool": "text",
            "icon": "T",
            "title": "文字标注",
            "shortcut": "T"
        },
        {
            "tool": "arrow",
            "icon": "↗",
            "title": "箭头",
            "shortcut": "A"
        },
        {
            "tool": "rectangle",
            "icon": "▢",
            "title": "矩形",
            "shortcut": "R"
        },
        {
            "tool": "circle",
            "icon": "○",
            "title": "圆形",
            "shortcut": "C"
        },
        {
            "tool": "brush",
            "icon": "✏",
            "title": "画笔",
            "shortcut": "B"
        }
    ],
    "actionTools": [
        {
            "tool": "undo",
            "icon": "↶",
            "title": "撤销",
            "shortcut": "Ctrl+Z"
        },
        {
            "tool": "redo",
            "icon": "↷",
            "title": "重做",
            "shortcut": "Ctrl+Y"
        },
        {
            "tool": "save",
            "icon": "💾",
            "title": "保存",
            "shortcut": "Ctrl+S"
        },
        {
            "tool": "copy",
            "icon": "📋",
            "title": "复制",
            "shortcut": "Ctrl+C"
        },
        {
            "tool": "close",
            "icon": "✕",
            "title": "关闭",
            "shortcut": "ESC"
        }
    ],
    "layout": {
        "defaultOrientation": "vertical",
        "defaultPosition": "right",
        "spacing": 6,
        "buttonSize": 44,
        "borderRadius": 12
    }
}
</script>
