/* 
 * 共享工具栏组件样式
 * 从 independent-toolbar.html 提取的可复用CSS样式
 * 用于overlay和独立工具栏窗口
 */

/* 基础重置和字体 */
.toolbar-reset {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    user-select: none;
    -webkit-user-select: none;
}

/* 工具栏容器 */
.shared-toolbar-container {
    background: transparent;
    border-radius: 0;
    padding: 4px;
    display: flex;
    gap: 6px;
    opacity: 1;
    transition: all 0.3s ease;
    z-index: 1000;
    position: absolute;
}

/* 拖拽区域 */
.shared-drag-handle {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: move;
    z-index: 10;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.shared-drag-handle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.shared-drag-handle:active {
    background: rgba(255, 255, 255, 0.15);
}

/* 垂直布局 */
.shared-toolbar-container.vertical {
    flex-direction: column;
    width: 48px;
    height: auto;
    min-height: 420px;
}

/* 水平布局 */
.shared-toolbar-container.horizontal {
    flex-direction: row;
    width: auto;
    min-width: 320px;
    height: 48px;
}

/* 工具栏组 */
.shared-toolbar-group {
    display: flex;
    gap: 4px;
    position: relative;
    z-index: 10;
}

.shared-toolbar-container.vertical .shared-toolbar-group {
    flex-direction: column;
}

.shared-toolbar-container.horizontal .shared-toolbar-group {
    flex-direction: row;
}

/* 工具栏按钮 */
.shared-toolbar-button {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

.shared-toolbar-button:hover {
    background: rgba(255, 107, 53, 0.9);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 107, 53, 0.4);
}

.shared-toolbar-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shared-toolbar-button.active {
    background: rgba(255, 107, 53, 1);
    color: white;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.5);
}

/* 工具栏分隔符 */
.shared-toolbar-separator {
    background: rgba(0, 0, 0, 0.15);
    margin: 4px 0;
}

.shared-toolbar-container.horizontal .shared-toolbar-separator {
    width: 1px;
    height: 32px;
}

.shared-toolbar-container.vertical .shared-toolbar-separator {
    width: 32px;
    height: 1px;
}



/* 状态指示器 */
.shared-status-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.shared-toolbar-button.connected .shared-status-indicator {
    opacity: 1;
}

/* Overlay特定样式 */
.overlay-toolbar-container {
    position: absolute;
    z-index: 999; /* 低于overlay主层但高于其他元素 */
}

/* 隐藏状态 */
.shared-toolbar-container.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.9);
}

/* 响应式设计 */
@media (max-width: 600px) {
    .shared-toolbar-container.horizontal {
        min-width: 240px;
    }

    .shared-toolbar-button {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
}

/* 动画效果 */
@keyframes toolbarFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.shared-toolbar-container.animate-in {
    animation: toolbarFadeIn 0.3s ease-out;
}

/* 工具特定样式 */
.shared-toolbar-button[data-tool="text"] {
    font-family: serif;
    font-weight: bold;
}

.shared-toolbar-button[data-tool="arrow"] {
    font-size: 18px;
}

.shared-toolbar-button[data-tool="rectangle"] {
    font-size: 14px;
}

.shared-toolbar-button[data-tool="circle"] {
    font-size: 18px;
}

.shared-toolbar-button[data-tool="brush"] {
    font-size: 14px;
}

/* 禁用状态 */
.shared-toolbar-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.shared-toolbar-button:disabled:hover {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
