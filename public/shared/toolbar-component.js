/**
 * 共享工具栏组件 - HTML结构动态创建
 * 提供创建和管理工具栏HTML结构的功能
 */

/**
 * 工具栏配置
 */
const TOOLBAR_CONFIG = {
    drawingTools: [
        {
            tool: "text",
            icon: "T",
            title: "文字标注",
            shortcut: "T"
        },
        {
            tool: "arrow",
            icon: "↗",
            title: "箭头",
            shortcut: "A"
        },
        {
            tool: "rectangle",
            icon: "▢",
            title: "矩形",
            shortcut: "R"
        },
        {
            tool: "circle",
            icon: "○",
            title: "圆形",
            shortcut: "C"
        },
        {
            tool: "brush",
            icon: "✏",
            title: "画笔",
            shortcut: "B"
        }
    ],
    actionTools: [
        {
            tool: "undo",
            icon: "↶",
            title: "撤销",
            shortcut: "Ctrl+Z"
        },
        {
            tool: "redo",
            icon: "↷",
            title: "重做",
            shortcut: "Ctrl+Y"
        },
        {
            tool: "save",
            icon: "💾",
            title: "保存",
            shortcut: "Ctrl+S"
        },
        {
            tool: "copy",
            icon: "📋",
            title: "复制",
            shortcut: "Ctrl+C"
        },
        {
            tool: "close",
            icon: "✕",
            title: "关闭",
            shortcut: "ESC"
        }
    ],
    layout: {
        defaultOrientation: "vertical",
        defaultPosition: "right",
        spacing: 6,
        buttonSize: 40,
        borderRadius: 8
    }
};

/**
 * 创建工具栏按钮
 */
function createToolbarButton(toolConfig) {
    const button = document.createElement('button');
    button.className = 'shared-toolbar-button';
    button.setAttribute('data-tool', toolConfig.tool);
    button.setAttribute('title', toolConfig.title);

    // 设置按钮内容
    button.innerHTML = `
        ${toolConfig.icon}
        <div class="shared-status-indicator"></div>
    `;

    return button;
}

/**
 * 创建工具栏组
 */
function createToolbarGroup(tools) {
    const group = document.createElement('div');
    group.className = 'shared-toolbar-group';
    
    tools.forEach(toolConfig => {
        const button = createToolbarButton(toolConfig);
        group.appendChild(button);
    });
    
    return group;
}

/**
 * 创建工具栏分隔符
 */
function createToolbarSeparator() {
    const separator = document.createElement('div');
    separator.className = 'shared-toolbar-separator';
    return separator;
}

/**
 * 创建拖拽手柄
 */
function createDragHandle(isOverlayMode = false) {
    const dragHandle = document.createElement('div');
    dragHandle.className = 'shared-drag-handle';
    dragHandle.id = 'sharedDragHandle';
    
    if (!isOverlayMode) {
        dragHandle.setAttribute('data-tauri-drag-region', '');
        dragHandle.style.display = 'block';
    } else {
        dragHandle.style.display = 'none';
    }
    
    return dragHandle;
}

/**
 * 创建完整的工具栏HTML结构
 */
function createSharedToolbar(options = {}) {
    const {
        containerId = 'sharedToolbarContainer',
        layout = 'vertical',
        isOverlayMode = false,
        includeDrawingTools = true,
        includeActionTools = true,
        customTools = []
    } = options;

    // 创建主容器
    const container = document.createElement('div');
    container.className = `shared-toolbar-container ${layout}`;
    container.id = containerId;

    // 添加拖拽手柄
    const dragHandle = createDragHandle(isOverlayMode);
    container.appendChild(dragHandle);

    // 添加绘图工具组
    if (includeDrawingTools) {
        const drawingGroup = createToolbarGroup(TOOLBAR_CONFIG.drawingTools);
        drawingGroup.id = 'drawingToolsGroup';
        container.appendChild(drawingGroup);
    }

    // 添加自定义工具
    if (customTools.length > 0) {
        const customGroup = createToolbarGroup(customTools);
        customGroup.id = 'customToolsGroup';
        container.appendChild(customGroup);
    }

    // 添加分隔符
    if ((includeDrawingTools || customTools.length > 0) && includeActionTools) {
        const separator = createToolbarSeparator();
        container.appendChild(separator);
    }

    // 添加操作工具组
    if (includeActionTools) {
        const actionGroup = createToolbarGroup(TOOLBAR_CONFIG.actionTools);
        actionGroup.id = 'actionToolsGroup';
        container.appendChild(actionGroup);
    }

    return container;
}

/**
 * 将工具栏插入到指定的父元素中
 */
function insertSharedToolbar(parentElement, options = {}) {
    if (typeof parentElement === 'string') {
        parentElement = document.getElementById(parentElement);
    }
    
    if (!parentElement) {
        console.error('🛠️ Parent element not found for toolbar insertion');
        return null;
    }

    const toolbar = createSharedToolbar(options);
    parentElement.appendChild(toolbar);
    
    console.log('🛠️ Shared toolbar inserted into:', parentElement.id || parentElement.tagName);
    return toolbar;
}

/**
 * 从HTML模板创建工具栏
 */
function createToolbarFromTemplate(templateId = 'toolbarTemplate') {
    const template = document.getElementById(templateId);
    if (!template) {
        console.error('🛠️ Toolbar template not found:', templateId);
        return null;
    }

    const clone = template.content.cloneNode(true);
    return clone;
}

/**
 * 更新工具栏布局
 */
function updateToolbarLayout(containerId, newLayout) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // 移除旧的布局类
    container.classList.remove('vertical', 'horizontal');
    
    // 添加新的布局类
    container.classList.add(newLayout);
    
    console.log('🛠️ Toolbar layout updated to:', newLayout);
}

/**
 * 添加工具栏到overlay窗口
 */
function addToolbarToOverlay(overlaySelector = 'body', options = {}) {
    const overlayElement = document.querySelector(overlaySelector);
    if (!overlayElement) {
        console.error('🛠️ Overlay element not found:', overlaySelector);
        return null;
    }

    // 确保是overlay模式
    const overlayOptions = {
        ...options,
        isOverlayMode: true,
        containerId: options.containerId || 'overlayToolbarContainer'
    };

    const toolbar = createSharedToolbar(overlayOptions);
    toolbar.classList.add('overlay-toolbar-container');
    
    overlayElement.appendChild(toolbar);
    
    console.log('🛠️ Toolbar added to overlay');
    return toolbar;
}

/**
 * 移除工具栏
 */
function removeSharedToolbar(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.remove();
        console.log('🛠️ Toolbar removed:', containerId);
    }
}

/**
 * 获取工具栏配置
 */
function getToolbarConfig() {
    return TOOLBAR_CONFIG;
}

/**
 * 设置自定义工具栏配置
 */
function setToolbarConfig(newConfig) {
    Object.assign(TOOLBAR_CONFIG, newConfig);
}

// 导出函数供外部使用
if (typeof window !== 'undefined') {
    window.SharedToolbarComponent = {
        create: createSharedToolbar,
        insert: insertSharedToolbar,
        addToOverlay: addToolbarToOverlay,
        remove: removeSharedToolbar,
        updateLayout: updateToolbarLayout,
        createFromTemplate: createToolbarFromTemplate,
        getConfig: getToolbarConfig,
        setConfig: setToolbarConfig,
        createButton: createToolbarButton,
        createGroup: createToolbarGroup
    };
}
