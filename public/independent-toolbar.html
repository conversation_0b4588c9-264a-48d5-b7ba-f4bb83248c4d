<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Toolbar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: transparent;
            overflow: visible;
            user-select: none;
            -webkit-user-select: none;
            cursor: default;
        }

        .toolbar-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            border-radius: 0;
            padding: 4px;
            display: flex;
            gap: 6px;
            opacity: 1;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        /* 拖拽区域 */
        .drag-handle {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: move;
            z-index: 10;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .drag-handle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .drag-handle:active {
            background: rgba(255, 255, 255, 0.15);
        }

        .toolbar-container.vertical {
            flex-direction: column;
            width: 48px;
            height: auto;
            min-height: 420px;
        }

        .toolbar-container.horizontal {
            flex-direction: row;
            width: auto;
            min-width: 320px;
            height: 48px;
        }

        .toolbar-group {
            display: flex;
            gap: 4px;
            position: relative;
            z-index: 10;
        }

        .toolbar-container.vertical .toolbar-group {
            flex-direction: column;
        }

        .toolbar-container.horizontal .toolbar-group {
            flex-direction: row;
        }

        .toolbar-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 10;
        }

        .toolbar-button:hover {
            background: rgba(255, 107, 53, 0.9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 107, 53, 0.4);
        }

        .toolbar-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .toolbar-button.active {
            background: rgba(255, 107, 53, 1);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.5);
        }

        .toolbar-separator {
            background: rgba(0, 0, 0, 0.15);
            margin: 4px 0;
        }

        .toolbar-container.horizontal .toolbar-separator {
            width: 1px;
            height: 32px;
        }

        .toolbar-container.vertical .toolbar-separator {
            width: 32px;
            height: 1px;
        }



        /* 状态指示器 */
        .status-indicator {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .toolbar-button.connected .status-indicator {
            opacity: 1;
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .toolbar-container.horizontal {
                min-width: 240px;
            }

            .toolbar-button {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }
        }

        /* 🆕 注解模式样式 */
        .toolbar.annotation-mode {
            border: 2px solid #007AFF;
            box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
        }

        .toolbar.annotation-mode .toolbar-button.active {
            background: #007AFF;
            color: white;
            box-shadow: 0 0 10px rgba(0, 122, 255, 0.5);
        }

        .toolbar.annotation-mode .toolbar-button:hover {
            background: rgba(0, 122, 255, 0.1);
        }

        .toolbar-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .toolbar-button.disabled:hover {
            background: transparent !important;
        }

        .annotation-indicator {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 10px;
            height: 10px;
            background: #007AFF;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }
    </style>
</head>
<body>


    <div class="toolbar-container vertical" id="toolbarContainer">
        <!-- 拖拽手柄 -->
        <div class="drag-handle" id="dragHandle" data-tauri-drag-region></div>
        
        <!-- 绘图工具组 -->
        <div class="toolbar-group">
            <button class="toolbar-button" data-tool="text" title="文字标注">
                T
                <div class="status-indicator"></div>
            </button>
            <button class="toolbar-button" data-tool="arrow" title="箭头">
                ↗
                <div class="status-indicator"></div>
            </button>
            <button class="toolbar-button" data-tool="rectangle" title="矩形">
                ▢
                <div class="status-indicator"></div>
            </button>
            <button class="toolbar-button" data-tool="circle" title="圆形">
                ○
                <div class="status-indicator"></div>
            </button>
            <button class="toolbar-button" data-tool="brush" title="画笔">
                ✏
                <div class="status-indicator"></div>
            </button>
        </div>

        <div class="toolbar-separator"></div>

        <!-- 操作工具组 -->
        <div class="toolbar-group">
            <button class="toolbar-button" data-tool="undo" title="撤销">
                ↶
                <div class="status-indicator"></div>
            </button>
            <button class="toolbar-button" data-tool="redo" title="重做">
                ↷
                <div class="status-indicator"></div>
            </button>
            <button class="toolbar-button" data-tool="save" title="保存">
                💾
                <div class="status-indicator"></div>
            </button>
            <button class="toolbar-button" data-tool="copy" title="确定">
                ✔
                <div class="status-indicator"></div>
            </button>
            <button class="toolbar-button" data-tool="close" title="关闭">
                ✕
                <div class="status-indicator"></div>
            </button>
        </div>
    </div>

    <script>
        
        // 全局状态
        let currentTool = null;
        let previewWindowId = null;
        let isConnected = false;
        let isDragging = false;
        let toolbarLayout = 'vertical'; // 默认垂直布局
        let dragStartTime = 0;

        // 🆕 工具栏配置状态
        let toolbarConfig = {
            toolbarId: null,
            previewWindowId: null,
            config: null
        };

        // 🆕 注解模式状态
        let annotationMode = false;
        let annotationWindowId = null;
        let drawingState = {
            isDrawing: false,
            canUndo: false,
            canRedo: false,
            activeAnnotations: 0
        };
        let currentStyle = {
            strokeColor: '#FF0000',
            strokeWidth: 2,
            fillColor: null,
            fontSize: 16,
            fontFamily: 'Arial'
        };
        
        // 🔧 ENHANCED: 初始化工具栏（增强版本）
        async function initializeToolbar() {
            // 🆕 设置工具栏初始化事件监听器
            setupToolbarInitListener();

            // 设置拖拽功能
            setupDragFunctionality();

            // 设置工具按钮事件
            setupToolButtons();

            // 设置键盘快捷键
            setupKeyboardShortcuts();

            // 设置窗口事件
            setupWindowEvents();

            // 🆕 设置智能焦点管理
            setupSmartFocusManagement();

            // 建立与预览窗口的连接
            await establishPreviewConnection();

            // 🆕 启动注解窗口监控
            startAnnotationWindowMonitoring();
        }

        // 🆕 启动注解窗口监控
        function startAnnotationWindowMonitoring() {
            // 定期检查注解窗口状态
            setInterval(async () => {
                if (annotationMode && !annotationWindowId) {
                    console.log('🎨 Annotation mode active but no window ID, attempting detection...');
                    await detectAnnotationWindow();
                }
            }, 5000); // 每5秒检查一次

            // 监听注解窗口关闭事件
            if (window.__TAURI__ && window.__TAURI__.event) {
                window.__TAURI__.event.listen('annotation-window-closed', (event) => {
                    if (event.payload.windowId === annotationWindowId) {
                        console.log('🎨 Annotation window closed, resetting state');
                        handleAnnotationWindowClosed();
                    }
                });
            }
        }

        // 🆕 处理注解窗口关闭
        function handleAnnotationWindowClosed() {
            annotationWindowId = null;

            if (annotationMode) {
                // 如果还在注解模式，尝试重新连接或退出模式
                console.log('🎨 Annotation window closed unexpectedly, exiting annotation mode');
                endAnnotationMode();
            }
        }

        // 🆕 设置工具栏初始化事件监听器
        function setupToolbarInitListener() {
            if (window.__TAURI__) {
                window.__TAURI__.event.listen('toolbar-init', (event) => {
                    console.log('🛠️ Received toolbar initialization event:', event.payload);

                    // 更新工具栏配置
                    toolbarConfig.toolbarId = event.payload.toolbarId;
                    toolbarConfig.previewWindowId = event.payload.previewWindowId;
                    toolbarConfig.config = event.payload.config;

                    // 同时更新全局变量以保持兼容性
                    previewWindowId = event.payload.previewWindowId;

                    console.log('🛠️ Toolbar config updated:', {
                        toolbarId: toolbarConfig.toolbarId,
                        previewWindowId: toolbarConfig.previewWindowId,
                        isRegionSelection: toolbarConfig.previewWindowId === 'region_selection'
                    });

                    // 如果是区域选择阶段，显示特殊提示
                    if (toolbarConfig.previewWindowId === 'region_selection') {
                        console.log('🛠️ Toolbar initialized for region selection phase');
                        updateToolbarForRegionSelection();
                    }
                });

                console.log('🛠️ Toolbar initialization listener setup completed');
            }
        }

        // 🆕 为区域选择阶段更新工具栏
        function updateToolbarForRegionSelection() {
            // 添加视觉提示表明这是区域选择阶段
            const toolbar = document.querySelector('.toolbar-container');
            if (toolbar) {
                toolbar.classList.add('region-selection-mode');

                // 可以添加一个小提示
                const hint = document.createElement('div');
                hint.className = 'region-selection-hint';
                hint.textContent = 'Select tool to capture';
                hint.style.cssText = `
                    position: absolute;
                    top: -25px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0,0,0,0.8);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 11px;
                    white-space: nowrap;
                    pointer-events: none;
                    z-index: 1000;
                `;
                toolbar.appendChild(hint);

                // 3秒后移除提示
                setTimeout(() => {
                    if (hint.parentNode) {
                        hint.parentNode.removeChild(hint);
                    }
                }, 3000);
            }
        }

        // 设置拖拽功能
        function setupDragFunctionality() {
            const dragHandle = document.getElementById('dragHandle');

            // 添加拖拽手柄的视觉提示
            dragHandle.style.cursor = 'move';
            dragHandle.style.background = 'rgba(255, 255, 255, 0.05)';
            dragHandle.style.borderRadius = '4px';
            dragHandle.style.transition = 'background-color 0.2s ease';

            // 添加拖拽手柄的悬停效果
            dragHandle.addEventListener('mouseenter', () => {
                dragHandle.style.background = 'rgba(255, 255, 255, 0.1)';
            });

            dragHandle.addEventListener('mouseleave', () => {
                dragHandle.style.background = 'rgba(255, 255, 255, 0.05)';
            });
        }




        
        // 设置工具按钮事件
        function setupToolButtons() {
            const buttons = document.querySelectorAll('.toolbar-button');

            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // 检查是否在拖拽过程中或刚完成拖拽
                    const timeSinceStart = Date.now() - (window.dragStartTime || 0);
                    if (isDragging || timeSinceStart < 200) {
                        e.preventDefault();
                        e.stopPropagation();
                        return;
                    }

                    const tool = this.getAttribute('data-tool');
                    handleToolSelection(tool, this);
                });
            });
        }
        
        // 处理工具选择
        async function handleToolSelection(tool, buttonElement) {
            const buttons = document.querySelectorAll('.toolbar-button');

            // 移除其他按钮的active状态
            buttons.forEach(btn => btn.classList.remove('active'));

            // 🆕 立即更新光标状态
            updateToolbarCursor();

            // 🔧 ENHANCED: 检查是否在区域选择阶段 - 如果是，先触发截图捕获
            if (toolbarConfig.previewWindowId === 'region_selection') {
                console.log('🛠️ Tool selected during region selection phase, triggering capture first');
                console.log('🛠️ Toolbar config:', toolbarConfig);

                try {
                    // 通知overlay窗口触发截图捕获
                    await window.__TAURI__.core.invoke('trigger_region_capture_from_toolbar', {
                        selectedTool: tool
                    });

                    console.log('🛠️ Region capture triggered successfully, tool will be applied after capture');

                    // 设置按钮为active状态以提供视觉反馈
                    buttonElement.classList.add('active');
                    currentTool = tool;

                    return; // 捕获完成后，新的预览窗口会重新创建工具栏并应用工具

                } catch (error) {
                    console.error('🛠️ Failed to trigger region capture:', error);
                    // 如果捕获失败，继续正常的工具选择流程
                }
            }

            // 处理不同类型的工具
            if (['text', 'arrow', 'rectangle', 'circle', 'brush'].includes(tool)) {
                // 🆕 绘图工具 - 检查当前连接的窗口类型
                console.log('🎨 Drawing tool selected:', tool, 'Preview window:', previewWindowId);

                if (previewWindowId === 'screenshot-preview') {
                    // Screenshot preview窗口 - 发送注解模式启动事件
                    console.log('🎨 Sending annotation-mode-start to screenshot preview');
                    await notifyPreviewWindow('annotation-mode-start', {
                        tool: tool,
                        style: currentStyle,
                        timestamp: Date.now()
                    });
                } else if (!annotationMode) {
                    // 其他情况 - 启动注解模式（创建新窗口）
                    await startAnnotationMode(tool);
                } else {
                    // 已在注解模式，切换工具
                    await switchDrawingTool(tool);
                }

                // 设置为active状态
                buttonElement.classList.add('active');
                currentTool = tool;

            } else if (['undo', 'redo', 'save', 'copy', 'close'].includes(tool)) {
                // 操作工具 - 执行操作
                await executeAction(tool);
            }
        }
        
        // 🆕 启动注解模式
        async function startAnnotationMode(selectedTool) {
            try {
                console.log('🎨 Starting annotation mode with tool:', selectedTool);

                // 首先尝试检测现有的注解窗口
                const annotationWindowDetected = await detectAnnotationWindow();

                if (annotationWindowDetected) {
                    console.log('🎨 Existing annotation window detected, connecting...');
                    await connectToAnnotationWindow(selectedTool);
                } else {
                    console.log('🎨 No annotation window found, requesting creation...');
                    // 发送注解模式启动事件到预览窗口，请求创建注解窗口
                    await notifyPreviewWindow('annotation-mode-start', {
                        tool: selectedTool,
                        style: currentStyle,
                        timestamp: Date.now()
                    });
                }

                annotationMode = true;
                updateToolbarForAnnotationMode();

                console.log('🎨 Annotation mode started successfully');
            } catch (error) {
                console.error('🎨 Failed to start annotation mode:', error);
            }
        }

        // 🆕 检测注解窗口
        async function detectAnnotationWindow() {
            try {
                // 发送检测事件，等待响应
                await window.__TAURI__.event.emit('annotation-window-detect', {
                    toolbarId: 'independent-toolbar',
                    timestamp: Date.now()
                });

                // 等待短时间看是否有注解窗口响应
                return new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        resolve(false);
                    }, 1000); // 1秒超时

                    const listener = window.__TAURI__.event.listen('annotation-window-found', (event) => {
                        clearTimeout(timeout);
                        annotationWindowId = event.payload.windowId;
                        console.log('🎨 Annotation window found:', annotationWindowId);
                        listener.then(unlisten => unlisten()); // 清理监听器
                        resolve(true);
                    });
                });
            } catch (error) {
                console.error('🎨 Failed to detect annotation window:', error);
                return false;
            }
        }

        // 🆕 连接到注解窗口
        async function connectToAnnotationWindow(selectedTool) {
            try {
                console.log('🎨 Connecting to annotation window:', annotationWindowId);

                // 发送连接请求到注解窗口
                await notifyAnnotationWindow('toolbar-connect', {
                    toolbarId: 'independent-toolbar',
                    selectedTool: selectedTool,
                    style: currentStyle,
                    capabilities: ['text', 'arrow', 'rectangle', 'circle', 'brush', 'undo', 'redo', 'save', 'copy'],
                    layout: toolbarLayout
                });

                console.log('🎨 Connection request sent to annotation window');
            } catch (error) {
                console.error('🎨 Failed to connect to annotation window:', error);
            }
        }

        // 🆕 切换绘图工具
        async function switchDrawingTool(tool) {
            try {
                console.log('🎨 Switching drawing tool to:', tool);

                // 清除之前工具的active状态
                const buttons = document.querySelectorAll('.toolbar-button');
                buttons.forEach(btn => {
                    if (['text', 'arrow', 'rectangle', 'circle', 'brush'].includes(btn.getAttribute('data-tool'))) {
                        btn.classList.remove('active');
                    }
                });

                // 发送工具选择事件
                await notifyAnnotationWindow('drawing-tool-selected', {
                    tool: tool,
                    previousTool: currentTool,
                    style: currentStyle
                });

                console.log('🎨 Drawing tool switched successfully');
            } catch (error) {
                console.error('🎨 Failed to switch drawing tool:', error);
            }
        }

        // 🆕 更新工具栏为注解模式
        function updateToolbarForAnnotationMode() {
            // 更新工具栏样式以指示注解模式
            const toolbar = document.querySelector('.toolbar');
            if (toolbar) {
                toolbar.classList.add('annotation-mode');
                console.log('🎨 Toolbar updated for annotation mode');
            } else {
                console.warn('🎨 Toolbar element not found, retrying...');
                // 延迟重试，确保DOM已加载
                setTimeout(() => {
                    const retryToolbar = document.querySelector('.toolbar');
                    if (retryToolbar) {
                        retryToolbar.classList.add('annotation-mode');
                        console.log('🎨 Toolbar updated for annotation mode (retry)');
                    } else {
                        console.error('🎨 Toolbar element still not found after retry');
                    }
                }, 100);
            }

            // 更新按钮状态
            updateDrawingStateUI();
        }

        // 🆕 更新绘图状态UI
        function updateDrawingStateUI() {
            // 更新撤销/重做按钮状态
            const undoBtn = document.querySelector('[data-tool="undo"]');
            const redoBtn = document.querySelector('[data-tool="redo"]');

            if (undoBtn) {
                undoBtn.disabled = !drawingState.canUndo;
                undoBtn.classList.toggle('disabled', !drawingState.canUndo);
            }

            if (redoBtn) {
                redoBtn.disabled = !drawingState.canRedo;
                redoBtn.classList.toggle('disabled', !drawingState.canRedo);
            }
        }

        // 执行操作
        async function executeAction(action) {
            try {
                switch (action) {
                    case 'undo':
                        if (annotationMode) {
                            await notifyAnnotationWindow('action-undo', {});
                        } else {
                            await notifyPreviewWindow('action-undo', {});
                        }
                        break;
                    case 'redo':
                        if (annotationMode) {
                            await notifyAnnotationWindow('action-redo', {});
                        } else {
                            await notifyPreviewWindow('action-redo', {});
                        }
                        break;
                    case 'save':
                        if (annotationMode) {
                            await notifyAnnotationWindow('action-save', {});
                        } else {
                            await notifyPreviewWindow('action-save', {});
                        }
                        break;
                    case 'copy':
                        if (annotationMode) {
                            await notifyAnnotationWindow('action-copy', {});
                        } else {
                            await notifyPreviewWindow('action-copy', {});
                        }
                        break;
                    case 'close':
                        if (annotationMode) {
                            await endAnnotationMode();
                        } else {
                            await notifyPreviewWindow('action-close', {});
                        }
                        // 关闭工具栏窗口
                        if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
                            const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                            await currentWindow.close();
                        }
                        break;
                }
            } catch (error) {
                // Action execution failed
            }
        }

        // 🆕 结束注解模式
        async function endAnnotationMode() {
            try {
                console.log('🎨 Ending annotation mode');

                // 发送注解模式结束事件
                await notifyAnnotationWindow('annotation-mode-end', {
                    saveAnnotations: true,
                    timestamp: Date.now()
                });

                annotationMode = false;
                annotationWindowId = null;
                currentTool = null;

                // 重置工具栏状态
                const toolbar = document.querySelector('.toolbar');
                toolbar.classList.remove('annotation-mode');

                // 清除所有active状态
                const buttons = document.querySelectorAll('.toolbar-button');
                buttons.forEach(btn => btn.classList.remove('active'));

                console.log('🎨 Annotation mode ended successfully');
            } catch (error) {
                console.error('🎨 Failed to end annotation mode:', error);
            }
        }
        
        // 通知预览窗口
        async function notifyPreviewWindow(eventType, data) {
            if (!window.__TAURI__ || !window.__TAURI__.event) {
                return;
            }

            try {
                await window.__TAURI__.event.emit('toolbar-event', {
                    type: eventType,
                    data: data,
                    timestamp: Date.now(),
                    toolbarId: 'independent-toolbar'
                });
            } catch (error) {
                // Failed to notify preview window
            }
        }

        // 🆕 通知注解窗口
        async function notifyAnnotationWindow(eventType, data) {
            if (!window.__TAURI__ || !window.__TAURI__.event) {
                return;
            }

            try {
                // 🔧 CRITICAL FIX: Send 'toolbar-event' instead of 'annotation-event'
                // to match the event name that preview window is listening for
                await window.__TAURI__.event.emit('toolbar-event', {
                    type: eventType,
                    data: data,
                    timestamp: Date.now(),
                    toolbarId: 'independent-toolbar',
                    targetWindow: annotationWindowId
                });
                console.log('🎨 Annotation event sent:', eventType, data);
            } catch (error) {
                console.error('🎨 Failed to notify annotation window:', error);
            }
        }
        
        // 建立与预览窗口的连接
        async function establishPreviewConnection() {
            if (!window.__TAURI__ || !window.__TAURI__.event) {
                return;
            }

            try {
                // 监听来自预览窗口的事件
                await window.__TAURI__.event.listen('preview-event', (event) => {
                    handlePreviewEvent(event.payload);
                });

                // 🆕 监听来自注解窗口的事件
                await window.__TAURI__.event.listen('annotation-response', (event) => {
                    handleAnnotationEvent(event.payload);
                });

                // 发送连接请求
                await notifyPreviewWindow('toolbar-connected', {
                    toolbarId: 'independent-toolbar',
                    capabilities: ['text', 'arrow', 'rectangle', 'circle', 'brush', 'undo', 'redo', 'save', 'copy'],
                    layout: toolbarLayout
                });

                isConnected = true;
                updateConnectionStatus();
            } catch (error) {
                // Failed to establish connection
            }
        }
        
        // 处理来自预览窗口的事件
        function handlePreviewEvent(payload) {
            switch (payload.type) {
                case 'tool-feedback':
                    // 工具反馈，更新UI状态
                    updateToolStatus(payload.data);
                    break;
                case 'undo-redo-sync':
                    // 🔧 CRITICAL FIX: Handle undo/redo state sync specifically
                    console.log('[TOOLBAR] 🔧 Received undo-redo-sync:', payload.data);
                    if (payload.data.state) {
                        drawingState.canUndo = payload.data.state.canUndo || false;
                        drawingState.canRedo = payload.data.state.canRedo || false;
                        console.log('[TOOLBAR] 🔧 Undo/Redo sync updated:', {
                            canUndo: drawingState.canUndo,
                            canRedo: drawingState.canRedo,
                            historyLength: payload.data.history?.length || 0,
                            redoStackLength: payload.data.redoStack?.length || 0
                        });
                        updateDrawingStateUI();
                    }
                    break;
                case 'connection-ack':
                    // 连接确认
                    isConnected = true;
                    previewWindowId = payload.data.windowId;
                    updateConnectionStatus();
                    break;
                case 'annotation-completed':
                    // 标注完成，可以更新工具状态
                    break;
            }
        }

        // 🆕 处理来自注解窗口的事件
        function handleAnnotationEvent(payload) {
            console.log('🎨 Received annotation event:', payload.type, payload.data);

            switch (payload.type) {
                case 'annotation-ready':
                    // 注解窗口准备就绪
                    annotationWindowId = payload.data.windowId;
                    console.log('🎨 Annotation window ready:', annotationWindowId);

                    // 如果有当前选择的工具，发送给注解窗口
                    if (currentTool) {
                        switchDrawingTool(currentTool);
                    }
                    break;

                case 'drawing-state-change':
                    // 绘图状态变化
                    drawingState = { ...drawingState, ...payload.data };
                    updateDrawingStateUI();
                    break;

                case 'annotation-data-update':
                    // 注解数据更新
                    if (payload.data.totalCount !== undefined) {
                        drawingState.activeAnnotations = payload.data.totalCount;
                    }
                    break;

                case 'tool-capability-update':
                    // 工具能力更新
                    updateToolCapabilities(payload.data);
                    break;

                case 'connection-ack':
                    // 注解窗口连接确认
                    annotationWindowId = payload.data.windowId;
                    console.log('🎨 Annotation window connected:', annotationWindowId);

                    // 发送当前工具状态
                    if (currentTool) {
                        switchDrawingTool(currentTool);
                    }
                    break;

                case 'window-detection-response':
                    // 窗口检测响应
                    if (payload.data.exists) {
                        annotationWindowId = payload.data.windowId;
                        console.log('🎨 Annotation window detected via response:', annotationWindowId);
                    }
                    break;

                case 'annotation-window-created':
                    // 新注解窗口创建通知
                    annotationWindowId = payload.data.windowId;
                    console.log('🎨 New annotation window created:', annotationWindowId);

                    // 自动连接到新创建的窗口
                    if (currentTool) {
                        connectToAnnotationWindow(currentTool);
                    }
                    break;

                default:
                    console.log('🎨 Unhandled annotation event:', payload.type);
            }
        }

        // 🆕 更新工具能力
        function updateToolCapabilities(capabilities) {
            const buttons = document.querySelectorAll('.toolbar-button');
            buttons.forEach(button => {
                const tool = button.getAttribute('data-tool');
                if (capabilities[tool]) {
                    button.disabled = !capabilities[tool].enabled;
                    button.classList.toggle('disabled', !capabilities[tool].enabled);
                }
            });
        }

        // 🆕 验证注解窗口连接
        async function validateAnnotationConnection() {
            if (!annotationWindowId) {
                return false;
            }

            try {
                // 发送ping事件测试连接
                await notifyAnnotationWindow('connection-ping', {
                    timestamp: Date.now()
                });

                // 等待pong响应
                return new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        resolve(false);
                    }, 2000); // 2秒超时

                    const listener = window.__TAURI__.event.listen('annotation-response', (event) => {
                        if (event.payload.type === 'connection-pong' &&
                            event.payload.data.windowId === annotationWindowId) {
                            clearTimeout(timeout);
                            listener.then(unlisten => unlisten());
                            resolve(true);
                        }
                    });
                });
            } catch (error) {
                console.error('🎨 Failed to validate annotation connection:', error);
                return false;
            }
        }

        // 🆕 获取注解窗口状态
        async function getAnnotationWindowStatus() {
            return {
                annotationMode: annotationMode,
                windowId: annotationWindowId,
                isConnected: annotationWindowId !== null,
                currentTool: currentTool,
                drawingState: drawingState,
                style: currentStyle
            };
        }
        
        // 更新工具状态
        function updateToolStatus(statusData) {
            console.log('[TOOLBAR] 🔧 Updating tool status:', statusData);

            const buttons = document.querySelectorAll('.toolbar-button');
            buttons.forEach(button => {
                const tool = button.getAttribute('data-tool');
                const indicator = button.querySelector('.status-indicator');

                if (statusData.toolStatus && statusData.toolStatus[tool]) {
                    button.classList.add('connected');
                } else {
                    button.classList.remove('connected');
                }
            });

            // 🔧 CRITICAL FIX: Update undo/redo state from undoRedoState
            if (statusData.undoRedoState) {
                const prevCanUndo = drawingState.canUndo;
                const prevCanRedo = drawingState.canRedo;

                drawingState.canUndo = statusData.undoRedoState.canUndo || false;
                drawingState.canRedo = statusData.undoRedoState.canRedo || false;

                console.log('[TOOLBAR] 🔧 Undo/Redo state updated:', {
                    canUndo: `${prevCanUndo} → ${drawingState.canUndo}`,
                    canRedo: `${prevCanRedo} → ${drawingState.canRedo}`,
                    historyLength: statusData.undoRedoState.historyLength,
                    redoStackLength: statusData.undoRedoState.redoStackLength
                });

                // Update UI immediately
                updateDrawingStateUI();
            }
        }
        
        // 更新连接状态
        function updateConnectionStatus() {
            const buttons = document.querySelectorAll('.toolbar-button');
            buttons.forEach(button => {
                if (isConnected) {
                    button.classList.add('connected');
                } else {
                    button.classList.remove('connected');
                }
            });
        }



        // 改变工具栏布局
        function changeLayout(newLayout) {
            const container = document.getElementById('toolbarContainer');

            if (newLayout === 'horizontal' && toolbarLayout !== 'horizontal') {
                container.classList.remove('vertical');
                container.classList.add('horizontal');
                toolbarLayout = 'horizontal';
            } else if (newLayout === 'vertical' && toolbarLayout !== 'vertical') {
                container.classList.remove('horizontal');
                container.classList.add('vertical');
                toolbarLayout = 'vertical';
            }
        }
        
        // 设置键盘快捷键
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key.toLowerCase()) {
                        case 'z':
                            e.preventDefault();
                            if (e.shiftKey) {
                                executeAction('redo');
                            } else {
                                executeAction('undo');
                            }
                            break;
                        case 'y':
                            e.preventDefault();
                            executeAction('redo');
                            break;
                        case 's':
                            e.preventDefault();
                            executeAction('save');
                            break;
                        case 'c':
                            e.preventDefault();
                            executeAction('copy');
                            break;
                    }
                } else {
                    switch (e.key.toLowerCase()) {
                        case 'escape':
                            executeAction('close');
                            break;
                        case 't':
                            handleToolSelection('text', document.querySelector('[data-tool="text"]'));
                            break;
                        case 'a':
                            handleToolSelection('arrow', document.querySelector('[data-tool="arrow"]'));
                            break;
                        case 'r':
                            handleToolSelection('rectangle', document.querySelector('[data-tool="rectangle"]'));
                            break;
                        case 'c':
                            handleToolSelection('circle', document.querySelector('[data-tool="circle"]'));
                            break;
                        case 'b':
                            handleToolSelection('brush', document.querySelector('[data-tool="brush"]'));
                            break;
                    }
                }
            });
        }

        // 设置窗口事件监听
        function setupWindowEvents() {
            // 窗口关闭前清理
            window.addEventListener('beforeunload', function() {
                if (isConnected) {
                    notifyPreviewWindow('toolbar-disconnected', {
                        toolbarId: 'independent-toolbar'
                    });
                }
            });

            // 窗口失去焦点时的处理
            window.addEventListener('blur', function() {
                // Window lost focus
            });

            // 窗口获得焦点时的处理
            window.addEventListener('focus', function() {
                // Window gained focus
            });
        }

        // 🆕 智能窗口焦点管理
        let focusRequestTimeout = null;
        let isMouseInWindow = false;

        function setupSmartFocusManagement() {
            if (!window.__TAURI__) return;

            // 鼠标进入窗口事件
            document.addEventListener('mouseenter', () => {
                isMouseInWindow = true;
                requestSmartFocus();
                // 🆕 自动更新光标为指针
                updateToolbarCursor();
            });

            // 鼠标离开窗口事件
            document.addEventListener('mouseleave', () => {
                isMouseInWindow = false;
                cancelPendingFocus();
            });

            // 窗口获得焦点时确保光标正确
            window.addEventListener('focus', () => {
                updateToolbarCursor();
            });

            console.log('🎯 Smart focus and cursor management setup completed for toolbar');
        }

        // 🆕 更新工具栏光标
        function updateToolbarCursor() {
            document.body.style.cursor = 'pointer';

            // 确保所有按钮都有正确的光标
            const buttons = document.querySelectorAll('.tool-button, .action-button');
            buttons.forEach(button => {
                button.style.cursor = 'pointer';
            });

            console.log('🎯 Toolbar cursor updated to pointer');
        }

        function requestSmartFocus() {
            // 取消之前的焦点请求
            cancelPendingFocus();

            // 设置延迟焦点请求
            focusRequestTimeout = setTimeout(async () => {
                if (!isMouseInWindow) return; // 鼠标已离开，不设置焦点

                try {
                    // 检查当前是否已有焦点
                    const hasFocus = await window.__TAURI__.core.invoke('check_window_focus', {
                        windowId: 'independent-toolbar'
                    });

                    if (!hasFocus && isMouseInWindow) {
                        console.log('🎯 Requesting smart focus for toolbar');
                        await window.__TAURI__.core.invoke('request_window_focus', {
                            windowId: 'independent-toolbar',
                            delayMs: 50
                        });
                    }
                } catch (error) {
                    console.warn('🎯 Smart focus request failed:', error);
                }
            }, 200); // 200ms延迟
        }

        function cancelPendingFocus() {
            if (focusRequestTimeout) {
                clearTimeout(focusRequestTimeout);
                focusRequestTimeout = null;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializeToolbar);
    </script>
</body>
</html>
