<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Screenshot Overlay</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: transparent;
            cursor: crosshair;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
        }

        .overlay-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .selection-area {
            position: absolute;
            border: 2px solid #007AFF;
            background: rgba(0, 122, 255, 0.1);
            display: none;
            pointer-events: none;
        }

        .selection-handles {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #007AFF;
            border: 1px solid white;
            border-radius: 50%;
            display: none;
        }

        .handle-nw { top: -4px; left: -4px; cursor: nw-resize; }
        .handle-ne { top: -4px; right: -4px; cursor: ne-resize; }
        .handle-sw { bottom: -4px; left: -4px; cursor: sw-resize; }
        .handle-se { bottom: -4px; right: -4px; cursor: se-resize; }
        .handle-n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
        .handle-s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
        .handle-w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
        .handle-e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

        .toolbar {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            padding: 8px;
            display: none;
            gap: 8px;
            align-items: center;
        }

        .toolbar-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .toolbar-btn:hover {
            background: #0056CC;
        }

        .toolbar-btn.cancel {
            background: #FF3B30;
        }

        .toolbar-btn.cancel:hover {
            background: #D70015;
        }

        .coordinates-display {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: monospace;
            display: none;
        }

        .instructions {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
        }

        .instructions h2 {
            margin-bottom: 10px;
            color: #007AFF;
        }

        .instructions p {
            margin-bottom: 8px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="overlay-container">
        <div class="instructions" id="instructions">
            <h2>📸 Screenshot Mode</h2>
            <p>Click and drag to select an area</p>
            <p>Press <strong>ESC</strong> to cancel</p>
            <p>Press <strong>Enter</strong> to capture full screen</p>
        </div>

        <div class="selection-area" id="selectionArea">
            <div class="selection-handles handle-nw"></div>
            <div class="selection-handles handle-ne"></div>
            <div class="selection-handles handle-sw"></div>
            <div class="selection-handles handle-se"></div>
            <div class="selection-handles handle-n"></div>
            <div class="selection-handles handle-s"></div>
            <div class="selection-handles handle-w"></div>
            <div class="selection-handles handle-e"></div>
        </div>

        <div class="toolbar" id="toolbar">
            <button class="toolbar-btn" onclick="captureSelection()">Capture</button>
            <button class="toolbar-btn" onclick="copyToClipboard()">Copy</button>
            <button class="toolbar-btn cancel" onclick="cancelSelection()">Cancel</button>
        </div>

        <div class="coordinates-display" id="coordinates">
            Position: (0, 0) | Size: 0×0
        </div>
    </div>

    <script>
        let isSelecting = false;
        let startX = 0, startY = 0;
        let currentX = 0, currentY = 0;
        let selectionArea = document.getElementById('selectionArea');
        let toolbar = document.getElementById('toolbar');
        let coordinates = document.getElementById('coordinates');
        let instructions = document.getElementById('instructions');

        // 鼠标事件处理
        document.addEventListener('mousedown', startSelection);
        document.addEventListener('mousemove', updateSelection);
        document.addEventListener('mouseup', endSelection);

        // 键盘事件处理
        document.addEventListener('keydown', handleKeyboard);

        // 🆕 注解模式状态管理
        let annotationMode = false;
        let annotationData = null;
        let toolbarState = null;
        let currentDrawingTool = 'text';
        let currentDrawingStyle = {
            strokeColor: '#FF0000',
            strokeWidth: 2,
            fillColor: null,
            fontSize: 16,
            fontFamily: 'Arial'
        };
        let annotations = [];
        let undoStack = [];
        let redoStack = [];

        // 🆕 监听注解初始化事件
        if (window.__TAURI__) {
            window.__TAURI__.event.listen('annotation-initialize', (event) => {
                console.log('🎨 Received annotation initialization:', event.payload);
                handleAnnotationInitialization(event.payload.data);
            });

            // 🆕 监听工具栏事件
            window.__TAURI__.event.listen('annotation-event', (event) => {
                console.log('🎨 Received annotation event:', event.payload);
                handleAnnotationEvent(event.payload);
            });
        }

        // 🆕 处理注解初始化
        function handleAnnotationInitialization(data) {
            try {
                console.log('🎨 Initializing annotation mode with data:', data);

                annotationMode = true;
                annotationData = data;
                toolbarState = data.toolbarState;

                // 设置窗口尺寸和位置
                if (data.regionData) {
                    const region = data.regionData;
                    console.log('🎨 Setting window region:', region);

                    // 这里可以设置canvas或其他元素的尺寸
                    setupAnnotationCanvas(region);
                }

                // 加载截图
                if (data.screenshotPath) {
                    console.log('🎨 Loading screenshot:', data.screenshotPath);
                    loadScreenshotForAnnotation(data.screenshotPath);
                }

                // 应用工具栏状态
                if (toolbarState) {
                    console.log('🎨 Applying toolbar state:', toolbarState);
                    applyToolbarState(toolbarState);
                }

                // 发送准备就绪事件
                sendAnnotationReady(data.windowId);

            } catch (error) {
                console.error('🎨 Failed to initialize annotation mode:', error);
            }
        }

        // 🆕 处理注解事件
        function handleAnnotationEvent(payload) {
            if (!annotationMode) return;

            console.log('🎨 Processing annotation event:', payload.type, payload.data);

            switch (payload.type) {
                case 'drawing-tool-selected':
                    console.log('🎨 Tool selected:', payload.data.tool);
                    selectDrawingTool(payload.data.tool, payload.data.style);
                    sendDrawingStateUpdate();
                    break;

                case 'drawing-style-change':
                    console.log('🎨 Style change:', payload.data);
                    updateDrawingStyle(payload.data);
                    break;

                case 'action-undo':
                    console.log('🎨 Undo action');
                    const undoResult = performUndo();
                    sendDrawingStateUpdate();
                    break;

                case 'action-redo':
                    console.log('🎨 Redo action');
                    const redoResult = performRedo();
                    sendDrawingStateUpdate();
                    break;

                case 'action-save':
                    console.log('🎨 Save action');
                    saveAnnotation();
                    break;

                case 'action-copy':
                    console.log('🎨 Copy action');
                    copyAnnotation();
                    break;

                case 'canvas-clear':
                    console.log('🎨 Clear canvas');
                    clearCanvas(payload.data.clearAll);
                    sendDrawingStateUpdate();
                    break;

                case 'annotation-export':
                    console.log('🎨 Export annotation');
                    exportAnnotation(payload.data.format);
                    break;

                case 'annotation-mode-end':
                    console.log('🎨 Ending annotation mode');
                    endAnnotationMode();
                    break;

                case 'connection-ping':
                    console.log('🎨 Connection ping received');
                    sendConnectionPong(payload.data);
                    break;

                case 'toolbar-connect':
                    console.log('🎨 Toolbar connection request');
                    handleToolbarConnection(payload.data);
                    break;

                default:
                    console.log('🎨 Unhandled annotation event:', payload.type);
            }
        }

        // 🆕 发送绘图状态更新
        function sendDrawingStateUpdate(isCurrentlyDrawing = false) {
            if (window.__TAURI__) {
                const state = {
                    isDrawing: isCurrentlyDrawing,
                    currentTool: currentDrawingTool || 'text',
                    activeAnnotations: annotations ? annotations.length : 0,
                    canUndo: canPerformUndo(),
                    canRedo: canPerformRedo()
                };

                window.__TAURI__.event.emit('annotation-response', {
                    type: 'drawing-state-change',
                    data: state,
                    timestamp: Date.now(),
                    windowId: annotationData?.windowId || 'unknown'
                });

                console.log('🎨 Drawing state update sent:', state);
            }
        }

        // 🆕 发送注解数据更新
        function sendAnnotationDataUpdate() {
            if (window.__TAURI__) {
                window.__TAURI__.event.emit('annotation-response', {
                    type: 'annotation-data-update',
                    data: {
                        annotations: annotations,
                        totalCount: annotations.length,
                        lastModified: Date.now()
                    },
                    timestamp: Date.now(),
                    windowId: annotationData?.windowId || 'unknown'
                });

                console.log('🎨 Annotation data update sent, count:', annotations.length);
            }
        }

        // 🆕 更新绘图样式
        function updateDrawingStyle(styleData) {
            if (currentDrawingStyle) {
                Object.assign(currentDrawingStyle, styleData);
                console.log('🎨 Drawing style updated:', currentDrawingStyle);
            }
        }

        // 🆕 清空画布
        function clearCanvas(clearAll = true) {
            const canvas = document.getElementById('annotationCanvas');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    console.log('🎨 Canvas cleared');
                }
            }

            if (clearAll && annotations && annotations.length > 0) {
                clearAllAnnotations();
            }
        }

        // 🆕 生成合成图片
        async function generateCompositeImage() {
            try {
                const canvas = document.getElementById('annotationCanvas');
                const bgImage = document.getElementById('backgroundImage');

                if (!canvas || !bgImage) {
                    throw new Error('Canvas or background image not found');
                }

                // 创建临时canvas用于合成
                const compositeCanvas = document.createElement('canvas');
                compositeCanvas.width = canvas.width;
                compositeCanvas.height = canvas.height;
                const ctx = compositeCanvas.getContext('2d');

                // 等待背景图片加载完成
                await new Promise((resolve, reject) => {
                    if (bgImage.complete) {
                        resolve();
                    } else {
                        bgImage.onload = resolve;
                        bgImage.onerror = reject;
                        setTimeout(reject, 5000); // 5秒超时
                    }
                });

                // 绘制背景图片
                ctx.drawImage(bgImage, 0, 0, canvas.width, canvas.height);

                // 绘制所有注解
                annotations.forEach(annotation => {
                    drawAnnotation(ctx, annotation);
                });

                // 转换为base64数据
                const imageData = compositeCanvas.toDataURL('image/png');
                console.log('🎨 Composite image generated, size:', imageData.length);

                return imageData;
            } catch (error) {
                console.error('🎨 Failed to generate composite image:', error);
                return null;
            }
        }

        // 🆕 导出注解
        async function exportAnnotation(format = 'png') {
            console.log('🎨 Exporting annotation as:', format);

            try {
                const imageData = await generateCompositeImage();
                if (!imageData) {
                    throw new Error('Failed to generate image data');
                }

                // 创建下载链接
                const link = document.createElement('a');
                link.download = `annotation_${Date.now()}.${format}`;
                link.href = imageData;

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('🎨 Annotation exported successfully');
                return true;
            } catch (error) {
                console.error('🎨 Failed to export annotation:', error);
                return false;
            }
        }

        // 🆕 获取注解数据用于导出
        function getAnnotationExportData() {
            return {
                annotations: annotations,
                metadata: {
                    version: '1.0',
                    created: Date.now(),
                    toolVersion: 'Mecap v0.1.0',
                    canvasSize: {
                        width: document.getElementById('annotationCanvas')?.width || 0,
                        height: document.getElementById('annotationCanvas')?.height || 0
                    }
                },
                style: currentDrawingStyle
            };
        }

        // 🆕 导入注解数据
        function importAnnotationData(data) {
            try {
                if (!data || !data.annotations) {
                    throw new Error('Invalid annotation data');
                }

                // 保存当前状态
                saveStateToHistory();

                // 导入注解
                annotations = JSON.parse(JSON.stringify(data.annotations));

                // 应用样式（如果有）
                if (data.style) {
                    currentDrawingStyle = { ...data.style };
                }

                redrawCanvas();
                sendDrawingStateUpdate();
                sendAnnotationDataUpdate();

                console.log('🎨 Annotation data imported, count:', annotations.length);
                return true;
            } catch (error) {
                console.error('🎨 Failed to import annotation data:', error);
                return false;
            }
        }

        // 🆕 发送连接pong响应
        function sendConnectionPong(pingData) {
            if (window.__TAURI__) {
                window.__TAURI__.event.emit('annotation-response', {
                    type: 'connection-pong',
                    data: {
                        windowId: annotationData?.windowId || 'unknown',
                        timestamp: Date.now(),
                        pingTimestamp: pingData.timestamp
                    }
                });
                console.log('🎨 Connection pong sent');
            }
        }

        // 🆕 处理工具栏连接
        function handleToolbarConnection(connectionData) {
            console.log('🎨 Handling toolbar connection:', connectionData);

            // 应用工具栏状态
            if (connectionData.selectedTool) {
                selectDrawingTool(connectionData.selectedTool, connectionData.style);
            }

            // 发送连接确认
            if (window.__TAURI__) {
                window.__TAURI__.event.emit('annotation-response', {
                    type: 'connection-ack',
                    data: {
                        windowId: annotationData?.windowId || 'unknown',
                        capabilities: getAnnotationCapabilities(),
                        timestamp: Date.now()
                    }
                });
                console.log('🎨 Toolbar connection acknowledged');
            }
        }

        // 🆕 获取注解能力
        function getAnnotationCapabilities() {
            return {
                supportedTools: ['text', 'arrow', 'rectangle', 'circle', 'brush'],
                maxAnnotations: 100,
                supportsPressure: false,
                supportsLayers: false,
                supportsUndo: true,
                supportsExport: ['png', 'jpg']
            };
        }

        // 🆕 设置注解画布
        function setupAnnotationCanvas(region) {
            console.log('🎨 Setting up annotation canvas for region:', region);

            // 创建或获取canvas元素
            let canvas = document.getElementById('annotationCanvas');
            if (!canvas) {
                canvas = document.createElement('canvas');
                canvas.id = 'annotationCanvas';
                canvas.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 1000;
                    pointer-events: auto;
                `;
                document.body.appendChild(canvas);

                // 添加绘图事件监听器
                setupCanvasDrawingEvents(canvas);
            }

            // 设置canvas尺寸
            canvas.width = region.width;
            canvas.height = region.height;

            console.log('🎨 Annotation canvas setup completed');
        }

        // 🆕 设置画布绘图事件
        function setupCanvasDrawingEvents(canvas) {
            let isDrawing = false;
            let startX, startY;
            let currentAnnotation = null;
            let brushPoints = [];

            canvas.addEventListener('mousedown', (e) => {
                if (!annotationMode || !currentDrawingTool) return;

                isDrawing = true;
                const rect = canvas.getBoundingClientRect();
                startX = e.clientX - rect.left;
                startY = e.clientY - rect.top;

                // 特殊处理文本工具
                if (currentDrawingTool === 'text') {
                    handleTextAnnotation(startX, startY);
                    return;
                }

                // 创建新注解
                currentAnnotation = createNewAnnotation(startX, startY);

                // 初始化画笔点数组
                if (currentDrawingTool === 'brush') {
                    brushPoints = [{ x: startX, y: startY }];
                    currentAnnotation.coordinates.points = brushPoints;
                }

                console.log('🎨 Started drawing:', currentDrawingTool, 'at', startX, startY);

                // 发送绘图状态更新
                sendDrawingStateUpdate(true);
            });

            canvas.addEventListener('mousemove', (e) => {
                if (!isDrawing || !currentAnnotation) return;

                const rect = canvas.getBoundingClientRect();
                const currentX = e.clientX - rect.left;
                const currentY = e.clientY - rect.top;

                // 更新当前注解
                updateCurrentAnnotation(currentAnnotation, startX, startY, currentX, currentY);

                // 画笔工具特殊处理
                if (currentDrawingTool === 'brush') {
                    brushPoints.push({ x: currentX, y: currentY });
                    currentAnnotation.coordinates.points = [...brushPoints];
                }

                // 重绘画布
                redrawCanvas();

                // 绘制当前正在创建的注解
                const ctx = canvas.getContext('2d');
                drawAnnotation(ctx, currentAnnotation);
            });

            canvas.addEventListener('mouseup', (e) => {
                if (!isDrawing || !currentAnnotation) return;

                isDrawing = false;

                // 完成注解创建
                finishAnnotation(currentAnnotation);
                currentAnnotation = null;
                brushPoints = [];

                console.log('🎨 Finished drawing, total annotations:', annotations.length);

                // 发送绘图状态更新
                sendDrawingStateUpdate(false);
            });

            // 添加键盘事件支持
            canvas.addEventListener('keydown', (e) => {
                if (!annotationMode) return;

                switch (e.key) {
                    case 'Escape':
                        if (isDrawing) {
                            // 取消当前绘制
                            isDrawing = false;
                            currentAnnotation = null;
                            brushPoints = [];
                            redrawCanvas();
                        }
                        break;
                    case 'Delete':
                    case 'Backspace':
                        // 删除选中的注解
                        deleteSelectedAnnotation();
                        break;
                }
            });

            // 确保canvas可以接收键盘事件
            canvas.setAttribute('tabindex', '0');
        }

        // 🆕 处理文本注解
        function handleTextAnnotation(x, y) {
            // 创建文本输入框
            const textInput = document.createElement('input');
            textInput.type = 'text';
            textInput.placeholder = 'Enter text...';
            textInput.style.cssText = `
                position: absolute;
                left: ${x}px;
                top: ${y}px;
                font-size: ${currentDrawingStyle.fontSize || 16}px;
                font-family: ${currentDrawingStyle.fontFamily || 'Arial'};
                color: ${currentDrawingStyle.strokeColor || '#FF0000'};
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid ${currentDrawingStyle.strokeColor || '#FF0000'};
                border-radius: 4px;
                padding: 4px 8px;
                z-index: 2000;
                min-width: 100px;
            `;

            document.body.appendChild(textInput);
            textInput.focus();

            // 处理文本输入完成
            const finishTextInput = () => {
                const text = textInput.value.trim();
                if (text) {
                    const annotation = {
                        id: Date.now().toString(),
                        type: 'text',
                        coordinates: { x, y },
                        style: { ...currentDrawingStyle },
                        content: text,
                        timestamp: Date.now()
                    };

                    finishAnnotation(annotation);
                }

                document.body.removeChild(textInput);
            };

            textInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    finishTextInput();
                } else if (e.key === 'Escape') {
                    document.body.removeChild(textInput);
                }
            });

            textInput.addEventListener('blur', finishTextInput);
        }

        // 🆕 创建新注解
        function createNewAnnotation(x, y) {
            const annotation = {
                id: Date.now().toString(),
                type: currentDrawingTool,
                coordinates: { x, y },
                style: { ...currentDrawingStyle },
                timestamp: Date.now()
            };

            return annotation;
        }

        // 🆕 删除选中的注解
        function deleteSelectedAnnotation() {
            // TODO: 实现注解选择和删除逻辑
            console.log('🎨 Delete annotation requested');
        }

        // 🆕 更新当前注解
        function updateCurrentAnnotation(annotation, startX, startY, currentX, currentY) {
            switch (annotation.type) {
                case 'rectangle':
                case 'circle':
                    annotation.coordinates = {
                        x: Math.min(startX, currentX),
                        y: Math.min(startY, currentY),
                        width: Math.abs(currentX - startX),
                        height: Math.abs(currentY - startY)
                    };
                    break;
                case 'arrow':
                    annotation.coordinates = {
                        x: startX,
                        y: startY,
                        endX: currentX,
                        endY: currentY
                    };
                    break;
                case 'text':
                    annotation.coordinates = { x: startX, y: startY };
                    break;
                case 'brush':
                    if (!annotation.coordinates.points) {
                        annotation.coordinates.points = [];
                    }
                    annotation.coordinates.points.push({ x: currentX, y: currentY });
                    break;
            }
        }

        // 🆕 完成注解
        function finishAnnotation(annotation) {
            // 验证注解有效性
            if (!isValidAnnotation(annotation)) {
                console.warn('🎨 Invalid annotation, skipping:', annotation);
                return;
            }

            // 保存当前状态到撤销栈
            saveStateToHistory();

            // 添加注解到列表
            annotations.push(annotation);

            console.log('🎨 Annotation added:', annotation.type, 'Total:', annotations.length);

            // 发送状态更新
            sendDrawingStateUpdate();
            sendAnnotationDataUpdate();
        }

        // 🆕 验证注解有效性
        function isValidAnnotation(annotation) {
            if (!annotation || !annotation.type) return false;

            switch (annotation.type) {
                case 'rectangle':
                case 'circle':
                    return annotation.coordinates.width > 5 && annotation.coordinates.height > 5;
                case 'arrow':
                    const dx = annotation.coordinates.endX - annotation.coordinates.x;
                    const dy = annotation.coordinates.endY - annotation.coordinates.y;
                    return Math.sqrt(dx * dx + dy * dy) > 10; // 最小长度
                case 'text':
                    return annotation.content && annotation.content.trim().length > 0;
                case 'brush':
                    return annotation.coordinates.points && annotation.coordinates.points.length > 2;
                default:
                    return false;
            }
        }

        // 🆕 保存状态到历史记录
        function saveStateToHistory() {
            // 深拷贝当前状态
            const currentState = JSON.parse(JSON.stringify(annotations));
            undoStack.push(currentState);

            // 限制撤销栈大小
            if (undoStack.length > 50) {
                undoStack.shift();
            }

            // 清空重做栈（新操作后不能重做之前的撤销）
            redoStack.length = 0;

            console.log('🎨 State saved to history, undo stack size:', undoStack.length);
        }

        // 🆕 获取当前状态快照
        function getCurrentStateSnapshot() {
            return {
                annotations: JSON.parse(JSON.stringify(annotations)),
                undoStackSize: undoStack.length,
                redoStackSize: redoStack.length,
                currentTool: currentDrawingTool,
                style: { ...currentDrawingStyle },
                timestamp: Date.now()
            };
        }

        // 🆕 恢复状态快照
        function restoreStateSnapshot(snapshot) {
            if (!snapshot) return false;

            try {
                annotations = JSON.parse(JSON.stringify(snapshot.annotations));
                currentDrawingTool = snapshot.currentTool;
                currentDrawingStyle = { ...snapshot.style };

                redrawCanvas();
                sendDrawingStateUpdate();
                sendAnnotationDataUpdate();

                console.log('🎨 State snapshot restored, annotations:', annotations.length);
                return true;
            } catch (error) {
                console.error('🎨 Failed to restore state snapshot:', error);
                return false;
            }
        }

        // 🆕 加载截图用于注解
        function loadScreenshotForAnnotation(screenshotPath) {
            console.log('🎨 Loading screenshot for annotation:', screenshotPath);

            // 创建背景图片元素
            let bgImage = document.getElementById('backgroundImage');
            if (!bgImage) {
                bgImage = document.createElement('img');
                bgImage.id = 'backgroundImage';
                bgImage.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    z-index: 500;
                    pointer-events: none;
                `;
                document.body.appendChild(bgImage);
            }

            bgImage.src = screenshotPath;
            bgImage.onload = () => {
                console.log('🎨 Screenshot loaded successfully');
            };
        }

        // 🆕 应用工具栏状态
        function applyToolbarState(state) {
            console.log('🎨 Applying toolbar state:', state);

            if (state.selectedTool) {
                selectDrawingTool(state.selectedTool);
            }
        }

        // 🆕 发送注解准备就绪事件
        function sendAnnotationReady(windowId) {
            if (window.__TAURI__) {
                window.__TAURI__.event.emit('annotation-response', {
                    type: 'annotation-ready',
                    data: {
                        windowId: windowId,
                        capabilities: {
                            supportedTools: ['text', 'arrow', 'rectangle', 'circle', 'brush'],
                            maxAnnotations: 100,
                            supportsPressure: false,
                            supportsLayers: false,
                            supportsUndo: true,
                            supportsExport: ['png', 'jpg']
                        }
                    },
                    timestamp: Date.now()
                });
                console.log('🎨 Annotation ready event sent');
            }
        }

        // 🆕 选择绘图工具
        function selectDrawingTool(tool, style = null) {
            console.log('🎨 Selecting drawing tool:', tool, style);

            currentDrawingTool = tool;

            if (style) {
                Object.assign(currentDrawingStyle, style);
            }

            // 更新画布光标
            updateCanvasCursor(tool);

            console.log('🎨 Drawing tool selected:', currentDrawingTool, currentDrawingStyle);
        }

        // 🆕 更新画布光标
        function updateCanvasCursor(tool) {
            const canvas = document.getElementById('annotationCanvas');
            if (canvas) {
                switch (tool) {
                    case 'text':
                        canvas.style.cursor = 'text';
                        break;
                    case 'brush':
                        canvas.style.cursor = 'crosshair';
                        break;
                    case 'arrow':
                    case 'rectangle':
                    case 'circle':
                        canvas.style.cursor = 'crosshair';
                        break;
                    default:
                        canvas.style.cursor = 'default';
                }
            }
        }

        // 🆕 执行撤销
        function performUndo() {
            console.log('🎨 Performing undo, stack size:', undoStack.length);

            if (undoStack.length > 0) {
                // 保存当前状态到重做栈
                const currentState = JSON.parse(JSON.stringify(annotations));
                redoStack.push(currentState);

                // 恢复上一个状态
                const lastState = undoStack.pop();
                annotations = JSON.parse(JSON.stringify(lastState));

                // 限制重做栈大小
                if (redoStack.length > 50) {
                    redoStack.shift();
                }

                redrawCanvas();
                sendDrawingStateUpdate();
                sendAnnotationDataUpdate();

                console.log('🎨 Undo performed, annotations count:', annotations.length);
                return true;
            }

            console.log('🎨 Nothing to undo');
            return false;
        }

        // 🆕 执行重做
        function performRedo() {
            console.log('🎨 Performing redo, stack size:', redoStack.length);

            if (redoStack.length > 0) {
                // 保存当前状态到撤销栈
                const currentState = JSON.parse(JSON.stringify(annotations));
                undoStack.push(currentState);

                // 恢复下一个状态
                const nextState = redoStack.pop();
                annotations = JSON.parse(JSON.stringify(nextState));

                redrawCanvas();
                sendDrawingStateUpdate();
                sendAnnotationDataUpdate();

                console.log('🎨 Redo performed, annotations count:', annotations.length);
                return true;
            }

            console.log('🎨 Nothing to redo');
            return false;
        }

        // 🆕 清空所有注解
        function clearAllAnnotations() {
            if (annotations.length > 0) {
                // 保存当前状态到撤销栈
                saveStateToHistory();

                // 清空注解
                annotations.length = 0;

                redrawCanvas();
                sendDrawingStateUpdate();
                sendAnnotationDataUpdate();

                console.log('🎨 All annotations cleared');
                return true;
            }

            return false;
        }

        // 🆕 删除指定注解
        function deleteAnnotation(annotationId) {
            const index = annotations.findIndex(ann => ann.id === annotationId);
            if (index !== -1) {
                // 保存当前状态到撤销栈
                saveStateToHistory();

                // 删除注解
                const deleted = annotations.splice(index, 1)[0];

                redrawCanvas();
                sendDrawingStateUpdate();
                sendAnnotationDataUpdate();

                console.log('🎨 Annotation deleted:', deleted.type, deleted.id);
                return true;
            }

            return false;
        }

        // 🆕 获取历史状态信息
        function getHistoryInfo() {
            return {
                canUndo: canPerformUndo(),
                canRedo: canPerformRedo(),
                undoStackSize: undoStack.length,
                redoStackSize: redoStack.length,
                totalAnnotations: annotations.length
            };
        }

        // 🆕 检查是否可以撤销
        function canPerformUndo() {
            return undoStack.length > 0;
        }

        // 🆕 检查是否可以重做
        function canPerformRedo() {
            return redoStack.length > 0;
        }

        // 🆕 重绘画布
        function redrawCanvas() {
            const canvas = document.getElementById('annotationCanvas');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // 重绘所有注解
                    annotations.forEach(annotation => {
                        drawAnnotation(ctx, annotation);
                    });

                    console.log('🎨 Canvas redrawn with', annotations.length, 'annotations');
                }
            }
        }

        // 🆕 绘制单个注解
        function drawAnnotation(ctx, annotation) {
            console.log('🎨 Drawing annotation:', annotation.type);

            ctx.save();

            // 设置样式
            ctx.strokeStyle = annotation.style.strokeColor || currentDrawingStyle.strokeColor;
            ctx.lineWidth = annotation.style.strokeWidth || currentDrawingStyle.strokeWidth;
            ctx.fillStyle = annotation.style.fillColor || currentDrawingStyle.fillColor;

            switch (annotation.type) {
                case 'rectangle':
                    drawRectangle(ctx, annotation);
                    break;
                case 'circle':
                    drawCircle(ctx, annotation);
                    break;
                case 'arrow':
                    drawArrow(ctx, annotation);
                    break;
                case 'text':
                    drawText(ctx, annotation);
                    break;
                case 'brush':
                    drawBrush(ctx, annotation);
                    break;
                default:
                    console.warn('🎨 Unknown annotation type:', annotation.type);
            }

            ctx.restore();
        }

        // 🆕 绘制矩形
        function drawRectangle(ctx, annotation) {
            const { x, y, width, height } = annotation.coordinates;
            ctx.strokeRect(x, y, width, height);
            if (annotation.style.fillColor) {
                ctx.fillRect(x, y, width, height);
            }
        }

        // 🆕 绘制圆形
        function drawCircle(ctx, annotation) {
            const { x, y, width, height } = annotation.coordinates;
            const centerX = x + width / 2;
            const centerY = y + height / 2;
            const radius = Math.min(Math.abs(width), Math.abs(height)) / 2;

            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.stroke();
            if (annotation.style.fillColor) {
                ctx.fill();
            }
        }

        // 🆕 绘制箭头
        function drawArrow(ctx, annotation) {
            const { x, y, endX, endY } = annotation.coordinates;
            const headLength = 15;
            const angle = Math.atan2(endY - y, endX - x);

            // 绘制箭头线
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 绘制箭头头部
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - headLength * Math.cos(angle - Math.PI / 6),
                      endY - headLength * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - headLength * Math.cos(angle + Math.PI / 6),
                      endY - headLength * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 🆕 绘制文字
        function drawText(ctx, annotation) {
            const { x, y } = annotation.coordinates;
            const text = annotation.content || 'Text';

            ctx.save();
            ctx.font = `${annotation.style.fontSize || 16}px ${annotation.style.fontFamily || 'Arial'}`;
            ctx.fillStyle = annotation.style.strokeColor || currentDrawingStyle.strokeColor;
            ctx.textBaseline = 'top';

            // 添加文字背景（可选）
            if (annotation.style.fillColor) {
                const metrics = ctx.measureText(text);
                const textHeight = annotation.style.fontSize || 16;
                ctx.fillStyle = annotation.style.fillColor;
                ctx.fillRect(x - 2, y - 2, metrics.width + 4, textHeight + 4);
                ctx.fillStyle = annotation.style.strokeColor || currentDrawingStyle.strokeColor;
            }

            ctx.fillText(text, x, y);
            ctx.restore();
        }

        // 🆕 绘制画笔
        function drawBrush(ctx, annotation) {
            const points = annotation.coordinates.points || [];
            if (points.length < 2) return;

            ctx.save();
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);

            // 使用二次贝塞尔曲线平滑画笔轨迹
            for (let i = 1; i < points.length - 1; i++) {
                const currentPoint = points[i];
                const nextPoint = points[i + 1];
                const controlX = (currentPoint.x + nextPoint.x) / 2;
                const controlY = (currentPoint.y + nextPoint.y) / 2;
                ctx.quadraticCurveTo(currentPoint.x, currentPoint.y, controlX, controlY);
            }

            // 绘制最后一个点
            if (points.length > 1) {
                const lastPoint = points[points.length - 1];
                ctx.lineTo(lastPoint.x, lastPoint.y);
            }

            ctx.stroke();
            ctx.restore();
        }

        // 🆕 获取注解边界框
        function getAnnotationBounds(annotation) {
            switch (annotation.type) {
                case 'rectangle':
                case 'circle':
                    return {
                        x: annotation.coordinates.x,
                        y: annotation.coordinates.y,
                        width: annotation.coordinates.width,
                        height: annotation.coordinates.height
                    };
                case 'arrow':
                    const { x, y, endX, endY } = annotation.coordinates;
                    return {
                        x: Math.min(x, endX),
                        y: Math.min(y, endY),
                        width: Math.abs(endX - x),
                        height: Math.abs(endY - y)
                    };
                case 'text':
                    // 估算文字边界
                    const canvas = document.getElementById('annotationCanvas');
                    if (canvas) {
                        const ctx = canvas.getContext('2d');
                        ctx.font = `${annotation.style.fontSize || 16}px ${annotation.style.fontFamily || 'Arial'}`;
                        const metrics = ctx.measureText(annotation.content || 'Text');
                        return {
                            x: annotation.coordinates.x,
                            y: annotation.coordinates.y,
                            width: metrics.width,
                            height: annotation.style.fontSize || 16
                        };
                    }
                    break;
                case 'brush':
                    const points = annotation.coordinates.points || [];
                    if (points.length === 0) return { x: 0, y: 0, width: 0, height: 0 };

                    let minX = points[0].x, maxX = points[0].x;
                    let minY = points[0].y, maxY = points[0].y;

                    points.forEach(point => {
                        minX = Math.min(minX, point.x);
                        maxX = Math.max(maxX, point.x);
                        minY = Math.min(minY, point.y);
                        maxY = Math.max(maxY, point.y);
                    });

                    return {
                        x: minX,
                        y: minY,
                        width: maxX - minX,
                        height: maxY - minY
                    };
            }

            return { x: 0, y: 0, width: 0, height: 0 };
        }

        // 🆕 保存注解
        async function saveAnnotation() {
            console.log('🎨 Saving annotation');

            try {
                // 生成合成图片
                const compositeImageData = await generateCompositeImage();
                if (!compositeImageData) {
                    throw new Error('Failed to generate composite image');
                }

                // 调用后端保存功能
                const result = await window.__TAURI__.core.invoke('save_annotated_screenshot', {
                    imageData: compositeImageData,
                    format: 'png',
                    annotations: annotations
                });

                console.log('🎨 Annotation saved successfully:', result);

                // 发送保存完成事件
                if (window.__TAURI__) {
                    window.__TAURI__.event.emit('annotation-response', {
                        type: 'annotation-saved',
                        data: {
                            success: true,
                            path: result.path,
                            timestamp: Date.now()
                        },
                        windowId: annotationData?.windowId || 'unknown'
                    });
                }

                return result;
            } catch (error) {
                console.error('🎨 Failed to save annotation:', error);

                // 发送保存失败事件
                if (window.__TAURI__) {
                    window.__TAURI__.event.emit('annotation-response', {
                        type: 'annotation-save-failed',
                        data: {
                            success: false,
                            error: error.message,
                            timestamp: Date.now()
                        },
                        windowId: annotationData?.windowId || 'unknown'
                    });
                }

                throw error;
            }
        }

        // 🆕 复制注解到剪贴板
        async function copyAnnotation() {
            console.log('🎨 Copying annotation to clipboard');

            try {
                // 生成合成图片
                const compositeImageData = await generateCompositeImage();
                if (!compositeImageData) {
                    throw new Error('Failed to generate composite image');
                }

                // 调用后端复制功能
                const result = await window.__TAURI__.core.invoke('copy_annotated_screenshot', {
                    imageData: compositeImageData
                });

                console.log('🎨 Annotation copied to clipboard successfully');

                // 发送复制完成事件
                if (window.__TAURI__) {
                    window.__TAURI__.event.emit('annotation-response', {
                        type: 'annotation-copied',
                        data: {
                            success: true,
                            timestamp: Date.now()
                        },
                        windowId: annotationData?.windowId || 'unknown'
                    });
                }

                return result;
            } catch (error) {
                console.error('🎨 Failed to copy annotation:', error);

                // 发送复制失败事件
                if (window.__TAURI__) {
                    window.__TAURI__.event.emit('annotation-response', {
                        type: 'annotation-copy-failed',
                        data: {
                            success: false,
                            error: error.message,
                            timestamp: Date.now()
                        },
                        windowId: annotationData?.windowId || 'unknown'
                    });
                }

                throw error;
            }
        }

        // 🆕 结束注解模式
        function endAnnotationMode() {
            console.log('🎨 Ending annotation mode');

            // 清理注解状态
            cleanupAnnotationState();

            // 发送注解窗口关闭事件
            sendAnnotationWindowClosed();

            // 延迟关闭窗口，确保事件发送完成
            setTimeout(() => {
                closeAnnotationWindow();
            }, 200);
        }

        // 🆕 清理注解状态
        function cleanupAnnotationState() {
            console.log('🎨 Cleaning up annotation state');

            annotationMode = false;
            annotationData = null;
            toolbarState = null;

            // 清理Canvas
            const canvas = document.getElementById('annotationCanvas');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                }
                canvas.remove();
            }

            // 清理背景图片
            const bgImage = document.getElementById('backgroundImage');
            if (bgImage) {
                bgImage.remove();
            }

            console.log('🎨 Annotation state cleaned up');
        }

        // 🆕 发送注解窗口关闭事件
        function sendAnnotationWindowClosed() {
            if (window.__TAURI__) {
                window.__TAURI__.event.emit('annotation-response', {
                    type: 'annotation-window-closed',
                    data: {
                        windowId: annotationData?.windowId || 'unknown',
                        timestamp: Date.now()
                    }
                });
                console.log('🎨 Annotation window closed event sent');
            }
        }

        // 🆕 关闭注解窗口
        function closeAnnotationWindow() {
            console.log('🎨 Closing annotation window');

            if (window.__TAURI__) {
                const currentWindow = window.__TAURI__.webviewWindow.getCurrentWebviewWindow();
                currentWindow.close().catch(error => {
                    console.error('🎨 Error closing annotation window:', error);
                });
            }
        }

        // 🆕 窗口卸载时的清理
        window.addEventListener('beforeunload', () => {
            console.log('🎨 Window unloading, performing cleanup');
            cleanupAnnotationState();
        });

        function startSelection(event) {
            // 🆕 在注解模式下禁用区域选择
            if (annotationMode) {
                return;
            }

            if (event.target.closest('.toolbar') || event.target.closest('.selection-handles')) {
                return;
            }

            isSelecting = true;
            startX = event.clientX;
            startY = event.clientY;
            currentX = startX;
            currentY = startY;

            instructions.style.display = 'none';
            coordinates.style.display = 'block';
            
            updateSelectionArea();
        }

        function updateSelection(event) {
            if (!isSelecting) return;

            currentX = event.clientX;
            currentY = event.clientY;

            updateSelectionArea();
            updateCoordinates();
        }

        function endSelection(event) {
            if (!isSelecting) return;

            isSelecting = false;
            
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            if (width > 10 && height > 10) {
                showToolbar();
            } else {
                cancelSelection();
            }
        }

        function updateSelectionArea() {
            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            selectionArea.style.left = left + 'px';
            selectionArea.style.top = top + 'px';
            selectionArea.style.width = width + 'px';
            selectionArea.style.height = height + 'px';
            selectionArea.style.display = 'block';
        }

        function updateCoordinates() {
            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            coordinates.textContent = `Position: (${left}, ${top}) | Size: ${width}×${height}`;
        }

        function showToolbar() {
            const rect = selectionArea.getBoundingClientRect();
            toolbar.style.left = (rect.left + rect.width / 2 - 75) + 'px';
            toolbar.style.top = (rect.bottom + 10) + 'px';
            toolbar.style.display = 'flex';
        }

        function captureSelection() {
            const rect = selectionArea.getBoundingClientRect();
            const area = {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height
            };

            // 调用Tauri命令捕获区域
            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('capture_region_new', { area })
                    .then(result => {
                        console.log('Screenshot captured:', result);
                        closeOverlay();
                    })
                    .catch(error => {
                        console.error('Failed to capture screenshot:', error);
                    });
            }
        }

        function copyToClipboard() {
            // TODO: 实现复制到剪贴板功能
            console.log('Copy to clipboard not yet implemented');
        }

        function cancelSelection() {
            selectionArea.style.display = 'none';
            toolbar.style.display = 'none';
            coordinates.style.display = 'none';
            instructions.style.display = 'block';
        }

        function handleKeyboard(event) {
            switch (event.key) {
                case 'Escape':
                    closeOverlay();
                    break;
                case 'Enter':
                    captureFullScreen();
                    break;
            }
        }

        function captureFullScreen() {
            if (window.__TAURI__) {
                window.__TAURI__.core.invoke('capture_fullscreen')
                    .then(result => {
                        console.log('Fullscreen captured:', result);
                        closeOverlay();
                    })
                    .catch(error => {
                        console.error('Failed to capture fullscreen:', error);
                    });
            }
        }

        function closeOverlay() {
            if (window.__TAURI__) {
                window.__TAURI__.window.getCurrent().close();
            }
        }

        // 初始化
        console.log('Screenshot overlay loaded');
    </script>
</body>
</html>
