/**
 * Jest测试环境设置
 */

// 模拟Canvas API
global.HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
  clearRect: jest.fn(),
  drawImage: jest.fn(),
  strokeRect: jest.fn(),
  fillRect: jest.fn(),
  beginPath: jest.fn(),
  moveTo: jest.fn(),
  lineTo: jest.fn(),
  stroke: jest.fn(),
  fill: jest.fn(),
  arc: jest.fn(),
  ellipse: jest.fn(),
  fillText: jest.fn(),
  strokeText: jest.fn(),
  save: jest.fn(),
  restore: jest.fn(),
  scale: jest.fn(),
  getImageData: jest.fn(() => new ImageData(1, 1)),
  toDataURL: jest.fn(() => 'data:image/png;base64,test'),
  imageSmoothingEnabled: true,
  imageSmoothingQuality: 'high',
  globalAlpha: 1,
  strokeStyle: '#000000',
  fillStyle: '#000000',
  lineWidth: 1,
  lineCap: 'butt',
  lineJoin: 'miter',
  font: '10px sans-serif',
  textAlign: 'start',
  textBaseline: 'alphabetic'
}));

// 模拟requestAnimationFrame
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn();

// 模拟performance API
global.performance = {
  now: jest.fn(() => Date.now())
};

// 模拟Image构造函数
global.Image = class {
  constructor() {
    this.naturalWidth = 800;
    this.naturalHeight = 600;
    this.crossOrigin = '';
  }
  
  set src(value) {
    setTimeout(() => {
      if (this.onload) this.onload();
    }, 0);
  }
};

// 模拟ImageData
global.ImageData = class {
  constructor(width, height) {
    this.width = width;
    this.height = height;
    this.data = new Uint8ClampedArray(width * height * 4);
  }
};

// 模拟Tauri API
global.window.__TAURI__ = {
  core: {
    invoke: jest.fn(() => Promise.resolve({ success: true }))
  }
};

// 模拟console方法以避免测试输出污染
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};
