/**
 * 截图标注功能综合测试套件
 * 测试所有9种标注工具和边缘情况
 */

// 模拟DOM环境
const mockCanvas = {
    width: 800,
    height: 600,
    style: { width: '400px', height: '300px' },
    getContext: () => ({
        clearRect: jest.fn(),
        drawImage: jest.fn(),
        strokeRect: jest.fn(),
        fillRect: jest.fn(),
        beginPath: jest.fn(),
        moveTo: jest.fn(),
        lineTo: jest.fn(),
        stroke: jest.fn(),
        fill: jest.fn(),
        arc: jest.fn(),
        ellipse: jest.fn(),
        fillText: jest.fn(),
        strokeText: jest.fn(),
        save: jest.fn(),
        restore: jest.fn(),
        scale: jest.fn(),
        getImageData: jest.fn(() => new ImageData(1, 1)),
        toDataURL: jest.fn(() => 'data:image/png;base64,test')
    })
};

// 模拟全局变量
global.window = {
    devicePixelRatio: 2,
    innerWidth: 1200,
    innerHeight: 800
};

global.document = {
    getElementById: jest.fn(() => mockCanvas),
    createElement: jest.fn(() => mockCanvas)
};

global.Image = class {
    constructor() {
        this.naturalWidth = 800;
        this.naturalHeight = 600;
        this.crossOrigin = '';
    }
    set src(value) {
        setTimeout(() => this.onload && this.onload(), 0);
    }
};

describe('截图标注功能测试', () => {
    let shapes, annotationContext, screenshotData;

    beforeEach(() => {
        shapes = [];
        annotationContext = mockCanvas.getContext();
        screenshotData = {
            width: 800,
            height: 600,
            dataUrl: 'data:image/png;base64,test'
        };
    });

    describe('矩形工具测试', () => {
        test('应该创建有效的矩形形状', () => {
            const shape = {
                type: 'rectangle',
                startX: 100,
                startY: 100,
                endX: 200,
                endY: 200,
                color: '#ff0000',
                strokeWidth: 3
            };

            shapes.push(shape);
            expect(shapes).toHaveLength(1);
            expect(shape.type).toBe('rectangle');
            expect(Math.abs(shape.endX - shape.startX)).toBeGreaterThan(5); // 最小尺寸检查
        });

        test('应该过滤过小的矩形', () => {
            const smallShape = {
                type: 'rectangle',
                startX: 100,
                startY: 100,
                endX: 102,
                endY: 102,
                color: '#ff0000',
                strokeWidth: 3
            };

            const width = Math.abs(smallShape.endX - smallShape.startX);
            const height = Math.abs(smallShape.endY - smallShape.startY);
            const MIN_SHAPE_SIZE = 5;

            if (width >= MIN_SHAPE_SIZE && height >= MIN_SHAPE_SIZE) {
                shapes.push(smallShape);
            }

            expect(shapes).toHaveLength(0); // 应该被过滤掉
        });
    });

    describe('圆形工具测试', () => {
        test('应该创建有效的圆形形状', () => {
            const shape = {
                type: 'circle',
                startX: 100,
                startY: 100,
                endX: 200,
                endY: 200,
                color: '#00ff00',
                strokeWidth: 3
            };

            shapes.push(shape);
            expect(shapes).toHaveLength(1);
            expect(shape.type).toBe('circle');
        });
    });

    describe('画笔工具测试', () => {
        test('应该创建有效的画笔笔画', () => {
            const shape = {
                type: 'brush',
                points: [100, 100, 110, 105, 120, 110, 130, 115, 140, 120],
                color: '#0000ff',
                strokeWidth: 3
            };

            shapes.push(shape);
            expect(shapes).toHaveLength(1);
            expect(shape.points).toHaveLength(10); // 5个点，每个点2个坐标
        });

        test('应该过滤过短的画笔笔画', () => {
            const shortStroke = {
                type: 'brush',
                points: [100, 100, 105, 105],
                color: '#0000ff',
                strokeWidth: 3
            };

            const MIN_BRUSH_STROKE_LENGTH = 10;
            if (shortStroke.points.length >= MIN_BRUSH_STROKE_LENGTH) {
                shapes.push(shortStroke);
            }

            expect(shapes).toHaveLength(0); // 应该被过滤掉
        });
    });

    describe('文字工具测试', () => {
        test('应该创建有效的文字标注', () => {
            const shape = {
                type: 'text',
                x: 100,
                y: 100,
                text: 'Test Text',
                color: '#000000',
                fontSize: 16,
                fontFamily: 'Arial'
            };

            shapes.push(shape);
            expect(shapes).toHaveLength(1);
            expect(shape.text).toBe('Test Text');
        });

        test('应该过滤空文字', () => {
            const emptyText = {
                type: 'text',
                x: 100,
                y: 100,
                text: '',
                color: '#000000',
                fontSize: 16
            };

            if (emptyText.text && emptyText.text.trim()) {
                shapes.push(emptyText);
            }

            expect(shapes).toHaveLength(0); // 应该被过滤掉
        });
    });

    describe('序号工具测试', () => {
        test('应该创建递增的序号标注', () => {
            let numberCounter = 1;

            const shape1 = {
                type: 'number',
                x: 100,
                y: 100,
                number: numberCounter++,
                color: '#ff0000',
                radius: 15
            };

            const shape2 = {
                type: 'number',
                x: 200,
                y: 200,
                number: numberCounter++,
                color: '#ff0000',
                radius: 15
            };

            shapes.push(shape1, shape2);
            expect(shapes).toHaveLength(2);
            expect(shape1.number).toBe(1);
            expect(shape2.number).toBe(2);
        });
    });

    describe('橡皮擦工具测试', () => {
        test('应该正确检测点是否在矩形内', () => {
            const shape = {
                type: 'rectangle',
                startX: 100,
                startY: 100,
                endX: 200,
                endY: 200
            };

            function isPointInRectangle(x, y, shape, tolerance = 5) {
                const left = Math.min(shape.startX, shape.endX) - tolerance;
                const right = Math.max(shape.startX, shape.endX) + tolerance;
                const top = Math.min(shape.startY, shape.endY) - tolerance;
                const bottom = Math.max(shape.startY, shape.endY) + tolerance;
                
                return x >= left && x <= right && y >= top && y <= bottom;
            }

            expect(isPointInRectangle(150, 150, shape)).toBe(true); // 内部点
            expect(isPointInRectangle(50, 50, shape)).toBe(false); // 外部点
            expect(isPointInRectangle(95, 150, shape)).toBe(true); // 边界点（考虑容差）
        });
    });

    describe('Canvas尺寸处理测试', () => {
        test('应该正确计算DPR缩放', () => {
            const originalWidth = 800;
            const originalHeight = 600;
            const dpr = 2;

            const logicalWidth = originalWidth;
            const logicalHeight = originalHeight;
            const actualWidth = logicalWidth * dpr;
            const actualHeight = logicalHeight * dpr;

            expect(actualWidth).toBe(1600);
            expect(actualHeight).toBe(1200);
        });

        test('应该正确缩放标注坐标', () => {
            const shape = {
                startX: 100,
                startY: 100,
                endX: 200,
                endY: 200
            };

            const scaleX = 0.5;
            const scaleY = 0.5;

            function scaleShapeForExport(shape, scaleX, scaleY) {
                return {
                    ...shape,
                    startX: shape.startX * scaleX,
                    startY: shape.startY * scaleY,
                    endX: shape.endX * scaleX,
                    endY: shape.endY * scaleY
                };
            }

            const scaledShape = scaleShapeForExport(shape, scaleX, scaleY);
            expect(scaledShape.startX).toBe(50);
            expect(scaledShape.startY).toBe(50);
            expect(scaledShape.endX).toBe(100);
            expect(scaledShape.endY).toBe(100);
        });
    });

    describe('性能优化测试', () => {
        test('应该正确计算形状边界框', () => {
            function getShapeBounds(shape) {
                switch (shape.type) {
                    case 'rectangle':
                        return {
                            left: Math.min(shape.startX, shape.endX),
                            top: Math.min(shape.startY, shape.endY),
                            right: Math.max(shape.startX, shape.endX),
                            bottom: Math.max(shape.startY, shape.endY)
                        };
                    default:
                        return null;
                }
            }

            const shape = {
                type: 'rectangle',
                startX: 200,
                startY: 200,
                endX: 100,
                endY: 100
            };

            const bounds = getShapeBounds(shape);
            expect(bounds.left).toBe(100);
            expect(bounds.top).toBe(100);
            expect(bounds.right).toBe(200);
            expect(bounds.bottom).toBe(200);
        });

        test('应该正确检测形状是否在视口内', () => {
            function isShapeInViewport(bounds, viewWidth, viewHeight, padding = 50) {
                return !(bounds.right < -padding || 
                        bounds.left > viewWidth + padding ||
                        bounds.bottom < -padding || 
                        bounds.top > viewHeight + padding);
            }

            const bounds = { left: 100, top: 100, right: 200, bottom: 200 };
            const viewWidth = 800;
            const viewHeight = 600;

            expect(isShapeInViewport(bounds, viewWidth, viewHeight)).toBe(true);

            const outsideBounds = { left: 900, top: 100, right: 1000, bottom: 200 };
            expect(isShapeInViewport(outsideBounds, viewWidth, viewHeight)).toBe(false);
        });
    });

    describe('错误处理测试', () => {
        test('应该处理Canvas污染错误', () => {
            const mockContext = {
                getImageData: jest.fn(() => {
                    throw new Error('Canvas has been tainted by cross-origin data');
                })
            };

            let canvasTainted = false;
            try {
                mockContext.getImageData(0, 0, 1, 1);
            } catch (error) {
                if (error.message.includes('tainted')) {
                    canvasTainted = true;
                }
            }

            expect(canvasTainted).toBe(true);
        });

        test('应该处理Tauri API不可用错误', () => {
            global.window.__TAURI__ = undefined;

            function checkTauriAPI() {
                return !!(window.__TAURI__ && window.__TAURI__.core && window.__TAURI__.core.invoke);
            }

            expect(checkTauriAPI()).toBe(false);
        });
    });
});
