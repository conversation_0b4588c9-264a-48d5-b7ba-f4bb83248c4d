import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Screenshot {
  id: string;
  path: string;
  createdAt: number;
  tags: string[];
  syncStatus: 'local' | 'syncing' | 'synced' | 'error';
  width?: number;
  height?: number;
  name?: string;
}

interface ScreenshotState {
  screenshots: Screenshot[];
  recentScreenshots: Screenshot[];
  isCapturing: boolean;
  selectedScreenshot: Screenshot | null;
  
  // Actions
  addScreenshot: (screenshot: Omit<Screenshot, 'id' | 'createdAt' | 'syncStatus'>) => void;
  deleteScreenshot: (id: string) => void;
  updateSyncStatus: (id: string, status: Screenshot['syncStatus']) => void;
  setIsCapturing: (capturing: boolean) => void;
  setSelectedScreenshot: (screenshot: Screenshot | null) => void;
  updateScreenshot: (id: string, updates: Partial<Screenshot>) => void;
}

export const useScreenshotStore = create<ScreenshotState>()(
  persist(
    (set) => ({
      screenshots: [],
      recentScreenshots: [],
      isCapturing: false,
      selectedScreenshot: null,
      
      addScreenshot: (screenshotData) => {
        const newScreenshot: Screenshot = {
          ...screenshotData,
          id: Date.now().toString(),
          createdAt: Date.now(),
          syncStatus: 'local',
        };
        
        set(state => ({
          screenshots: [newScreenshot, ...state.screenshots],
          recentScreenshots: [newScreenshot, ...state.recentScreenshots].slice(0, 10),
        }));
      },
      
      deleteScreenshot: (id: string) => {
        set(state => ({
          screenshots: state.screenshots.filter(s => s.id !== id),
          recentScreenshots: state.recentScreenshots.filter(s => s.id !== id),
          selectedScreenshot: state.selectedScreenshot?.id === id ? null : state.selectedScreenshot,
        }));
      },
      
      updateSyncStatus: (id: string, status: Screenshot['syncStatus']) => {
        set(state => ({
          screenshots: state.screenshots.map(s => 
            s.id === id ? { ...s, syncStatus: status } : s
          ),
          recentScreenshots: state.recentScreenshots.map(s => 
            s.id === id ? { ...s, syncStatus: status } : s
          ),
          selectedScreenshot: state.selectedScreenshot?.id === id 
            ? { ...state.selectedScreenshot, syncStatus: status } 
            : state.selectedScreenshot,
        }));
      },
      
      setIsCapturing: (capturing: boolean) => {
        set({ isCapturing: capturing });
      },
      
      setSelectedScreenshot: (screenshot: Screenshot | null) => {
        set({ selectedScreenshot: screenshot });
      },
      
      updateScreenshot: (id: string, updates: Partial<Screenshot>) => {
        set(state => ({
          screenshots: state.screenshots.map(s => 
            s.id === id ? { ...s, ...updates } : s
          ),
          recentScreenshots: state.recentScreenshots.map(s => 
            s.id === id ? { ...s, ...updates } : s
          ),
          selectedScreenshot: state.selectedScreenshot?.id === id 
            ? { ...state.selectedScreenshot, ...updates } 
            : state.selectedScreenshot,
        }));
      },
    }),
    {
      name: 'mecap-screenshots',
      partialize: (state) => ({
        screenshots: state.screenshots,
        recentScreenshots: state.recentScreenshots,
      }),
    }
  )
);
