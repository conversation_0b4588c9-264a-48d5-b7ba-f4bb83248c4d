import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { EditorState, EditorMode, Shape, CropArea, HistoryState, ToolType, WindowState, ToolbarState } from '../types/editor';

// 扩展 Window 接口以支持 Tauri 类型
declare global {
  interface Window {
    __TAURI__?: {
      event: {
        listen: (event: string, callback: (event: any) => void) => void;
      };
    };
  }
}

const MAX_HISTORY = 50;

// 初始窗口状态
const initialWindowState: WindowState = {
  detectionMode: "none",
  selectedWindow: null,
  hoveredWindow: null
};

// 初始工具栏状态
const initialToolbarState: ToolbarState = {
  position: "bottom",
  isVisible: true,
  quickActions: []
};

export const useEditorStore = create<EditorState>()(
  persist(
    (set, get) => ({
      // 编辑器状态
      // 编辑器状态
      currentTool: 'select',
      isEditing: false,
      mode: EditorMode.WindowDetection, // 添加模式状态
      
      // 图像数据
      originalImage: '',
      editedImage: null,
      
      // 画布状态
      canvasSize: { width: 800, height: 600 },
      zoom: 1,
      
      // 绘图元素
      shapes: [],
      selectedShapeId: null,
      
      // 历史记录
      history: [],
      historyIndex: -1,
      
      // 裁剪状态
      cropArea: null,
      
      // 窗口状态
      windowState: initialWindowState,
      
      // 工具栏状态
      toolbarState: initialToolbarState,
      
      // Actions
      setCurrentTool: (tool: ToolType) => {
        set({ currentTool: tool });
      },
      
      setMode: (mode: EditorMode) => {
        set({ mode });
      },

      setOriginalImage: (imageUrl: string) => {
        set({ originalImage: imageUrl });
        // 设置图片时初始化历史记录
        get().initializeHistory();
      },

      // 初始化历史记录
      initializeHistory: () => {
        const initialState: HistoryState = {
          shapes: [],
          cropArea: null,
          timestamp: Date.now()
        };

        set({
          history: [initialState],
          historyIndex: 0
        });

        console.log('[EDITOR-STORE] 🔄 History initialized with empty state');
      },
      
      addShape: (shape: Shape) => {
        const state = get();
        const newShapes = [...state.shapes, shape];
        set({ shapes: newShapes });
        get().saveToHistory();
      },
      
      updateShape: (id: string, updates: Partial<Shape>) => {
        const state = get();
        const newShapes = state.shapes.map(shape =>
          shape.id === id ? { ...shape, ...updates } : shape
        );
        set({ shapes: newShapes });
      },
      
      deleteShape: (id: string) => {
        const state = get();
        const newShapes = state.shapes.filter(shape => shape.id !== id);
        set({
          shapes: newShapes,
          selectedShapeId: state.selectedShapeId === id ? null : state.selectedShapeId
        });
        get().saveToHistory();
      },
      
      setCropArea: (area: CropArea | null) => {
        set({ cropArea: area });
      },
      
      saveToHistory: () => {
        const state = get();
        const historyState: HistoryState = {
          shapes: [...state.shapes],
          cropArea: state.cropArea ? { ...state.cropArea } : null,
          timestamp: Date.now()
        };

        // 移除当前索引之后的历史记录（清除redo栈）
        const newHistory = state.history.slice(0, state.historyIndex + 1);
        newHistory.push(historyState);

        // 限制历史记录数量
        if (newHistory.length > MAX_HISTORY) {
          newHistory.shift();
        }

        const newIndex = newHistory.length - 1;
        set({
          history: newHistory,
          historyIndex: newIndex
        });

        console.log('[EDITOR-STORE] 📝 History saved:', {
          historyLength: newHistory.length,
          historyIndex: newIndex,
          shapesCount: state.shapes.length,
          canUndo: newIndex > 0,
          canRedo: false // redo栈被清空
        });
      },
      
      undo: () => {
        const state = get();
        if (state.historyIndex > 0) {
          const newIndex = state.historyIndex - 1;
          const historyState = state.history[newIndex];

          set({
            shapes: [...historyState.shapes],
            cropArea: historyState.cropArea ? { ...historyState.cropArea } : null,
            historyIndex: newIndex,
            selectedShapeId: null
          });

          console.log('[EDITOR-STORE] ↶ Undo executed:', {
            newIndex,
            shapesCount: historyState.shapes.length,
            canUndo: newIndex > 0,
            canRedo: true,
            timestamp: historyState.timestamp
          });
        } else {
          console.log('[EDITOR-STORE] ⚠️ Cannot undo - no history available');
        }
      },
      
      redo: () => {
        const state = get();
        if (state.historyIndex < state.history.length - 1) {
          const newIndex = state.historyIndex + 1;
          const historyState = state.history[newIndex];

          set({
            shapes: [...historyState.shapes],
            cropArea: historyState.cropArea ? { ...historyState.cropArea } : null,
            historyIndex: newIndex,
            selectedShapeId: null
          });

          console.log('[EDITOR-STORE] ↷ Redo executed:', {
            newIndex,
            shapesCount: historyState.shapes.length,
            canUndo: true,
            canRedo: newIndex < state.history.length - 1,
            timestamp: historyState.timestamp
          });
        } else {
          console.log('[EDITOR-STORE] ⚠️ Cannot redo - no future history available');
        }
      },
      
      setZoom: (zoom: number) => {
        set({ zoom: Math.max(0.1, Math.min(5, zoom)) });
      },
      
      setSelectedShapeId: (id: string | null) => {
        set({ selectedShapeId: id });
      },
      
      // 设置选中的窗口信息
      setSelectedWindow: (windowData: { x: number; y: number; width: number; height: number } | null) => {
        set(state => ({
          windowState: {
            ...state.windowState,
            selectedWindow: windowData
          }
        }));
      },
      
      // 设置窗口检测模式
      setDetectionMode: (mode: "hover"|"select"|"none") => {
        set(state => ({
          windowState: {
            ...state.windowState,
            detectionMode: mode
          }
        }));
      },
      
      // 设置工具栏位置
      setToolbarPosition: (position: "top"|"bottom"|"left"|"right") => {
        set(state => ({
          toolbarState: {
            ...state.toolbarState,
            position
          }
        }));
      }
    }),
    {
      name: "editor-storage",
      partialize: (state) => ({
        toolbarState: state.toolbarState,
        windowState: { detectionMode: state.windowState.detectionMode }
      }),
      // 注意：模式状态不需要持久化
    }
  )
);

// 监听窗口选择事件（如果Tauri可用）
if (typeof window !== 'undefined' && window.__TAURI__) {
  window.__TAURI__.event.listen('window-selected', (event: any) => {
    const { x, y, width, height } = event.payload;
    useEditorStore.getState().setSelectedWindow({ x, y, width, height });
    console.log('[EDITOR] Window coordinates received:', { x, y, width, height });
  });
}
