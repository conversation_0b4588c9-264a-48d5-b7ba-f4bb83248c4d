import { describe, it, expect, beforeEach } from 'vitest';
import { useEditorStore } from './editorStore';
import { Shape } from '../types/editor'; // ToolType removed as it's not used

describe('editorStore', () => {
  beforeEach(() => {
    useEditorStore.setState(useEditorStore.getInitialState());
  });

  it('should initialize with default values', () => {
    const state = useEditorStore.getState();
    expect(state.currentTool).toBe('select');
    expect(state.windowState.detectionMode).toBe('none');
    expect(state.toolbarState.position).toBe('bottom');
  });

  it('should add shape and save to history', () => {
    const shape: Shape = { 
      id: '1', 
      type: 'rectangle',
      x: 10, 
      y: 10,
      width: 50,
      height: 50,
      style: { stroke: '#000', strokeWidth: 2 }
    };
    
    useEditorStore.getState().addShape(shape);
    const state = useEditorStore.getState();
    
    expect(state.shapes).toHaveLength(1);
    expect(state.history).toHaveLength(1);
    expect(state.historyIndex).toBe(0);
  });

  it('should update window state', () => {
    useEditorStore.getState().setDetectionMode('hover');
    useEditorStore.getState().setSelectedWindow({ x: 100, y: 100, width: 200, height: 200 });
    
    const state = useEditorStore.getState();
    expect(state.windowState.detectionMode).toBe('hover');
    expect(state.windowState.selectedWindow).toEqual({ x: 100, y: 100, width: 200, height: 200 });
  });

  it('should persist toolbar and window state', async () => {
    // 模拟持久化
    useEditorStore.getState().setToolbarPosition('right');
    useEditorStore.getState().setDetectionMode('select');
    
    // 重置状态
    useEditorStore.setState(useEditorStore.getInitialState());
    
    // 恢复持久化状态
    const state = useEditorStore.getState();
    expect(state.toolbarState.position).toBe('right');
    expect(state.windowState.detectionMode).toBe('select');
  });
});