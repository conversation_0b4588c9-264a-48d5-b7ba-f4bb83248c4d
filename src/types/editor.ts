export enum EditorMode {
  WindowDetection = "window-detection",
  RegionSelection = "region-selection",
  Editing = "editing"
}

export type ToolType =
  | 'select'
  | 'crop'
  | 'rectangle'
  | 'ellipse'
  | 'line'
  | 'dashed-line'
  | 'arrow'
  | 'text'
  | 'pen'
  | 'mosaic'
  | 'number'
  | 'eraser';

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface Shape {
  id: string;
  type: 'rectangle' | 'ellipse' | 'line' | 'dashed-line' | 'arrow' | 'text' | 'path' | 'mosaic' | 'number';
  x: number;
  y: number;
  width?: number;
  height?: number;
  points?: number[];
  text?: string;
  number?: number;
  style: {
    stroke: string;
    strokeWidth: number;
    fill?: string;
    fontSize?: number;
    fontFamily?: string;
    dash?: number[];
    opacity?: number;
  };
}

export interface HistoryState {
  shapes: Shape[];
  cropArea: CropArea | null;
  timestamp: number;
}

export interface WindowState {
  detectionMode: "hover"|"select"|"none";
  selectedWindow: { x: number; y: number; width: number; height: number } | null;
  hoveredWindow: { x: number; y: number; width: number; height: number } | null;
}

export interface ToolbarState {
  position: "top"|"bottom"|"left"|"right"|"floating";
  isVisible: boolean;
  quickActions: string[];
  coordinates?: {
    x: number;
    y: number;
  };
}

export interface EditorState {
  // 编辑器状态
  currentTool: ToolType;
  isEditing: boolean;
  mode: EditorMode; // 新增模式字段
  
  // 图像数据
  originalImage: string;
  editedImage: string | null;
  
  // 画布状态
  canvasSize: { width: number; height: number };
  zoom: number;
  
  // 绘图元素
  shapes: Shape[];
  selectedShapeId: string | null;
  
  // 历史记录
  history: HistoryState[];
  historyIndex: number;
  
  // 裁剪状态
  cropArea: CropArea | null;
  
  // 窗口和工具栏状态
  windowState: WindowState;
  toolbarState: ToolbarState;
  
  // Actions
  setCurrentTool: (tool: ToolType) => void;
  addShape: (shape: Shape) => void;
  updateShape: (id: string, updates: Partial<Shape>) => void;
  deleteShape: (id: string) => void;
  setCropArea: (area: CropArea | null) => void;
  undo: () => void;
  redo: () => void;
  saveToHistory: () => void;
  initializeHistory: () => void;
  setZoom: (zoom: number) => void;
  setSelectedShapeId: (id: string | null) => void;
  setSelectedWindow: (windowData: { x: number; y: number; width: number; height: number } | null) => void;
  setMode: (mode: EditorMode) => void; // 新增设置模式方法
  setOriginalImage: (imageUrl: string) => void; // 新增设置原始图像方法
  setDetectionMode: (mode: "hover"|"select"|"none") => void;
  setToolbarPosition: (position: "top"|"bottom"|"left"|"right") => void;
}

export interface EditorProps {
  imagePath: string;
  onSave: (editedImagePath: string) => void;
  onCancel: () => void;
  initialCropArea?: CropArea;
}
