// 工具栏相关类型定义
export interface ToolbarButton {
  id: string;
  icon: string;
  label: string;
  shortcut?: string;
  action: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'danger';
}

export interface ToolbarPosition {
  x: number;
  y: number;
}

export interface ToolbarConfig {
  position: 'top' | 'bottom' | 'left' | 'right' | 'floating';
  autoHide: boolean;
  showLabels: boolean;
  size: 'small' | 'medium' | 'large';
  theme: 'light' | 'dark' | 'auto';
}

export interface CanvasToolbarProps {
  visible: boolean;
  position: ToolbarPosition;
  onAction: (action: ToolbarActionType) => void;
  onPositionChange?: (position: ToolbarPosition) => void;
  config?: Partial<ToolbarConfig>;
  disabled?: boolean;
  selectedRegion?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}



// 默认工具栏按钮配置
export const DEFAULT_TOOLBAR_BUTTONS: ToolbarButton[] = [
  {
    id: 'rectangle',
    icon: '⬜',
    label: '矩形',
    shortcut: 'R',
    action: 'openEditor',
    variant: 'secondary'
  },
  {
    id: 'ellipse',
    icon: '⭕',
    label: '椭圆',
    shortcut: 'O',
    action: 'openEditor',
    variant: 'secondary'
  },
  {
    id: 'arrow',
    icon: '↗️',
    label: '箭头',
    shortcut: 'A',
    action: 'openEditor',
    variant: 'secondary'
  },
  {
    id: 'pen',
    icon: '✏️',
    label: '画笔',
    shortcut: 'P',
    action: 'openEditor',
    variant: 'secondary'
  },
  {
    id: 'text',
    icon: 'T',
    label: '文字',
    shortcut: 'T',
    action: 'openEditor',
    variant: 'secondary'
  },
  {
    id: 'mosaic',
    icon: '🔲',
    label: '马赛克',
    shortcut: 'M',
    action: 'openEditor',
    variant: 'secondary'
  },
  {
    id: 'number',
    icon: '①',
    label: '序号',
    shortcut: 'N',
    action: 'openEditor',
    variant: 'secondary'
  },
  {
    id: 'undo',
    icon: '↶',
    label: '撤销',
    shortcut: 'Ctrl+Z',
    action: 'undo',
    variant: 'secondary'
  },
  {
    id: 'download',
    icon: '💾',
    label: '保存',
    shortcut: 'Ctrl+S',
    action: 'confirmCapture',
    variant: 'primary'
  },
  {
    id: 'cancel',
    icon: '❌',
    label: '取消',
    shortcut: 'Esc',
    action: 'cancelCapture',
    variant: 'danger'
  },
  {
    id: 'confirm',
    icon: '✓',
    label: '确认',
    shortcut: 'Enter',
    action: 'confirmCapture',
    variant: 'primary'
  }
];

// 默认工具栏配置
export const DEFAULT_TOOLBAR_CONFIG: ToolbarConfig = {
  position: 'floating',
  autoHide: false,
  showLabels: true,
  size: 'medium',
  theme: 'auto'
};

// 工具栏事件类型
export type ToolbarActionType =
  | 'confirmCapture'
  | 'cancelCapture'
  | 'openEditor'
  | 'copyToClipboard'
  | 'saveToFile'
  | 'showMoreOptions'
  | 'dragStart'
  | 'dragEnd'
  | 'save'
  | 'copy'
  | 'edit'
  | 'undo'
  | 'redo'
  | 'close';

export interface ToolbarActionEvent {
  action: ToolbarActionType;
  data?: any;
  timestamp: number;
}
