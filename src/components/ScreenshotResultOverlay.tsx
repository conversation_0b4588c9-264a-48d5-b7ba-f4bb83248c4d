import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Box, Fade, useTheme, alpha } from '@mui/material';
import { invoke } from '@tauri-apps/api/core';
import CanvasToolbar from './CanvasToolbarReact';
import { ToolbarPosition, ToolbarActionType } from '../types/toolbar';
import { useEditorStore } from '../store/editorStore';
import { EditorMode } from '../types/editor';
import { IntelligentToolbarPositioner, ScreenBounds, ScreenshotBounds } from '../utils/IntelligentToolbarPositioner';
import { CollisionDetector } from '../utils/CollisionDetector';

interface ScreenshotResultOverlayProps {
  visible: boolean;
  screenshotData?: {
    base64: string;
    width: number;
    height: number;
    x: number;
    y: number;
  };
  onComplete?: (savedPath?: string) => void;
  onCancel?: () => void;
}

export const ScreenshotResultOverlay: React.FC<ScreenshotResultOverlayProps> = ({
  visible,
  screenshotData,
  onComplete,
  onCancel
}) => {
  const theme = useTheme();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [toolbarPosition, setToolbarPosition] = useState<ToolbarPosition>({ x: 0, y: 0 });
  const [isProcessing, setIsProcessing] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  // 智能定位系统
  const positionerRef = useRef<IntelligentToolbarPositioner | null>(null);
  const collisionDetectorRef = useRef<CollisionDetector | null>(null);

  const { setMode, setOriginalImage } = useEditorStore();

  // 调试日志
  useEffect(() => {
    console.log('[OVERLAY] 🔧 ScreenshotResultOverlay render state update');
    console.log('[OVERLAY] 🔧 visible:', visible);
    console.log('[OVERLAY] 🔧 hasScreenshotData:', !!screenshotData);
    console.log('[OVERLAY] 🔧 screenshotData:', screenshotData);

    if (screenshotData) {
      console.log('[OVERLAY] 🔧 screenshotData details:', {
        x: screenshotData.x,
        y: screenshotData.y,
        width: screenshotData.width,
        height: screenshotData.height,
        hasBase64: !!screenshotData.base64,
        base64Length: screenshotData.base64?.length || 0
      });
    }

    console.log('[OVERLAY] 🔧 Component will render:', visible && !!screenshotData);
  }, [visible, screenshotData]);

  // 初始化智能定位系统
  useEffect(() => {
    const screenBounds: ScreenBounds = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    const toolbarSize = { width: 400, height: 60 };

    positionerRef.current = new IntelligentToolbarPositioner(screenBounds, toolbarSize);
    collisionDetectorRef.current = new CollisionDetector(screenBounds, toolbarSize);

    console.log('[OVERLAY] 🎯 Intelligent positioning system initialized');
  }, []);

  // 智能计算工具栏位置
  useEffect(() => {
    if (screenshotData && visible && positionerRef.current) {
      const screenshotBounds: ScreenshotBounds = {
        x: screenshotData.x,
        y: screenshotData.y,
        width: screenshotData.width,
        height: screenshotData.height
      };

      console.log('[OVERLAY] 🎯 Calculating optimal toolbar position');
      const optimalPosition = positionerRef.current.calculateOptimalPosition(screenshotBounds);

      // 暂时禁用碰撞检测以避免无限循环
      console.log('[OVERLAY] 🎯 Using optimal position without collision detection:', optimalPosition);
      setToolbarPosition(optimalPosition);
    }
  }, [screenshotData, visible]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (positionerRef.current && collisionDetectorRef.current) {
        const newScreenBounds: ScreenBounds = {
          width: window.innerWidth,
          height: window.innerHeight
        };

        // 更新定位系统的屏幕边界
        positionerRef.current.updateScreenBounds(newScreenBounds);
        collisionDetectorRef.current.updateScreenBounds(newScreenBounds);

        console.log('[OVERLAY] 📐 Screen bounds updated:', newScreenBounds);

        // 重新计算工具栏位置
        if (screenshotData && visible) {
          const screenshotBounds: ScreenshotBounds = {
            x: screenshotData.x,
            y: screenshotData.y,
            width: screenshotData.width,
            height: screenshotData.height
          };

          const newPosition = positionerRef.current.calculateOptimalPosition(screenshotBounds);
          const collisionInfo = collisionDetectorRef.current.detectCollisions(
            newPosition,
            screenshotBounds
          );

          const finalPosition = collisionInfo.suggestedAdjustment || newPosition;
          console.log('[OVERLAY] 📐 Repositioned toolbar after resize:', finalPosition);
          setToolbarPosition(finalPosition);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [screenshotData, visible]);

  // 初始化Canvas显示截图
  useEffect(() => {
    console.log('[OVERLAY] 🔧 Canvas useEffect triggered:', {
      hasScreenshotData: !!screenshotData,
      hasCanvas: !!canvasRef.current,
      visible: visible,
      base64: screenshotData?.base64?.substring(0, 50) + '...'
    });

    if (screenshotData && canvasRef.current && visible) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('[OVERLAY] ❌ Failed to get canvas context');
        return;
      }

      console.log('[OVERLAY] 🔧 Setting canvas dimensions:', screenshotData.width, 'x', screenshotData.height);

      // 设置Canvas尺寸
      canvas.width = screenshotData.width;
      canvas.height = screenshotData.height;

      // 创建图像对象并绘制
      const img = new Image();
      img.onload = () => {
        console.log('[OVERLAY] 🔧 Image onload triggered, drawing to canvas');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0);
        console.log('[OVERLAY] ✅ Image loaded and drawn to canvas');
      };

      img.onerror = (error) => {
        console.error('[OVERLAY] ❌ Failed to load image:', error);
        console.error('[OVERLAY] ❌ Image src was:', img.src);
      };

      // 检查是否是文件URL还是base64数据 - 修复asset://协议检测
      if (screenshotData.base64.startsWith('http') ||
          screenshotData.base64.startsWith('tauri://') ||
          screenshotData.base64.startsWith('asset://')) {
        // 文件URL（新的文件基础方法）
        console.log('[OVERLAY] 🔧 Loading image from file URL:', screenshotData.base64);
        img.src = screenshotData.base64;
        setOriginalImage(screenshotData.base64);
      } else if (screenshotData.base64.startsWith('data:')) {
        // 已经是完整的data URL
        console.log('[OVERLAY] 🔧 Loading image from data URL');
        img.src = screenshotData.base64;
        setOriginalImage(screenshotData.base64);
      } else {
        // Base64数据（旧方法的兼容性）
        console.log('[OVERLAY] 🔧 Loading image from base64 data');
        img.src = `data:image/png;base64,${screenshotData.base64}`;
        setOriginalImage(`data:image/png;base64,${screenshotData.base64}`);
      }
    } else {
      console.log('[OVERLAY] 🔧 Canvas useEffect conditions not met');
    }
  }, [screenshotData, visible, setOriginalImage]);



  // 处理工具栏操作
  const handleToolbarAction = useCallback(async (action: ToolbarActionType) => {
    if (isProcessing || !screenshotData) return;
    
    setIsProcessing(true);
    
    try {
      switch (action) {
        case 'confirmCapture':
          console.log('[RESULT_OVERLAY] Confirming capture - saving screenshot');
          // 获取正确的图像数据URL
          let imageDataUrl: string;
          if (screenshotData.base64.startsWith('http') || screenshotData.base64.startsWith('tauri://')) {
            // 文件URL - 需要从canvas获取数据
            const canvas = canvasRef.current;
            if (canvas) {
              imageDataUrl = canvas.toDataURL('image/png');
            } else {
              throw new Error('Canvas not available for file-based screenshot');
            }
          } else {
            // Base64数据
            imageDataUrl = `data:image/png;base64,${screenshotData.base64}`;
          }

          const saveResult = await invoke('save_edited_screenshot', {
            imageDataUrl: imageDataUrl,
            finalPath: null
          });

          console.log('[RESULT_OVERLAY] Screenshot saved:', saveResult);
          onComplete?.((saveResult as any).path);
          break;
          
        case 'cancelCapture':
          console.log('[RESULT_OVERLAY] Cancelling capture');
          onCancel?.();
          break;
          
        case 'openEditor':
          console.log('[RESULT_OVERLAY] Entering edit mode');
          setIsEditMode(true);
          setMode(EditorMode.Editing);
          break;
          
        case 'copyToClipboard':
          console.log('[RESULT_OVERLAY] Copying to clipboard');
          // 获取正确的图像数据URL
          let clipboardImageDataUrl: string;
          if (screenshotData.base64.startsWith('http') || screenshotData.base64.startsWith('tauri://')) {
            // 文件URL - 需要从canvas获取数据
            const canvas = canvasRef.current;
            if (canvas) {
              clipboardImageDataUrl = canvas.toDataURL('image/png');
            } else {
              throw new Error('Canvas not available for file-based screenshot');
            }
          } else {
            // Base64数据
            clipboardImageDataUrl = `data:image/png;base64,${screenshotData.base64}`;
          }

          await invoke('copy_edited_screenshot_to_clipboard', {
            imageDataUrl: clipboardImageDataUrl
          });
          break;
          
        default:
          console.log(`[RESULT_OVERLAY] Unhandled action: ${action}`);
          break;
      }
    } catch (error) {
      console.error(`[RESULT_OVERLAY] Action failed: ${action}`, error);
    } finally {
      setIsProcessing(false);
    }
  }, [screenshotData, onComplete, onCancel, isProcessing, setMode]);

  // 处理工具栏位置变化（简化版，避免循环）
  const handlePositionChange = useCallback((newPosition: ToolbarPosition) => {
    console.log('[OVERLAY] 🎯 Toolbar position change requested:', newPosition);
    setToolbarPosition(newPosition);
  }, []);

  if (!visible || !screenshotData) {
    console.log('[OVERLAY] 🔧 Component returning null - visible:', visible, 'screenshotData:', !!screenshotData);
    return null;
  }

  console.log('[OVERLAY] 🎯 Rendering ScreenshotResultOverlay with:', {
    visible,
    screenshotData: {
      x: screenshotData.x,
      y: screenshotData.y,
      width: screenshotData.width,
      height: screenshotData.height
    },
    toolbarPosition,
    isProcessing
  });

  console.log('[OVERLAY] 🔧 Component will render overlay with data:', {
    x: screenshotData.x,
    y: screenshotData.y,
    width: screenshotData.width,
    height: screenshotData.height
  });

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: alpha('#000', 0.3),
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      {/* 截图显示区域 */}
      <Fade in={visible} timeout={300}>
        <Box
          sx={{
            position: 'absolute',
            left: screenshotData.x,
            top: screenshotData.y,
            width: screenshotData.width,
            height: screenshotData.height,
            border: `2px solid ${theme.palette.primary.main}`,
            borderRadius: 1,
            overflow: 'hidden',
            boxShadow: theme.shadows[8]
          }}
        >
          <canvas
            ref={canvasRef}
            style={{
              width: '100%',
              height: '100%',
              display: 'block'
            }}
          />
        </Box>
      </Fade>

      {/* 尺寸信息显示 */}
      <Fade in={visible} timeout={200}>
        <Box
          sx={{
            position: 'absolute',
            top: screenshotData.y - 35,
            left: screenshotData.x,
            backgroundColor: alpha(theme.palette.background.paper, 0.95),
            color: theme.palette.text.primary,
            px: 2,
            py: 1,
            borderRadius: 1,
            fontSize: '0.875rem',
            fontFamily: 'monospace',
            border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
            backdropFilter: 'blur(10px)',
            boxShadow: theme.shadows[4]
          }}
        >
          {screenshotData.width} × {screenshotData.height}
        </Box>
      </Fade>

      {/* Canvas工具栏 */}
      <CanvasToolbar
        visible={visible}
        position={toolbarPosition}
        onAction={handleToolbarAction}
        onPositionChange={handlePositionChange}
        disabled={isProcessing}
      />

      {/* 编辑模式提示 */}
      {isEditMode && (
        <Fade in={isEditMode} timeout={200}>
          <Box
            sx={{
              position: 'absolute',
              top: 20,
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: alpha(theme.palette.info.main, 0.9),
              color: 'white',
              px: 3,
              py: 1,
              borderRadius: 2,
              fontSize: '0.875rem',
              fontWeight: 'medium',
              boxShadow: theme.shadows[6]
            }}
          >
            编辑模式已激活 - 使用工具栏进行标注
          </Box>
        </Fade>
      )}
    </Box>
  );
};

export default ScreenshotResultOverlay;
