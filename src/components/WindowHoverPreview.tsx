import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { invoke } from '@tauri-apps/api/core';
import { WindowToolbar } from './WindowToolbar';

export interface WindowInfo {
  id: number; // Changed to number to match <PERSON>ust's u64 (JavaScript numbers can represent u64 up to 2^53)
  title: string;
  owner_name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  is_minimized: boolean;
  monitor_id: number; // Add monitor ID for multi-monitor support
}

interface WindowHoverPreviewProps {
  isActive: boolean;
  onWindowSelected?: (window: WindowInfo) => void;
  onSave?: () => void;
  onCancel?: () => void;
}

export const WindowHoverPreview: React.FC<WindowHoverPreviewProps> = ({
  isActive,
  onWindowSelected,
  onSave,
  onCancel
}) => {
  const [hoveredWindow, setHoveredWindow] = useState<WindowInfo | null>(null);
  const [selectedWindow, setSelectedWindow] = useState<WindowInfo | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const throttleRef = useRef<NodeJS.Timeout | null>(null);
  const lastClickTimeRef = useRef<number>(0); // For rapid click detection
  const [windowMinimized, setWindowMinimized] = useState(false); // Track if window is minimized

  // 节流的窗口检测函数
  const detectWindowUnderCursor = useCallback(async (x: number, y: number) => {
    try {
      const window = await invoke<WindowInfo | null>('modules::window::detect_window_under_mouse', { x, y });
      setHoveredWindow(window);
      
      // Check if selected window is minimized
      if (selectedWindow && window && window.id === selectedWindow.id && window.is_minimized) {
        setWindowMinimized(true);
      }
    } catch (error) {
      console.error('Failed to detect window under cursor:', error);
      setHoveredWindow(null);
    }
  }, [selectedWindow]);

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isActive || selectedWindow) return;

    const x = e.screenX;
    const y = e.screenY;
    setMousePosition({ x, y });

    // 节流处理，避免过于频繁的API调用
    if (throttleRef.current) {
      clearTimeout(throttleRef.current);
    }

    throttleRef.current = setTimeout(() => {
      detectWindowUnderCursor(x, y);
    }, 100); // 100ms节流
  }, [isActive, selectedWindow, detectWindowUnderCursor]);

  // 处理点击事件 (with rapid click protection)
  const handleClick = useCallback((e: MouseEvent) => {
    if (!isActive || !hoveredWindow || selectedWindow) return;

    // Rapid click protection (300ms cooldown)
    const now = Date.now();
    if (now - lastClickTimeRef.current < 300) return;
    lastClickTimeRef.current = now;

    // Check if window is minimized
    if (hoveredWindow.is_minimized) {
      console.warn('Cannot select minimized window');
      return;
    }

    // Only prevent default if we're handling the click
    e.preventDefault();
    
    setSelectedWindow(hoveredWindow);
    setWindowMinimized(false);
    if (onWindowSelected) {
      onWindowSelected(hoveredWindow);
    }

    // Allow event to propagate after handling
  }, [isActive, hoveredWindow, selectedWindow, onWindowSelected]);

  // 处理保存操作
  const handleSave = useCallback(() => {
    if (onSave) onSave();
    setSelectedWindow(null);
  }, [onSave]);

  // 处理取消操作
  const handleCancel = useCallback(() => {
    if (onCancel) onCancel();
    setSelectedWindow(null);
  }, [onCancel]);

  // Handle window minimization during capture
  useEffect(() => {
    if (windowMinimized && selectedWindow) {
      console.log('Window minimized during capture, canceling...');
      handleCancel();
    }
  }, [windowMinimized, selectedWindow, handleCancel]);

  // 设置全局事件监听器
  useEffect(() => {
    if (!isActive) {
      setHoveredWindow(null);
      setSelectedWindow(null);
      setWindowMinimized(false);
      return;
    }

    // 启动窗口悬停模式
    invoke('modules::window::start_smart_window_detection').catch(console.error);

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('click', handleClick);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('click', handleClick);
      
      // 停止窗口悬停模式
      invoke('modules::window::stop_smart_window_detection').catch(console.error);
      
      if (throttleRef.current) {
        clearTimeout(throttleRef.current);
      }
    };
  }, [isActive, handleMouseMove, handleClick]);

  // 处理键盘事件（ESC退出）
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isActive && e.key === 'Escape') {
        e.preventDefault();
        e.stopPropagation();
        handleCancel();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isActive, handleCancel]);

  // 如果不活跃，不渲染任何内容
  if (!isActive) {
    return null;
  }

  // 使用选中的窗口或悬停的窗口
  const activeWindow = selectedWindow || hoveredWindow;
  if (!activeWindow) return null;

  return (
    <>
      {/* 全屏半透明遮罩层 */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          backgroundColor: 'rgba(0, 0, 0, 0.7)', // 70% 不透明度
          zIndex: 9997,
          pointerEvents: 'none',
        }}
      />

      {/* 窗口高亮边框 */}
      <Box
        sx={{
          position: 'fixed',
          left: activeWindow.x,
          top: activeWindow.y,
          width: activeWindow.width,
          height: activeWindow.height,
          border: '3px solid #3498db', // 2px 高亮边框 (#3498db)
          borderRadius: '4px',
          pointerEvents: 'none',
          zIndex: 9998,
          boxShadow: '0 0 20px rgba(52, 152, 219, 0.5)',
          animation: 'pulse 2s infinite',
          '@keyframes pulse': {
            '0%': {
              boxShadow: '0 0 20px rgba(52, 152, 219, 0.5)',
            },
            '50%': {
              boxShadow: '0 0 30px rgba(52, 152, 219, 0.8)',
            },
            '100%': {
              boxShadow: '0 0 20px rgba(52, 152, 219, 0.5)',
            },
          },
        }}
      />

      {/* 窗口信息提示 */}
      {!selectedWindow && (
        <Paper
          elevation={8}
          sx={{
            position: 'fixed',
            left: Math.min(mousePosition.x + 15, window.innerWidth - 250),
            top: Math.max(mousePosition.y - 60, 10),
            padding: '8px 12px',
            backgroundColor: 'rgba(50, 50, 50, 0.8)',
            color: 'white',
            borderRadius: '6px',
            pointerEvents: 'none',
            zIndex: 9999,
            maxWidth: '250px',
          }}
        >
          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
            {activeWindow.title}
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.8 }}>
            {activeWindow.owner_name}
          </Typography>
          <Typography variant="caption" sx={{ display: 'block', opacity: 0.6, mt: 0.5 }}>
            {activeWindow.width} × {activeWindow.height}
          </Typography>
          <Typography variant="caption" sx={{ display: 'block', opacity: 0.6, fontSize: '10px' }}>
            点击选择此窗口
          </Typography>
        </Paper>
      )}

      {/* 编辑工具栏 */}
      {selectedWindow && !windowMinimized && (
        <WindowToolbar
          windowRect={{
            x: selectedWindow.x,
            y: selectedWindow.y,
            width: selectedWindow.width,
            height: selectedWindow.height
          }}
          onSave={handleSave}
          onCancel={handleCancel}
          onToolSelect={(tool) => console.log('Tool selected:', tool)}
        />
      )}
    </>
  );
};
