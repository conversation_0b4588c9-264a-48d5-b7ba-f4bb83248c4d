import React, { useState, useRef } from 'react';
import { Rect } from 'react-konva';
import { useEditorStore } from '../../store/editorStore';

interface MultiSelectManagerProps {
  stageRef: React.RefObject<any>;
}

export const MultiSelectManager: React.FC<MultiSelectManagerProps> = ({
  stageRef
}) => {
  const [selectionRect, setSelectionRect] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const selectionStartRef = useRef<{ x: number; y: number } | null>(null);
  
  const {
    currentTool,
    shapes,
    setSelectedShapeId
  } = useEditorStore();

  // 处理选择框开始
  const handleSelectionStart = (e: any) => {
    if (currentTool !== 'select') return;
    
    // 检查是否点击在空白区域
    const clickedOnEmpty = e.target === e.target.getStage();
    if (!clickedOnEmpty) return;

    const stage = stageRef.current;
    if (!stage) return;

    const pos = stage.getPointerPosition();
    selectionStartRef.current = pos;
    setIsSelecting(true);
    setSelectionRect({
      x: pos.x,
      y: pos.y,
      width: 0,
      height: 0
    });

    console.log('[MULTI-SELECT] 🎯 Selection started at:', pos);
  };

  // 处理选择框拖拽
  const handleSelectionMove = (_e: any) => {
    if (!isSelecting || !selectionStartRef.current || currentTool !== 'select') return;

    const stage = stageRef.current;
    if (!stage) return;

    const pos = stage.getPointerPosition();
    const startPos = selectionStartRef.current;

    const x = Math.min(startPos.x, pos.x);
    const y = Math.min(startPos.y, pos.y);
    const width = Math.abs(pos.x - startPos.x);
    const height = Math.abs(pos.y - startPos.y);

    setSelectionRect({ x, y, width, height });
  };

  // 处理选择框结束
  const handleSelectionEnd = () => {
    if (!isSelecting || !selectionRect || currentTool !== 'select') return;

    setIsSelecting(false);

    // 如果选择框太小，视为点击
    if (selectionRect.width < 5 && selectionRect.height < 5) {
      setSelectionRect(null);
      setSelectedShapeId(null);
      return;
    }

    // 查找与选择框相交的形状
    const selectedShapes = shapes.filter(shape => {
      return isShapeIntersectingRect(shape, selectionRect);
    });

    console.log('[MULTI-SELECT] 📦 Selection completed:', {
      rect: selectionRect,
      selectedShapes: selectedShapes.map(s => s.id)
    });

    // 如果只选中一个形状，设置为当前选中
    if (selectedShapes.length === 1) {
      setSelectedShapeId(selectedShapes[0].id);
    } else if (selectedShapes.length > 1) {
      // 多选功能暂时选择第一个
      setSelectedShapeId(selectedShapes[0].id);
      // TODO: 实现真正的多选功能
    } else {
      setSelectedShapeId(null);
    }

    setSelectionRect(null);
    selectionStartRef.current = null;
  };

  // 检查形状是否与矩形相交
  const isShapeIntersectingRect = (shape: any, rect: any): boolean => {
    const shapeLeft = shape.x;
    const shapeTop = shape.y;
    const shapeRight = shape.x + (shape.width || 0);
    const shapeBottom = shape.y + (shape.height || 0);

    const rectLeft = rect.x;
    const rectTop = rect.y;
    const rectRight = rect.x + rect.width;
    const rectBottom = rect.y + rect.height;

    // 检查是否有重叠
    return !(shapeRight < rectLeft || 
             shapeLeft > rectRight || 
             shapeBottom < rectTop || 
             shapeTop > rectBottom);
  };

  // 绑定事件到stage
  React.useEffect(() => {
    const stage = stageRef.current;
    if (!stage) return;

    const handleMouseDown = (e: any) => handleSelectionStart(e);
    const handleMouseMove = (e: any) => handleSelectionMove(e);
    const handleMouseUp = () => handleSelectionEnd();

    stage.on('mousedown', handleMouseDown);
    stage.on('mousemove', handleMouseMove);
    stage.on('mouseup', handleMouseUp);

    return () => {
      stage.off('mousedown', handleMouseDown);
      stage.off('mousemove', handleMouseMove);
      stage.off('mouseup', handleMouseUp);
    };
  }, [isSelecting, currentTool]);

  // 只在选择模式下显示选择框
  if (currentTool !== 'select' || !selectionRect) {
    return null;
  }

  return (
    <Rect
      x={selectionRect.x}
      y={selectionRect.y}
      width={selectionRect.width}
      height={selectionRect.height}
      fill="rgba(33, 150, 243, 0.1)"
      stroke="#2196f3"
      strokeWidth={1}
      dash={[5, 5]}
      listening={false}
    />
  );
};
