import React, { useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  Button,
  Box,
  Toolbar,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  Save as SaveIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  CropFree as CropIcon,
  Rectangle as RectangleIcon,
  RadioButtonUnchecked as EllipseIcon,
  Settings as SettingsIcon,
  Remove as LineIcon,
  ArrowForward as ArrowIcon,
  TextFields as TextIcon,
  Brush as BrushIcon,
  PanTool as SelectIcon,
  Check as CheckIcon,
  Cancel as CancelIcon,
  BlurOn as MosaicIcon,
  LooksOne as NumberIcon,
  CleaningServices as EraserIcon
} from '@mui/icons-material';
import { EditorCanvas } from './EditorCanvas';
import { ColorPicker } from './ColorPicker';
import { ToolParametersPanel } from './ToolParametersPanel';
import CanvasToolbar from '../CanvasToolbar';
import { useEditorStore } from '../../store/editorStore';
import { EditorProps, ToolType } from '../../types/editor';
import { ToolbarActionType } from '../../types/toolbar';
import { saveAnnotationProject, copyCanvasToClipboard } from '../../utils/canvasExport';
import { useKeyboardShortcuts } from '../../hooks/useKeyboardShortcuts';


export const ScreenshotEditor: React.FC<EditorProps> = ({
  imagePath,
  onSave,
  onCancel,
  initialCropArea
}) => {
  const [currentColor, setCurrentColor] = React.useState('#ff0000');
  const [currentStrokeWidth, setCurrentStrokeWidth] = React.useState(2);
  const [currentFontSize, setCurrentFontSize] = React.useState(16);
  const [currentFontFamily, setCurrentFontFamily] = React.useState('Arial');
  const [isSaving, setIsSaving] = React.useState(false);
  const [showToolbar] = React.useState(true);
  const [showParametersPanel, setShowParametersPanel] = React.useState(false);
  const [toolbarPosition, setToolbarPosition] = React.useState({ x: 50, y: 50 });
  const stageRef = React.useRef<any>(null);
  const {
    currentTool,
    setCurrentTool,
    history,
    historyIndex,
    undo,
    redo,
    cropArea,
    setCropArea,
    saveToHistory,
    shapes
  } = useEditorStore();

  // 初始化编辑器
  useEffect(() => {
    if (initialCropArea) {
      // TODO: 设置初始裁剪区域
    }
  }, [initialCropArea]);

  const handleToolChange = (tool: ToolType) => {
    setCurrentTool(tool);
  };

  const handleCropConfirm = () => {
    if (cropArea) {
      saveToHistory();
      setCurrentTool('select');
    }
  };

  const handleCropCancel = () => {
    setCropArea(null);
    setCurrentTool('select');
  };

  const handleSave = async () => {
    if (!stageRef.current || isSaving) return;

    setIsSaving(true);
    try {
      const stage = stageRef.current;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `screenshot-${timestamp}`;

      console.log('[EDITOR] 💾 Starting save process with annotations');

      // 保存完整的标注项目（图片 + 元数据）
      const result = await saveAnnotationProject(
        stage,
        shapes,
        cropArea,
        filename
      );

      console.log('[EDITOR] ✅ Screenshot with annotations saved:', result);
      onSave(result.imagePath);
    } catch (error) {
      console.error('[EDITOR] ❌ Failed to save screenshot:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      alert(`保存失败: ${errorMessage}`);
    } finally {
      setIsSaving(false);
    }
  };

  // 集成快捷键支持
  useKeyboardShortcuts({
    onSave: handleSave,
    onCopy: async () => {
      if (stageRef.current) {
        try {
          await copyCanvasToClipboard(stageRef.current, cropArea);
          console.log('[EDITOR] ✅ Canvas copied to clipboard via shortcut');
        } catch (error) {
          console.error('[EDITOR] ❌ Failed to copy via shortcut:', error);
        }
      }
    },
    onCancel: onCancel,
    onDelete: () => {
      console.log('[EDITOR] 🗑️ Delete triggered via shortcut');
    }
  });

  // 处理工具栏操作
  const handleToolbarAction = async (action: ToolbarActionType) => {
    switch (action) {
      case 'saveToFile':
        await handleSave();
        break;
      case 'cancelCapture':
        onCancel();
        break;
      case 'copyToClipboard':
        if (stageRef.current) {
          try {
            await copyCanvasToClipboard(stageRef.current, cropArea);
            console.log('[EDITOR] ✅ Canvas copied to clipboard');
          } catch (error) {
            console.error('[EDITOR] ❌ Failed to copy to clipboard:', error);
            alert('复制到剪贴板失败');
          }
        }
        break;
      default:
        console.log(`Unhandled toolbar action: ${action}`);
        break;
    }
  };

  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  const tools = [
    { id: 'select' as ToolType, icon: SelectIcon, label: '选择' },
    { id: 'crop' as ToolType, icon: CropIcon, label: '裁剪' },
    { id: 'rectangle' as ToolType, icon: RectangleIcon, label: '矩形' },
    { id: 'ellipse' as ToolType, icon: EllipseIcon, label: '椭圆' },
    { id: 'line' as ToolType, icon: LineIcon, label: '直线' },
    { id: 'dashed-line' as ToolType, icon: LineIcon, label: '虚线' },
    { id: 'arrow' as ToolType, icon: ArrowIcon, label: '箭头' },
    { id: 'text' as ToolType, icon: TextIcon, label: '文字' },
    { id: 'pen' as ToolType, icon: BrushIcon, label: '画笔' },
    { id: 'mosaic' as ToolType, icon: MosaicIcon, label: '马赛克' },
    { id: 'number' as ToolType, icon: NumberIcon, label: '序号' },
    { id: 'eraser' as ToolType, icon: EraserIcon, label: '橡皮擦' }
  ];

  return (
    <Dialog
      open={true}
      onClose={onCancel}
      maxWidth={false}
      fullWidth
      slotProps={{
        paper: {
          sx: {
            width: '90vw',
            height: '90vh',
            maxWidth: 'none',
            maxHeight: 'none'
          }
        }
      }}
    >
      <DialogTitle sx={{ p: 1 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>截图编辑器 {isSaving && '- 保存中...'}</Box>
          <IconButton onClick={onCancel} size="small" disabled={isSaving}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      {/* 工具栏 */}
      <Toolbar variant="dense" sx={{ borderBottom: 1, borderColor: 'divider' }}>
        {/* 撤销重做 */}
        <Tooltip title="撤销">
          <span>
            <IconButton 
              onClick={undo} 
              disabled={!canUndo}
              size="small"
            >
              <UndoIcon />
            </IconButton>
          </span>
        </Tooltip>
        
        <Tooltip title="重做">
          <span>
            <IconButton 
              onClick={redo} 
              disabled={!canRedo}
              size="small"
            >
              <RedoIcon />
            </IconButton>
          </span>
        </Tooltip>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 工具按钮 */}
        {tools.map((tool) => {
          const IconComponent = tool.icon;
          return (
            <Tooltip key={tool.id} title={tool.label}>
              <IconButton
                onClick={() => handleToolChange(tool.id)}
                color={currentTool === tool.id ? 'primary' : 'default'}
                size="small"
                sx={{
                  backgroundColor: currentTool === tool.id ? 'action.selected' : 'transparent'
                }}
              >
                <IconComponent />
              </IconButton>
            </Tooltip>
          );
        })}

        {/* 裁剪工具的确认和取消按钮 */}
        {currentTool === 'crop' && (
          <>
            <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
            <Tooltip title="确认裁剪">
              <IconButton
                onClick={handleCropConfirm}
                color="success"
                size="small"
                sx={{ backgroundColor: 'success.light', color: 'success.contrastText' }}
              >
                <CheckIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="取消裁剪">
              <IconButton
                onClick={handleCropCancel}
                color="error"
                size="small"
                sx={{ backgroundColor: 'error.light', color: 'error.contrastText' }}
              >
                <CancelIcon />
              </IconButton>
            </Tooltip>
          </>
        )}

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 颜色选择器 */}
        <ColorPicker
          currentColor={currentColor}
          currentStrokeWidth={currentStrokeWidth}
          onColorChange={setCurrentColor}
          onStrokeWidthChange={setCurrentStrokeWidth}
        />

        <Box sx={{ flexGrow: 1 }} />

        {/* 工具参数设置按钮 */}
        <Tooltip title="工具参数设置">
          <IconButton
            onClick={() => setShowParametersPanel(!showParametersPanel)}
            size="small"
            color={showParametersPanel ? 'primary' : 'default'}
          >
            <SettingsIcon />
          </IconButton>
        </Tooltip>

        {/* 保存按钮 */}
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={isSaving}
          size="small"
        >
          {isSaving ? '保存中...' : '保存'}
        </Button>
      </Toolbar>

      <DialogContent sx={{ p: 0, flex: 1, position: 'relative' }}>
        <EditorCanvas
          imagePath={imagePath}
          currentColor={currentColor}
          currentStrokeWidth={currentStrokeWidth}
          currentFontSize={currentFontSize}
          currentFontFamily={currentFontFamily}
          stageRef={stageRef}
        />

        {/* 浮动工具栏 */}
        {showToolbar && (
          <CanvasToolbar
            visible={showToolbar}
            position={toolbarPosition}
            onAction={handleToolbarAction}
            onPositionChange={setToolbarPosition}
            disabled={isSaving}
            config={{
              position: 'floating',
              showLabels: false,
              size: 'small',
              theme: 'auto'
            }}
          />
        )}

        {/* 工具参数面板 */}
        <ToolParametersPanel
          visible={showParametersPanel}
          onClose={() => setShowParametersPanel(false)}
          currentColor={currentColor}
          onColorChange={setCurrentColor}
          currentStrokeWidth={currentStrokeWidth}
          onStrokeWidthChange={setCurrentStrokeWidth}
          currentFontSize={currentFontSize}
          onFontSizeChange={setCurrentFontSize}
          currentFontFamily={currentFontFamily}
          onFontFamilyChange={setCurrentFontFamily}
        />
      </DialogContent>
    </Dialog>
  );
};
