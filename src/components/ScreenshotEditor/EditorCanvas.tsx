import React, { useRef, useEffect, useState } from 'react';
import { Stage, Layer, Image as KonvaImage } from 'react-konva';
import { Box } from '@mui/material';
import { useEditorStore } from '../../store/editorStore';
import { CropTool } from './CropTool';
import { DrawingTools } from './DrawingTools';
import { TextTool } from './TextTool';
import { ShapeTransformer } from './ShapeTransformer';
import { MultiSelectManager } from './MultiSelectManager';
import useImage from 'use-image';

interface EditorCanvasProps {
  imagePath: string;
  currentColor?: string;
  currentStrokeWidth?: number;
  currentFontSize?: number;
  currentFontFamily?: string;
  stageRef?: React.RefObject<any>;
}

export const EditorCanvas: React.FC<EditorCanvasProps> = ({
  imagePath,
  currentColor,
  currentStrokeWidth,
  currentFontSize = 16,
  currentFontFamily = 'Arial',
  stageRef: externalStageRef
}) => {
  const internalStageRef = useRef<any>(null);
  const stageRef = externalStageRef || internalStageRef;
  const [image] = useImage(imagePath, 'anonymous');
  const [stageSize, setStageSize] = useState({ width: 800, height: 600 });
  
  const {
    zoom,
    setZoom,
    currentTool,
    selectedShapeId,
    setSelectedShapeId
  } = useEditorStore();

  // 计算图像在画布中的位置和大小
  const getImageDimensions = () => {
    if (!image) return { x: 0, y: 0, width: 0, height: 0, scale: 1 };
    
    const imageRatio = image.width / image.height;
    const containerRatio = stageSize.width / stageSize.height;
    
    let scale = 1;
    let width = image.width;
    let height = image.height;
    
    // 计算适合容器的缩放比例
    if (imageRatio > containerRatio) {
      // 图像更宽，以宽度为准
      scale = (stageSize.width * 0.9) / image.width;
    } else {
      // 图像更高，以高度为准
      scale = (stageSize.height * 0.9) / image.height;
    }
    
    width = image.width * scale;
    height = image.height * scale;
    
    // 居中显示
    const x = (stageSize.width - width) / 2;
    const y = (stageSize.height - height) / 2;
    
    return { x, y, width, height, scale };
  };

  const imageDimensions = getImageDimensions();

  // 处理画布点击事件
  const handleStageClick = (e: any) => {
    // 如果点击的是空白区域，取消选择
    if (e.target === e.target.getStage()) {
      setSelectedShapeId(null);
    }
  };

  // 处理鼠标滚轮缩放
  const handleWheel = (e: any) => {
    e.evt.preventDefault();
    
    const scaleBy = 1.1;
    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    
    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };
    
    const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy;
    
    setZoom(newScale);
    
    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };
    
    stage.position(newPos);
    stage.batchDraw();
  };

  // 响应容器大小变化
  useEffect(() => {
    const updateSize = () => {
      const container = document.getElementById('editor-container');
      if (container) {
        const rect = container.getBoundingClientRect();
        setStageSize({
          width: rect.width,
          height: rect.height
        });
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return (
    <Box
      id="editor-container"
      sx={{
        width: '100%',
        height: '100%',
        minHeight: '500px',
        border: '1px solid #ddd',
        borderRadius: 1,
        overflow: 'hidden',
        position: 'relative',
        backgroundColor: '#f5f5f5'
      }}
    >
      <Stage
        ref={stageRef}
        width={stageSize.width}
        height={stageSize.height}
        scaleX={zoom}
        scaleY={zoom}
        onClick={handleStageClick}
        onWheel={handleWheel}
        draggable={currentTool === 'select'}
      >
        {/* 背景图像层 */}
        <Layer>
          {image && (
            <KonvaImage
              image={image}
              x={imageDimensions.x}
              y={imageDimensions.y}
              width={imageDimensions.width}
              height={imageDimensions.height}
              listening={false} // 背景图像不响应事件
            />
          )}
        </Layer>
        
        {/* 绘图元素层 */}
        <Layer>
          <DrawingTools
            stageRef={stageRef}
            currentColor={currentColor}
            currentStrokeWidth={currentStrokeWidth}
            currentFontSize={currentFontSize}
            currentFontFamily={currentFontFamily}
          />
        </Layer>
        
        {/* 选择和裁剪层 */}
        <Layer>
          <CropTool
            imageX={imageDimensions.x}
            imageY={imageDimensions.y}
            imageWidth={imageDimensions.width}
            imageHeight={imageDimensions.height}
          />

          {/* 多选管理器 */}
          <MultiSelectManager stageRef={stageRef} />

          {/* 形状变换器 */}
          <ShapeTransformer
            selectedShapeId={selectedShapeId}
            onTransformEnd={(shapeId, newAttrs) => {
              console.log('[EDITOR-CANVAS] Shape transformed:', shapeId, newAttrs);
            }}
          />
        </Layer>
      </Stage>

      {/* 文字工具 */}
      <TextTool stageRef={stageRef} />

      {/* 缩放显示 */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '4px 8px',
          borderRadius: 1,
          fontSize: '12px'
        }}
      >
        {Math.round(zoom * 100)}%
      </Box>
    </Box>
  );
};
