import React, { useRef, useEffect } from 'react';
import { Rect, Transformer } from 'react-konva';
import { useEditorStore } from '../../store/editorStore';

interface CropToolProps {
  imageX: number;
  imageY: number;
  imageWidth: number;
  imageHeight: number;
}

export const CropTool: React.FC<CropToolProps> = ({
  imageX,
  imageY,
  imageWidth,
  imageHeight
}) => {
  const cropRectRef = useRef<any>(null);
  const transformerRef = useRef<any>(null);
  
  const {
    currentTool,
    cropArea,
    setCropArea
  } = useEditorStore();

  // 初始化裁剪区域
  useEffect(() => {
    if (currentTool === 'crop' && !cropArea) {
      // 默认裁剪区域为图像的80%，居中显示
      const defaultWidth = imageWidth * 0.8;
      const defaultHeight = imageHeight * 0.8;
      const defaultX = imageX + (imageWidth - defaultWidth) / 2;
      const defaultY = imageY + (imageHeight - defaultHeight) / 2;
      
      setCropArea({
        x: defaultX,
        y: defaultY,
        width: defaultWidth,
        height: defaultHeight
      });
    }
  }, [currentTool, cropArea, imageX, imageY, imageWidth, imageHeight, setCropArea]);

  // 设置transformer
  useEffect(() => {
    if (currentTool === 'crop' && cropRectRef.current && transformerRef.current) {
      transformerRef.current.nodes([cropRectRef.current]);
      transformerRef.current.getLayer()?.batchDraw();
    }
  }, [currentTool, cropArea]);

  // 处理裁剪区域变化
  const handleTransform = () => {
    if (!cropRectRef.current) return;
    
    const node = cropRectRef.current;
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();
    
    // 重置缩放并更新尺寸
    node.scaleX(1);
    node.scaleY(1);
    
    const newX = Math.max(imageX, node.x());
    const newY = Math.max(imageY, node.y());
    const newWidth = Math.min(
      node.width() * scaleX,
      imageX + imageWidth - newX
    );
    const newHeight = Math.min(
      node.height() * scaleY,
      imageY + imageHeight - newY
    );
    
    // 确保裁剪区域不超出图像边界
    const constrainedX = Math.max(imageX, Math.min(newX, imageX + imageWidth - newWidth));
    const constrainedY = Math.max(imageY, Math.min(newY, imageY + imageHeight - newHeight));
    
    setCropArea({
      x: constrainedX,
      y: constrainedY,
      width: Math.max(10, newWidth), // 最小宽度10px
      height: Math.max(10, newHeight) // 最小高度10px
    });
  };

  const handleDragEnd = () => {
    if (!cropRectRef.current) return;
    
    const node = cropRectRef.current;
    const newX = Math.max(imageX, Math.min(node.x(), imageX + imageWidth - node.width()));
    const newY = Math.max(imageY, Math.min(node.y(), imageY + imageHeight - node.height()));
    
    // 更新位置
    node.x(newX);
    node.y(newY);
    
    setCropArea({
      x: newX,
      y: newY,
      width: node.width(),
      height: node.height()
    });
  };

  // 只在裁剪模式下显示
  if (currentTool !== 'crop' || !cropArea) {
    return null;
  }

  return (
    <>
      {/* 裁剪区域矩形 */}
      <Rect
        ref={cropRectRef}
        x={cropArea.x}
        y={cropArea.y}
        width={cropArea.width}
        height={cropArea.height}
        stroke="#2196f3"
        strokeWidth={2}
        fill="rgba(33, 150, 243, 0.1)"
        draggable
        onDragEnd={handleDragEnd}
        onTransformEnd={handleTransform}
        dragBoundFunc={(pos) => {
          // 限制拖拽边界
          const newX = Math.max(imageX, Math.min(pos.x, imageX + imageWidth - cropArea.width));
          const newY = Math.max(imageY, Math.min(pos.y, imageY + imageHeight - cropArea.height));
          return { x: newX, y: newY };
        }}
      />
      
      {/* 变换控制器 */}
      <Transformer
        ref={transformerRef}
        boundBoxFunc={(oldBox, newBox) => {
          // 限制变换边界
          const minWidth = 10;
          const minHeight = 10;
          
          if (newBox.width < minWidth || newBox.height < minHeight) {
            return oldBox;
          }
          
          // 确保不超出图像边界
          if (newBox.x < imageX || 
              newBox.y < imageY || 
              newBox.x + newBox.width > imageX + imageWidth ||
              newBox.y + newBox.height > imageY + imageHeight) {
            return oldBox;
          }
          
          return newBox;
        }}
        enabledAnchors={[
          'top-left',
          'top-right',
          'bottom-left',
          'bottom-right',
          'top-center',
          'bottom-center',
          'middle-left',
          'middle-right'
        ]}
        rotateEnabled={false}
        borderStroke="#2196f3"
        borderStrokeWidth={2}
        anchorStroke="#2196f3"
        anchorFill="#ffffff"
        anchorSize={8}
        anchorCornerRadius={2}
      />
    </>
  );
};
