import React, { useEffect, useRef } from 'react';
import { Transformer } from 'react-konva';
import { useEditorStore } from '../../store/editorStore';

interface ShapeTransformerProps {
  selectedShapeId: string | null;
  onTransformEnd?: (shapeId: string, newAttrs: any) => void;
}

export const ShapeTransformer: React.FC<ShapeTransformerProps> = ({
  selectedShapeId,
  onTransformEnd
}) => {
  const transformerRef = useRef<any>(null);
  const { shapes, updateShape, saveToHistory } = useEditorStore();

  useEffect(() => {
    const transformer = transformerRef.current;
    if (!transformer) return;

    if (selectedShapeId) {
      // 查找对应的Konva节点
      const stage = transformer.getStage();
      if (stage) {
        const selectedNode = stage.findOne(`#${selectedShapeId}`);
        if (selectedNode) {
          transformer.nodes([selectedNode]);
          transformer.getLayer()?.batchDraw();
          
          console.log('[SHAPE-TRANSFORMER] 🎯 Shape selected for transformation:', selectedShapeId);
        } else {
          console.warn('[SHAPE-TRANSFORMER] ⚠️ Could not find node with id:', selectedShapeId);
        }
      }
    } else {
      // 清除选择
      transformer.nodes([]);
      transformer.getLayer()?.batchDraw();
    }
  }, [selectedShapeId]);

  const handleTransformEnd = (e: any) => {
    const node = e.target;
    const shapeId = node.id();
    
    if (!shapeId) return;

    // 获取变换后的属性
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();
    const rotation = node.rotation();
    
    // 重置缩放，将缩放应用到实际尺寸
    node.scaleX(1);
    node.scaleY(1);
    
    const newAttrs: any = {
      x: node.x(),
      y: node.y(),
      rotation: rotation
    };

    // 根据形状类型处理尺寸变化
    const shape = shapes.find(s => s.id === shapeId);
    if (shape) {
      switch (shape.type) {
        case 'rectangle':
        case 'ellipse':
        case 'mosaic':
          newAttrs.width = Math.max(5, (shape.width || 0) * scaleX);
          newAttrs.height = Math.max(5, (shape.height || 0) * scaleY);
          break;
          
        case 'line':
        case 'dashed-line':
        case 'arrow':
        case 'path':
          // 对于线条和路径，需要变换所有点
          if (shape.points) {
            const centerX = shape.x + (shape.width || 0) / 2;
            const centerY = shape.y + (shape.height || 0) / 2;
            
            newAttrs.points = shape.points.map((point, index) => {
              if (index % 2 === 0) {
                // X坐标
                return (point - centerX) * scaleX + centerX;
              } else {
                // Y坐标
                return (point - centerY) * scaleY + centerY;
              }
            });
          }
          break;
          
        case 'text':
        case 'number':
          // 文字类型调整字体大小
          const currentFontSize = shape.style.fontSize || 16;
          newAttrs.style = {
            ...shape.style,
            fontSize: Math.max(8, Math.round(currentFontSize * Math.min(scaleX, scaleY)))
          };
          break;
      }
    }

    // 更新形状属性
    updateShape(shapeId, newAttrs);
    
    // 保存到历史记录
    saveToHistory();
    
    // 触发回调
    onTransformEnd?.(shapeId, newAttrs);
    
    console.log('[SHAPE-TRANSFORMER] ✅ Shape transformed:', {
      shapeId,
      newAttrs,
      scaleX,
      scaleY,
      rotation
    });
  };

  const handleDragEnd = (e: any) => {
    const node = e.target;
    const shapeId = node.id();
    
    if (!shapeId) return;

    const newAttrs = {
      x: node.x(),
      y: node.y()
    };

    // 更新形状位置
    updateShape(shapeId, newAttrs);
    
    // 保存到历史记录
    saveToHistory();
    
    console.log('[SHAPE-TRANSFORMER] 📍 Shape moved:', { shapeId, newAttrs });
  };

  // 只在选择模式下显示变换器
  const { currentTool } = useEditorStore();
  if (currentTool !== 'select' || !selectedShapeId) {
    return null;
  }

  return (
    <Transformer
      ref={transformerRef}
      onTransformEnd={handleTransformEnd}
      onDragEnd={handleDragEnd}
      boundBoxFunc={(oldBox, newBox) => {
        // 限制最小尺寸
        if (newBox.width < 5 || newBox.height < 5) {
          return oldBox;
        }
        return newBox;
      }}
      // 变换器配置
      enabledAnchors={[
        'top-left',
        'top-center',
        'top-right',
        'middle-right',
        'bottom-right',
        'bottom-center',
        'bottom-left',
        'middle-left'
      ]}
      rotateEnabled={true}
      borderEnabled={true}
      borderStroke="#2196f3"
      borderStrokeWidth={1}
      borderDash={[4, 4]}
      anchorFill="#2196f3"
      anchorStroke="#1976d2"
      anchorStrokeWidth={1}
      anchorSize={8}
      rotateAnchorOffset={20}
      rotateAnchorCursor="grab"
    />
  );
};
