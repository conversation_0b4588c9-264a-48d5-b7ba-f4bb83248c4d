import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Popover,
  Grid,
  Slider,
  Typography,
  TextField
} from '@mui/material';
import { Palette as PaletteIcon } from '@mui/icons-material';

interface ColorPickerProps {
  currentColor: string;
  currentStrokeWidth: number;
  onColorChange: (color: string) => void;
  onStrokeWidthChange: (width: number) => void;
}

const PRESET_COLORS = [
  '#FF0000', '#FF8000', '#FFFF00', '#80FF00',
  '#00FF00', '#00FF80', '#00FFFF', '#0080FF',
  '#0000FF', '#8000FF', '#FF00FF', '#FF0080',
  '#000000', '#404040', '#808080', '#C0C0C0',
  '#FFFFFF', '#800000', '#808000', '#008000',
  '#008080', '#000080', '#800080', '#804000'
];

export const ColorPicker: React.FC<ColorPickerProps> = ({
  currentColor,
  currentStrokeWidth,
  onColorChange,
  onStrokeWidthChange
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [customColor, setCustomColor] = useState(currentColor);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleColorSelect = (color: string) => {
    onColorChange(color);
    setCustomColor(color);
  };

  const handleCustomColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const color = event.target.value;
    setCustomColor(color);
    onColorChange(color);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <IconButton
        onClick={handleClick}
        size="small"
        sx={{
          backgroundColor: currentColor,
          border: '2px solid #fff',
          boxShadow: '0 0 0 1px #ccc',
          '&:hover': {
            backgroundColor: currentColor,
            opacity: 0.8
          }
        }}
      >
        <PaletteIcon sx={{ color: currentColor === '#FFFFFF' ? '#000' : '#fff' }} />
      </IconButton>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Box sx={{ p: 2, width: 280 }}>
          {/* 预设颜色 */}
          <Typography variant="subtitle2" gutterBottom>
            预设颜色
          </Typography>
          <Grid container spacing={0.5} sx={{ mb: 2 }}>
            {PRESET_COLORS.map((color) => (
              <Grid size={1} key={color}>
                <Box
                  onClick={() => handleColorSelect(color)}
                  sx={{
                    width: 24,
                    height: 24,
                    backgroundColor: color,
                    border: currentColor === color ? '2px solid #2196f3' : '1px solid #ccc',
                    borderRadius: 1,
                    cursor: 'pointer',
                    '&:hover': {
                      transform: 'scale(1.1)',
                      transition: 'transform 0.1s'
                    }
                  }}
                />
              </Grid>
            ))}
          </Grid>

          {/* 自定义颜色 */}
          <Typography variant="subtitle2" gutterBottom>
            自定义颜色
          </Typography>
          <TextField
            type="color"
            value={customColor}
            onChange={handleCustomColorChange}
            size="small"
            fullWidth
            sx={{ mb: 2 }}
          />

          {/* 线条粗细 */}
          <Typography variant="subtitle2" gutterBottom>
            线条粗细: {currentStrokeWidth}px
          </Typography>
          <Slider
            value={currentStrokeWidth}
            onChange={(_, value) => onStrokeWidthChange(value as number)}
            min={1}
            max={10}
            step={1}
            marks
            valueLabelDisplay="auto"
            sx={{ mb: 1 }}
          />

          {/* 预览 */}
          <Box
            sx={{
              height: 40,
              border: '1px solid #ccc',
              borderRadius: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f5f5f5'
            }}
          >
            <Box
              sx={{
                width: 60,
                height: currentStrokeWidth,
                backgroundColor: currentColor,
                borderRadius: currentStrokeWidth / 2
              }}
            />
          </Box>
        </Box>
      </Popover>
    </>
  );
};
