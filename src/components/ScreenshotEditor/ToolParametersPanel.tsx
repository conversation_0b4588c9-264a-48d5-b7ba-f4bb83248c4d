import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Divider,
  IconButton,
  Collapse
} from '@mui/material';
import {
  Palette as PaletteIcon,
  LineWeight as LineWeightIcon,
  TextFields as TextFieldsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { useEditorStore } from '../../store/editorStore';


interface ToolParametersPanelProps {
  visible: boolean;
  onClose: () => void;
  currentColor: string;
  onColorChange: (color: string) => void;
  currentStrokeWidth: number;
  onStrokeWidthChange: (width: number) => void;
  currentFontSize?: number;
  onFontSizeChange?: (size: number) => void;
  currentFontFamily?: string;
  onFontFamilyChange?: (family: string) => void;
}

// 预定义颜色
const PRESET_COLORS = [
  '#FF0000', // 红色
  '#00FF00', // 绿色
  '#0000FF', // 蓝色
  '#FFFF00', // 黄色
  '#FF00FF', // 紫色
  '#00FFFF', // 青色
  '#FFA500', // 橙色
  '#800080', // 紫色
  '#000000', // 黑色
  '#FFFFFF', // 白色
  '#808080', // 灰色
  '#FFC0CB'  // 粉色
];

// 字体选项
const FONT_FAMILIES = [
  'Arial',
  'Helvetica',
  'Times New Roman',
  'Courier New',
  'Verdana',
  'Georgia',
  'Comic Sans MS',
  'Impact'
];

export const ToolParametersPanel: React.FC<ToolParametersPanelProps> = ({
  visible,
  onClose,
  currentColor,
  onColorChange,
  currentStrokeWidth,
  onStrokeWidthChange,
  currentFontSize = 16,
  onFontSizeChange,
  currentFontFamily = 'Arial',
  onFontFamilyChange
}) => {
  const { currentTool } = useEditorStore();
  const [expandedSections, setExpandedSections] = useState({
    color: true,
    stroke: true,
    text: true
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 根据当前工具显示相关参数
  const showColorPicker = ['rectangle', 'ellipse', 'line', 'dashed-line', 'arrow', 'pen', 'text', 'number'].includes(currentTool);
  const showStrokeWidth = ['rectangle', 'ellipse', 'line', 'dashed-line', 'arrow', 'pen'].includes(currentTool);
  const showTextOptions = ['text', 'number'].includes(currentTool);

  if (!visible) return null;

  return (
    <Paper
      elevation={8}
      sx={{
        position: 'absolute',
        top: 20,
        right: 20,
        width: 280,
        maxHeight: '80vh',
        overflow: 'auto',
        p: 2,
        zIndex: 1000,
        backgroundColor: 'background.paper'
      }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6" component="div">
          工具参数
        </Typography>
        <IconButton size="small" onClick={onClose}>
          <ExpandLessIcon />
        </IconButton>
      </Box>

      {/* 颜色选择器 */}
      {showColorPicker && (
        <Box mb={2}>
          <Box display="flex" alignItems="center" mb={1}>
            <PaletteIcon sx={{ mr: 1, fontSize: 20 }} />
            <Typography variant="subtitle2">颜色</Typography>
            <IconButton
              size="small"
              onClick={() => toggleSection('color')}
              sx={{ ml: 'auto' }}
            >
              {expandedSections.color ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
          
          <Collapse in={expandedSections.color}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {PRESET_COLORS.map((color) => (
                <Box
                  key={color}
                  sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: color,
                    border: currentColor === color ? '3px solid #2196f3' : '1px solid #ccc',
                    borderRadius: 1,
                    cursor: 'pointer',
                    '&:hover': {
                      transform: 'scale(1.1)'
                    }
                  }}
                  onClick={() => onColorChange(color)}
                />
              ))}
            </Box>
            
            {/* 自定义颜色输入 */}
            <Box display="flex" alignItems="center" gap={1}>
              <Typography variant="body2">自定义:</Typography>
              <input
                type="color"
                value={currentColor}
                onChange={(e) => onColorChange(e.target.value)}
                style={{
                  width: 40,
                  height: 30,
                  border: 'none',
                  borderRadius: 4,
                  cursor: 'pointer'
                }}
              />
            </Box>
          </Collapse>
        </Box>
      )}

      {/* 线条粗细 */}
      {showStrokeWidth && (
        <Box mb={2}>
          <Box display="flex" alignItems="center" mb={1}>
            <LineWeightIcon sx={{ mr: 1, fontSize: 20 }} />
            <Typography variant="subtitle2">线条粗细</Typography>
            <IconButton
              size="small"
              onClick={() => toggleSection('stroke')}
              sx={{ ml: 'auto' }}
            >
              {expandedSections.stroke ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
          
          <Collapse in={expandedSections.stroke}>
            <Box px={1}>
              <Slider
                value={currentStrokeWidth}
                onChange={(_, value) => onStrokeWidthChange(value as number)}
                min={1}
                max={20}
                step={1}
                marks={[
                  { value: 1, label: '1' },
                  { value: 5, label: '5' },
                  { value: 10, label: '10' },
                  { value: 20, label: '20' }
                ]}
                valueLabelDisplay="auto"
              />
            </Box>
          </Collapse>
        </Box>
      )}

      {/* 文字选项 */}
      {showTextOptions && (
        <Box mb={2}>
          <Box display="flex" alignItems="center" mb={1}>
            <TextFieldsIcon sx={{ mr: 1, fontSize: 20 }} />
            <Typography variant="subtitle2">文字设置</Typography>
            <IconButton
              size="small"
              onClick={() => toggleSection('text')}
              sx={{ ml: 'auto' }}
            >
              {expandedSections.text ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
          
          <Collapse in={expandedSections.text}>
            <Box>
              {/* 字体大小 */}
              {onFontSizeChange && (
                <Box mb={2}>
                  <Typography variant="body2" gutterBottom>
                    字体大小: {currentFontSize}px
                  </Typography>
                  <Slider
                    value={currentFontSize}
                    onChange={(_, value) => onFontSizeChange(value as number)}
                    min={8}
                    max={72}
                    step={2}
                    marks={[
                      { value: 12, label: '12' },
                      { value: 24, label: '24' },
                      { value: 48, label: '48' }
                    ]}
                    valueLabelDisplay="auto"
                  />
                </Box>
              )}
              
              {/* 字体族 */}
              {onFontFamilyChange && (
                <FormControl fullWidth size="small">
                  <InputLabel>字体</InputLabel>
                  <Select
                    value={currentFontFamily}
                    label="字体"
                    onChange={(e) => onFontFamilyChange(e.target.value)}
                  >
                    {FONT_FAMILIES.map((font) => (
                      <MenuItem key={font} value={font} style={{ fontFamily: font }}>
                        {font}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            </Box>
          </Collapse>
        </Box>
      )}

      <Divider sx={{ my: 2 }} />

      {/* 重置按钮 */}
      <Button
        variant="outlined"
        fullWidth
        onClick={() => {
          onColorChange('#FF0000');
          onStrokeWidthChange(2);
          onFontSizeChange?.(16);
          onFontFamilyChange?.('Arial');
        }}
      >
        重置为默认值
      </Button>
    </Paper>
  );
};
