import React, { useState, useRef, useEffect } from 'react';
import { useEditorStore } from '../../store/editorStore';
import { Shape } from '../../types/editor';

interface TextToolProps {
  stageRef: React.RefObject<any>;
}

export const TextTool: React.FC<TextToolProps> = ({ stageRef }) => {
  const {
    currentTool,
    addShape
  } = useEditorStore();

  const [isEditing, setIsEditing] = useState(false);
  const [editingText, setEditingText] = useState('');
  const [textPosition, setTextPosition] = useState({ x: 0, y: 0 });
  const textInputRef = useRef<HTMLTextAreaElement>(null);

  // 处理点击添加文字
  const handleStageClick = (e: any) => {
    if (currentTool !== 'text') return;

    const stage = e.target.getStage();
    const pos = stage.getPointerPosition();
    
    // 如果点击的是空白区域，开始编辑文字
    if (e.target === stage) {
      setTextPosition(pos);
      setEditingText('');
      setIsEditing(true);
    }
  };

  // 完成文字编辑
  const handleTextSubmit = () => {
    if (editingText.trim()) {
      const newTextShape: Shape = {
        id: `text_${Date.now()}`,
        type: 'text',
        x: textPosition.x,
        y: textPosition.y,
        text: editingText.trim(),
        style: {
          stroke: '#000000',
          strokeWidth: 1,
          fontSize: 16,
          fontFamily: 'Arial'
        }
      };
      
      addShape(newTextShape);
    }
    
    setIsEditing(false);
    setEditingText('');
  };

  // 取消文字编辑
  const handleTextCancel = () => {
    setIsEditing(false);
    setEditingText('');
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleTextSubmit();
    } else if (e.key === 'Escape') {
      handleTextCancel();
    }
  };

  // 绑定stage点击事件
  useEffect(() => {
    if (stageRef.current && currentTool === 'text') {
      const stage = stageRef.current;
      stage.on('click', handleStageClick);
      
      return () => {
        stage.off('click', handleStageClick);
      };
    }
  }, [currentTool, stageRef]);

  // 自动聚焦文本输入框
  useEffect(() => {
    if (isEditing && textInputRef.current) {
      textInputRef.current.focus();
    }
  }, [isEditing]);

  return (
    <>
      {/* 文字编辑输入框 */}
      {isEditing && (
        <div
          style={{
            position: 'absolute',
            left: textPosition.x,
            top: textPosition.y,
            zIndex: 1000,
            backgroundColor: 'white',
            border: '2px solid #2196f3',
            borderRadius: '4px',
            padding: '4px',
            minWidth: '100px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
          }}
        >
          <textarea
            ref={textInputRef}
            value={editingText}
            onChange={(e) => setEditingText(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleTextSubmit}
            placeholder="输入文字..."
            style={{
              border: 'none',
              outline: 'none',
              resize: 'none',
              fontSize: '16px',
              fontFamily: 'Arial',
              backgroundColor: 'transparent',
              minWidth: '100px',
              minHeight: '20px'
            }}
            rows={1}
          />
          <div style={{ 
            fontSize: '12px', 
            color: '#666', 
            marginTop: '4px',
            textAlign: 'center'
          }}>
            Enter确认 | Esc取消
          </div>
        </div>
      )}
    </>
  );
};
