import React, { useState } from 'react';
import { Rect, Line, Text, Group, Ellipse, Circle } from 'react-konva';
import { useEditorStore } from '../../store/editorStore';
import { Shape } from '../../types/editor';

interface DrawingToolsProps {
  stageRef: React.RefObject<any>;
  currentColor?: string;
  currentStrokeWidth?: number;
  currentFontSize?: number;
  currentFontFamily?: string;
}

export const DrawingTools: React.FC<DrawingToolsProps> = ({
  stageRef,
  currentColor = '#ff0000',
  currentStrokeWidth = 2,
  currentFontSize = 16,
  currentFontFamily = 'Arial'
}) => {
  const {
    currentTool,
    shapes,
    addShape,
    selectedShapeId,
    setSelectedShapeId
  } = useEditorStore();

  const [isDrawing, setIsDrawing] = useState(false);
  const [currentShape, setCurrentShape] = useState<Shape | null>(null);

  // 橡皮擦点击处理
  const handleEraserClick = (e: any) => {
    const clickedShape = e.target;

    // 检查是否点击了形状
    if (clickedShape && clickedShape.attrs && clickedShape.attrs.id) {
      const shapeId = clickedShape.attrs.id;
      const shapeToDelete = shapes.find(s => s.id === shapeId);

      if (shapeToDelete) {
        // 删除形状并保存到历史记录
        const { deleteShape } = useEditorStore.getState();
        deleteShape(shapeId);
        console.log('[DRAWING-TOOLS] 🗑️ Shape erased:', shapeId);
      }
    }
  };

  // 开始绘制
  const handleMouseDown = (e: any) => {
    const drawingTools = ['rectangle', 'ellipse', 'line', 'dashed-line', 'arrow', 'pen', 'mosaic', 'number'];

    // 橡皮擦工具处理
    if (currentTool === 'eraser') {
      handleEraserClick(e);
      return;
    }

    if (!drawingTools.includes(currentTool)) return;

    const stage = e.target.getStage();
    const pos = stage.getPointerPosition();
    setIsDrawing(true);

    const newShape: Shape = {
      id: `shape_${Date.now()}`,
      type: currentTool as Shape['type'],
      x: pos.x,
      y: pos.y,
      style: {
        stroke: currentColor,
        strokeWidth: currentStrokeWidth,
        fill: ['rectangle', 'ellipse'].includes(currentTool) ? 'transparent' : undefined,
        dash: currentTool === 'dashed-line' ? [10, 5] : undefined,
        opacity: currentTool === 'mosaic' ? 0.8 : 1
      }
    };

    if (['rectangle', 'ellipse', 'mosaic'].includes(currentTool)) {
      newShape.width = 0;
      newShape.height = 0;
    } else if (currentTool === 'pen') {
      newShape.type = 'path';
      newShape.points = [pos.x, pos.y];
    } else if (['line', 'dashed-line', 'arrow'].includes(currentTool)) {
      newShape.points = [pos.x, pos.y, pos.x, pos.y];
    } else if (currentTool === 'number') {
      // 为序号工具生成下一个数字
      const existingNumbers = shapes.filter(s => s.type === 'number').length;
      newShape.number = existingNumbers + 1;
      newShape.text = String(newShape.number);
      newShape.width = 30;
      newShape.height = 30;
      newShape.style.fill = currentColor;
      newShape.style.stroke = '#ffffff';
      newShape.style.fontSize = currentFontSize;
      newShape.style.fontFamily = currentFontFamily;
    }

    setCurrentShape(newShape);
  };

  // 绘制过程中
  const handleMouseMove = (e: any) => {
    if (!isDrawing || !currentShape) return;

    const stage = e.target.getStage();
    const pos = stage.getPointerPosition();

    if (['rectangle', 'ellipse', 'mosaic'].includes(currentTool)) {
      const width = pos.x - currentShape.x;
      const height = pos.y - currentShape.y;
      setCurrentShape({
        ...currentShape,
        width: Math.abs(width),
        height: Math.abs(height),
        x: width < 0 ? pos.x : currentShape.x,
        y: height < 0 ? pos.y : currentShape.y
      });
    } else if (currentTool === 'pen') {
      const newPoints = currentShape.points!.concat([pos.x, pos.y]);
      setCurrentShape({
        ...currentShape,
        points: newPoints
      });
    } else if (['line', 'dashed-line', 'arrow'].includes(currentTool)) {
      setCurrentShape({
        ...currentShape,
        points: [currentShape.x, currentShape.y, pos.x, pos.y]
      });
    }
  };

  // 结束绘制
  const handleMouseUp = () => {
    if (!isDrawing || !currentShape) return;

    setIsDrawing(false);

    // 只有当形状有实际大小时才添加
    if (['rectangle', 'ellipse', 'mosaic'].includes(currentTool) &&
        currentShape.width! > 5 && currentShape.height! > 5) {
      addShape(currentShape);
    } else if (currentTool === 'pen' && currentShape.points!.length > 4) {
      addShape(currentShape);
    } else if (['line', 'dashed-line', 'arrow'].includes(currentTool) &&
               currentShape.points!.length === 4) {
      const [x1, y1, x2, y2] = currentShape.points!;
      const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
      if (distance > 10) {
        addShape(currentShape);
      }
    } else if (currentTool === 'number') {
      // 序号工具总是添加
      addShape(currentShape);
    }

    setCurrentShape(null);
  };

  // 渲染箭头
  const renderArrow = (shape: Shape) => {
    if (!shape.points || shape.points.length !== 4) return null;
    
    const [x1, y1, x2, y2] = shape.points;
    const headLength = 15;
    const angle = Math.atan2(y2 - y1, x2 - x1);
    
    // 箭头线
    const linePoints = [x1, y1, x2, y2];
    
    // 箭头头部
    const arrowHead1X = x2 - headLength * Math.cos(angle - Math.PI / 6);
    const arrowHead1Y = y2 - headLength * Math.sin(angle - Math.PI / 6);
    const arrowHead2X = x2 - headLength * Math.cos(angle + Math.PI / 6);
    const arrowHead2Y = y2 - headLength * Math.sin(angle + Math.PI / 6);
    
    return (
      <Group key={shape.id} id={shape.id}>
        <Line
          points={linePoints}
          stroke={shape.style.stroke}
          strokeWidth={shape.style.strokeWidth}
          lineCap="round"
          onClick={() => setSelectedShapeId(shape.id)}
        />
        <Line
          points={[x2, y2, arrowHead1X, arrowHead1Y]}
          stroke={shape.style.stroke}
          strokeWidth={shape.style.strokeWidth}
          lineCap="round"
          onClick={() => setSelectedShapeId(shape.id)}
        />
        <Line
          points={[x2, y2, arrowHead2X, arrowHead2Y]}
          stroke={shape.style.stroke}
          strokeWidth={shape.style.strokeWidth}
          lineCap="round"
          onClick={() => setSelectedShapeId(shape.id)}
        />
      </Group>
    );
  };

  // 渲染形状
  const renderShape = (shape: Shape) => {
    const isSelected = selectedShapeId === shape.id;
    const strokeWidth = isSelected ? shape.style.strokeWidth + 1 : shape.style.strokeWidth;

    switch (shape.type) {
      case 'rectangle':
        return (
          <Rect
            key={shape.id}
            id={shape.id}
            x={shape.x}
            y={shape.y}
            width={shape.width || 0}
            height={shape.height || 0}
            stroke={shape.style.stroke}
            strokeWidth={strokeWidth}
            fill={shape.style.fill}
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );

      case 'ellipse':
        return (
          <Ellipse
            key={shape.id}
            id={shape.id}
            x={shape.x + (shape.width || 0) / 2}
            y={shape.y + (shape.height || 0) / 2}
            radiusX={(shape.width || 0) / 2}
            radiusY={(shape.height || 0) / 2}
            stroke={shape.style.stroke}
            strokeWidth={strokeWidth}
            fill={shape.style.fill}
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );

      case 'line':
        return (
          <Line
            key={shape.id}
            id={shape.id}
            points={shape.points || []}
            stroke={shape.style.stroke}
            strokeWidth={strokeWidth}
            lineCap="round"
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );

      case 'dashed-line':
        return (
          <Line
            key={shape.id}
            id={shape.id}
            points={shape.points || []}
            stroke={shape.style.stroke}
            strokeWidth={strokeWidth}
            lineCap="round"
            dash={shape.style.dash || [10, 5]}
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );

      case 'path':
        return (
          <Line
            key={shape.id}
            id={shape.id}
            points={shape.points || []}
            stroke={shape.style.stroke}
            strokeWidth={strokeWidth}
            lineCap="round"
            lineJoin="round"
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );

      case 'mosaic':
        return (
          <Group key={shape.id} id={shape.id}>
            <Rect
              x={shape.x}
              y={shape.y}
              width={shape.width || 0}
              height={shape.height || 0}
              fill={shape.style.stroke}
              opacity={shape.style.opacity || 0.8}
              onClick={() => setSelectedShapeId(shape.id)}
            />
            {/* 可以在这里添加马赛克效果的实现 */}
          </Group>
        );

      case 'number':
        return (
          <Group key={shape.id} id={shape.id}>
            <Circle
              x={shape.x + 15}
              y={shape.y + 15}
              radius={15}
              fill={shape.style.fill}
              stroke={shape.style.stroke}
              strokeWidth={2}
              onClick={() => setSelectedShapeId(shape.id)}
            />
            <Text
              x={shape.x + 15}
              y={shape.y + 15}
              text={shape.text || ''}
              fontSize={shape.style.fontSize || 16}
              fontFamily={shape.style.fontFamily || 'Arial'}
              fill="white"
              align="center"
              verticalAlign="middle"
              offsetX={shape.text ? shape.text.length * 4 : 0}
              offsetY={8}
              onClick={() => setSelectedShapeId(shape.id)}
            />
          </Group>
        );

      case 'arrow':
        return renderArrow(shape);

      case 'text':
        return (
          <Text
            key={shape.id}
            id={shape.id}
            x={shape.x}
            y={shape.y}
            text={shape.text || ''}
            fontSize={shape.style.fontSize || 16}
            fontFamily={shape.style.fontFamily || 'Arial'}
            fill={shape.style.stroke}
            onClick={() => setSelectedShapeId(shape.id)}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* 渲染所有已保存的形状 */}
      {shapes.map(renderShape)}
      
      {/* 渲染正在绘制的形状 */}
      {currentShape && renderShape(currentShape)}
      
      {/* 绑定鼠标事件到stage */}
      {stageRef.current && (
        <>
          {stageRef.current.on('mousedown touchstart', handleMouseDown)}
          {stageRef.current.on('mousemove touchmove', handleMouseMove)}
          {stageRef.current.on('mouseup touchend', handleMouseUp)}
        </>
      )}
    </>
  );
};
