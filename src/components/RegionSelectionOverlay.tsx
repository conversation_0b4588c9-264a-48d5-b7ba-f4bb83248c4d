import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Box, IconButton } from '@mui/material';
import { Close as CloseIcon, Check as CheckIcon } from '@mui/icons-material';
import { useEditorStore } from '../store/editorStore';

interface ScreenshotArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface RegionSelectionOverlayProps {
  backgroundImage: string;
  onRegionSelected: (area: ScreenshotArea) => void;
  onFullScreenCapture: () => void;
  onCancel: () => void;
}

export const RegionSelectionOverlay: React.FC<RegionSelectionOverlayProps> = ({
  backgroundImage,
  onRegionSelected,
  onFullScreenCapture,
  onCancel
}) => {
  const setSelectedWindow = useEditorStore(state => state.setSelectedWindow);
  const [isDragging, setIsDragging] = useState(false);
  const [resizeDirection, setResizeDirection] = useState<string | null>(null);
  const [dragStart, setDragStart] = useState({x: 0, y: 0});
  const [initialRect, setInitialRect] = useState({x:0, y:0, width:0, height:0});
  const [windowRect, setWindowRect] = useState<{x: number, y: number, width: number, height: number} | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  // 处理鼠标按下事件（窗口拖拽开始）
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button !== 0) return;

    const rect = overlayRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // 如果还没有窗口，创建新窗口
    if (!windowRect) {
      const newRect = {x, y, width: 100, height: 100};
      setWindowRect(newRect);
      setSelectedWindow(newRect);
      return;
    }
    
    // 检查是否按在手柄上
    const handles = [
      { position: 'n', x: windowRect.x + windowRect.width/2, y: windowRect.y },
      { position: 'ne', x: windowRect.x + windowRect.width, y: windowRect.y },
      { position: 'e', x: windowRect.x + windowRect.width, y: windowRect.y + windowRect.height/2 },
      { position: 'se', x: windowRect.x + windowRect.width, y: windowRect.y + windowRect.height },
      { position: 's', x: windowRect.x + windowRect.width/2, y: windowRect.y + windowRect.height },
      { position: 'sw', x: windowRect.x, y: windowRect.y + windowRect.height },
      { position: 'w', x: windowRect.x, y: windowRect.y + windowRect.height/2 },
      { position: 'nw', x: windowRect.x, y: windowRect.y }
    ];
    
    const handleSize = 8;
    let handleHit = null;
    for (const handle of handles) {
      if (Math.abs(x - handle.x) <= handleSize && Math.abs(y - handle.y) <= handleSize) {
        handleHit = handle.position;
        break;
      }
    }
    
    // 检查是否按在窗口内部
    const isInsideWindow = 
      x >= windowRect.x && 
      x <= windowRect.x + windowRect.width && 
      y >= windowRect.y && 
      y <= windowRect.y + windowRect.height;

    if (handleHit) {
      // 开始调整大小
      setResizeDirection(handleHit);
      setInitialRect({...windowRect});
    } else if (isInsideWindow) {
      // 开始拖拽整个窗口
      setIsDragging(true);
    } else {
      // 点击窗口外部，创建新窗口
      const newRect = {x, y, width: 100, height: 100};
      setWindowRect(newRect);
      setSelectedWindow(newRect);
      return;
    }
    
    setDragStart({x, y});
  }, [windowRect, setSelectedWindow]);

  // 处理鼠标移动事件（窗口拖拽中）
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging && !resizeDirection) return;

    const rect = overlayRef.current?.getBoundingClientRect();
    if (!rect || !windowRect) return;
    
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;
    
    if (isDragging) {
      // 拖拽整个窗口
      const dx = currentX - dragStart.x;
      const dy = currentY - dragStart.y;
      const newRect = {
        ...windowRect,
        x: windowRect.x + dx,
        y: windowRect.y + dy
      };
      setWindowRect(newRect);
      setSelectedWindow(newRect);
      setDragStart({x: currentX, y: currentY});
    } else if (resizeDirection) {
      // 调整窗口大小
      const { x, y, width, height } = initialRect;
      const dx = currentX - dragStart.x;
      const dy = currentY - dragStart.y;
      
      let newX = x;
      let newY = y;
      let newWidth = width;
      let newHeight = height;
      
      switch (resizeDirection) {
        case 'n':
          newY = y + dy;
          newHeight = height - dy;
          break;
        case 'ne':
          newY = y + dy;
          newHeight = height - dy;
          newWidth = width + dx;
          break;
        case 'e':
          newWidth = width + dx;
          break;
        case 'se':
          newWidth = width + dx;
          newHeight = height + dy;
          break;
        case 's':
          newHeight = height + dy;
          break;
        case 'sw':
          newX = x + dx;
          newWidth = width - dx;
          newHeight = height + dy;
          break;
        case 'w':
          newX = x + dx;
          newWidth = width - dx;
          break;
        case 'nw':
          newX = x + dx;
          newY = y + dy;
          newWidth = width - dx;
          newHeight = height - dy;
          break;
      }
      
      // 确保最小尺寸
      if (newWidth < 50) newWidth = 50;
      if (newHeight < 50) newHeight = 50;
      
      const newRect = {x: newX, y: newY, width: newWidth, height: newHeight};
      setWindowRect(newRect);
      setSelectedWindow(newRect);
    }
  }, [isDragging, resizeDirection, windowRect, initialRect, dragStart, setSelectedWindow]);

  // 处理鼠标释放事件（窗口拖拽结束）
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setResizeDirection(null);
  }, []);

  // 确认选择
  const handleConfirm = useCallback(() => {
    if (windowRect) {
      onRegionSelected(windowRect);
    } else {
      onFullScreenCapture();
    }
  }, [windowRect, onRegionSelected, onFullScreenCapture]);

  // 取消选择
  const handleCancel = useCallback(() => {
    setWindowRect(null);
    setIsDragging(false);
    setResizeDirection(null);
    setSelectedWindow(null);
    onCancel();
  }, [onCancel, setSelectedWindow]);

  // 渲染调整手柄
  const renderHandles = () => {
    if (!windowRect) return null;
    
    const handleStyle = {
      position: 'absolute',
      width: '8px',
      height: '8px',
      backgroundColor: '#2196f3',
      border: '1px solid white',
      borderRadius: '50%',
      zIndex: 10000,
    };
    
    return (
      <>
        {/* 上中 */}
        <Box sx={{ ...handleStyle, left: windowRect.x + windowRect.width/2 - 4, top: windowRect.y - 4, cursor: 'n-resize' }} />
        {/* 上右 */}
        <Box sx={{ ...handleStyle, left: windowRect.x + windowRect.width - 4, top: windowRect.y - 4, cursor: 'ne-resize' }} />
        {/* 右中 */}
        <Box sx={{ ...handleStyle, left: windowRect.x + windowRect.width - 4, top: windowRect.y + windowRect.height/2 - 4, cursor: 'e-resize' }} />
        {/* 下右 */}
        <Box sx={{ ...handleStyle, left: windowRect.x + windowRect.width - 4, top: windowRect.y + windowRect.height - 4, cursor: 'se-resize' }} />
        {/* 下中 */}
        <Box sx={{ ...handleStyle, left: windowRect.x + windowRect.width/2 - 4, top: windowRect.y + windowRect.height - 4, cursor: 's-resize' }} />
        {/* 下左 */}
        <Box sx={{ ...handleStyle, left: windowRect.x - 4, top: windowRect.y + windowRect.height - 4, cursor: 'sw-resize' }} />
        {/* 左中 */}
        <Box sx={{ ...handleStyle, left: windowRect.x - 4, top: windowRect.y + windowRect.height/2 - 4, cursor: 'w-resize' }} />
        {/* 上左 */}
        <Box sx={{ ...handleStyle, left: windowRect.x - 4, top: windowRect.y - 4, cursor: 'nw-resize' }} />
      </>
    );
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleCancel();
      } else if (e.key === 'Enter') {
        handleConfirm();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleCancel, handleConfirm]);

  return (
    <Box
      ref={overlayRef}
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 9999,
        cursor: isDragging ? 'grabbing' : (resizeDirection ? `${resizeDirection}-resize` : 'default'),
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      {/* 暗化遮罩 */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          pointerEvents: 'none',
        }}
      />

      {/* 窗口选择区域 */}
      {windowRect && (
        <>
          {/* 窗口区域 */}
          <Box
            sx={{
              position: 'absolute',
              left: windowRect.x,
              top: windowRect.y,
              width: windowRect.width,
              height: windowRect.height,
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              border: '2px solid #2196f3',
              pointerEvents: 'none',
            }}
          />

          {/* 窗口信息显示 */}
          <Box
            sx={{
              position: 'absolute',
              left: windowRect.x,
              top: windowRect.y - 40,
              backgroundColor: '#2196f3',
              color: 'white',
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              fontFamily: 'monospace',
              pointerEvents: 'none',
              whiteSpace: 'nowrap',
            }}
          >
            {Math.round(windowRect.width)} × {Math.round(windowRect.height)}
          </Box>

          {/* 渲染调整手柄 */}
          {renderHandles()}

          {/* 确认/取消按钮 */}
          <Box
            sx={{
              position: 'absolute',
              left: windowRect.x + windowRect.width - 80,
              top: windowRect.y + windowRect.height + 10,
              display: 'flex',
              gap: 1,
            }}
          >
            <IconButton
              size="small"
              onClick={handleConfirm}
              sx={{
                backgroundColor: '#4caf50',
                color: 'white',
                '&:hover': { backgroundColor: '#45a049' },
              }}
            >
              <CheckIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                backgroundColor: '#f44336',
                color: 'white',
                '&:hover': { backgroundColor: '#da190b' },
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </>
      )}

      {/* 顶部提示信息 */}
      <Box
        sx={{
          position: 'absolute',
          top: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '8px 16px',
          borderRadius: '4px',
          fontSize: '14px',
          pointerEvents: 'none',
        }}
      >
        {windowRect ? '拖拽窗口或调整手柄以选择窗口 • ESC 取消 • Enter 确认' : '点击一个窗口以选择 • ESC 取消 • Enter 确认 (无选择时为全屏)'}
      </Box>
    </Box>
  );
};
