/**
 * Canvas快捷操作栏React组件实现
 * 为截图覆盖层提供高性能的工具栏UI
 */
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Box, Paper, IconButton, Tooltip, Fade } from '@mui/material';
import { 
  Save as SaveIcon,
  ContentCopy as CopyIcon,
  Edit as EditIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  Close as CloseIcon,
  DragIndicator as DragIcon
} from '@mui/icons-material';
import { ToolbarPosition, ToolbarActionType } from '../types/toolbar';
import { useEditorStore } from '../store/editorStore';

interface CanvasToolbarProps {
  visible: boolean;
  position: ToolbarPosition;
  onAction: (action: ToolbarActionType) => void;
  onPositionChange: (position: ToolbarPosition) => void;
  disabled?: boolean;
}

const CanvasToolbar: React.FC<CanvasToolbarProps> = ({
  visible,
  position,
  onAction,
  onPositionChange,
  disabled = false
}) => {
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const { history, historyIndex } = useEditorStore();

  // 计算撤销/重做状态
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  console.log('[TOOLBAR] 🎯 CanvasToolbar render:', { visible, position, disabled });
  console.log('[TOOLBAR] 🎯 CanvasToolbar will render:', visible ? 'YES' : 'NO');

  // 拖拽处理
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (disabled) return;
    
    const rect = toolbarRef.current?.getBoundingClientRect();
    if (!rect) return;

    setIsDragging(true);
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    
    console.log('[TOOLBAR] 🎯 Drag started');
  }, [disabled]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const newPosition: ToolbarPosition = {
      x: e.clientX - dragOffset.x,
      y: e.clientY - dragOffset.y
    };

    onPositionChange(newPosition);
    console.log('[TOOLBAR] 🎯 Toolbar position changed:', newPosition);
  }, [isDragging, dragOffset, onPositionChange]);

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      console.log('[TOOLBAR] 🎯 Drag ended');
    }
  }, [isDragging]);

  // 设置全局拖拽事件监听器
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 工具栏按钮配置
  const toolbarButtons = [
    {
      id: 'save',
      icon: <SaveIcon />,
      tooltip: '保存截图',
      action: 'save' as ToolbarActionType,
      disabled: false
    },
    {
      id: 'copy',
      icon: <CopyIcon />,
      tooltip: '复制到剪贴板',
      action: 'copy' as ToolbarActionType,
      disabled: false
    },
    {
      id: 'edit',
      icon: <EditIcon />,
      tooltip: '编辑模式',
      action: 'edit' as ToolbarActionType,
      disabled: false
    },
    {
      id: 'undo',
      icon: <UndoIcon />,
      tooltip: '撤销',
      action: 'undo' as ToolbarActionType,
      disabled: !canUndo
    },
    {
      id: 'redo',
      icon: <RedoIcon />,
      tooltip: '重做',
      action: 'redo' as ToolbarActionType,
      disabled: !canRedo
    },
    {
      id: 'close',
      icon: <CloseIcon />,
      tooltip: '关闭',
      action: 'close' as ToolbarActionType,
      disabled: false
    }
  ];

  const handleButtonClick = useCallback((action: ToolbarActionType) => {
    if (disabled) return;
    console.log('[TOOLBAR] 🎯 Button clicked:', action);
    onAction(action);
  }, [disabled, onAction]);

  if (!visible) {
    console.log('[TOOLBAR] 🎯 Toolbar not visible, returning null');
    return null;
  }

  return (
    <Fade in={visible} timeout={200}>
      <Paper
        ref={toolbarRef}
        elevation={8}
        sx={{
          position: 'fixed',
          left: position.x,
          top: position.y,
          zIndex: 9999,
          display: 'flex',
          alignItems: 'center',
          padding: 1,
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          borderRadius: 2,
          cursor: isDragging ? 'grabbing' : 'grab',
          userSelect: 'none',
          border: '1px solid rgba(0, 0, 0, 0.1)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
          transition: isDragging ? 'none' : 'all 0.2s ease',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 1)',
            boxShadow: '0 6px 25px rgba(0, 0, 0, 0.2)',
          }
        }}
        onMouseDown={handleMouseDown}
      >
        {/* 拖拽手柄 */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            marginRight: 1,
            color: 'text.secondary',
            cursor: 'grab'
          }}
        >
          <DragIcon fontSize="small" />
        </Box>

        {/* 工具栏按钮 */}
        {toolbarButtons.map((button) => (
          <Tooltip key={button.id} title={button.tooltip} arrow>
            <span>
              <IconButton
                size="small"
                onClick={() => handleButtonClick(button.action)}
                disabled={disabled || button.disabled}
                sx={{
                  margin: 0.25,
                  padding: 1,
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.08)',
                  },
                  '&.Mui-disabled': {
                    opacity: 0.5,
                  }
                }}
              >
                {button.icon}
              </IconButton>
            </span>
          </Tooltip>
        ))}
      </Paper>
    </Fade>
  );
};

export default CanvasToolbar;
