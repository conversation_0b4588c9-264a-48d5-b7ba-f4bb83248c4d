import React, { useState, useRef, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Toolbar,
  Tooltip,
  Divider,
  TextField,
  Grid,
  Paper,
  Collapse
} from '@mui/material';
import {
  Close as CloseIcon,
  Save as SaveIcon,
  Crop as CropIcon,
  Cancel as CancelIcon,
  Check as CheckIcon,
  Refresh as ResetIcon,
  Edit as EditIcon,
  FlashOn as QuickSaveIcon
} from '@mui/icons-material';

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface ScreenshotPreviewProps {
  open: boolean;
  onClose: () => void;
  onSave: (filename?: string, cropArea?: CropArea) => void;
  onEdit: () => void;
  onAdvancedEdit?: () => void;
  imagePath: string;
  captureType: 'fullscreen' | 'window';
  defaultFilename?: string;
}

export const ScreenshotPreview: React.FC<ScreenshotPreviewProps> = ({
  open,
  onClose,
  onSave,
  onEdit,
  onAdvancedEdit,
  imagePath,
  captureType,
  defaultFilename = ''
}) => {
  const [filename, setFilename] = useState(defaultFilename);
  const [isEditing, setIsEditing] = useState(false);
  const [isCropping, setIsCropping] = useState(false);
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 0, height: 0 });
  const [imageNaturalSize, setImageNaturalSize] = useState({ width: 0, height: 0 });
  const imageRef = useRef<HTMLImageElement>(null);

  const handleSave = () => {
    const finalCropArea = isCropping && cropArea.width > 0 && cropArea.height > 0 ? cropArea : undefined;
    onSave(filename || defaultFilename, finalCropArea);
    onClose();
  };

  // 快速保存 - 使用默认文件名直接保存
  const handleQuickSave = () => {
    onSave(defaultFilename);
    onClose();
  };

  const handleEdit = () => {
    setIsEditing(true);
    setIsCropping(true);
    onEdit();
  };

  const handleImageLoad = useCallback(() => {
    if (imageRef.current) {
      setImageNaturalSize({
        width: imageRef.current.naturalWidth,
        height: imageRef.current.naturalHeight
      });
      // Initialize crop area to full image
      setCropArea({
        x: 0,
        y: 0,
        width: imageRef.current.naturalWidth,
        height: imageRef.current.naturalHeight
      });
    }
  }, []);

  const handleCropChange = (field: keyof CropArea, value: number) => {
    setCropArea(prev => ({ ...prev, [field]: Math.max(0, value) }));
  };

  const handleResetCrop = () => {
    setCropArea({
      x: 0,
      y: 0,
      width: imageNaturalSize.width,
      height: imageNaturalSize.height
    });
  };

  const handleCancelCrop = () => {
    setIsCropping(false);
    setIsEditing(false);
    handleResetCrop();
  };

  const handleConfirmCrop = () => {
    setIsCropping(false);
    setIsEditing(false);
  };

  const getCaptureTypeLabel = () => {
    switch (captureType) {
      case 'fullscreen':
        return 'Full Screen Screenshot';
      case 'window':
        return 'Window Screenshot';
      default:
        return 'Screenshot';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            height: '90vh',
            maxHeight: '90vh'
          }
        }
      }}
    >
      <DialogTitle sx={{ p: 0 }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {getCaptureTypeLabel()} Preview
          </Typography>
          
          <Tooltip title="Advanced Edit">
            <IconButton
              color="primary"
              onClick={onAdvancedEdit}
              disabled={isEditing}
            >
              <EditIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Quick Crop">
            <IconButton
              color="primary"
              onClick={handleEdit}
              disabled={isEditing}
            >
              <CropIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Close">
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Tooltip>
        </Toolbar>
        <Divider />
      </DialogTitle>

      <DialogContent sx={{ p: 2, display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Image Preview */}
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            bgcolor: 'grey.100',
            borderRadius: 1,
            mb: 2,
            overflow: 'hidden',
            position: 'relative'
          }}
        >
          {imagePath ? (
            <img
              ref={imageRef}
              src={imagePath}
              alt="Screenshot preview"
              onLoad={handleImageLoad}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
            />
          ) : (
            <Typography color="text.secondary">
              Loading preview...
            </Typography>
          )}

          {isCropping && (
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                display: 'flex',
                gap: 1
              }}
            >
              <Tooltip title="Confirm Crop">
                <IconButton
                  size="small"
                  onClick={handleConfirmCrop}
                  sx={{ bgcolor: 'success.main', color: 'white', '&:hover': { bgcolor: 'success.dark' } }}
                >
                  <CheckIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Reset Crop">
                <IconButton
                  size="small"
                  onClick={handleResetCrop}
                  sx={{ bgcolor: 'info.main', color: 'white', '&:hover': { bgcolor: 'info.dark' } }}
                >
                  <ResetIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Cancel Crop">
                <IconButton
                  size="small"
                  onClick={handleCancelCrop}
                  sx={{ bgcolor: 'error.main', color: 'white', '&:hover': { bgcolor: 'error.dark' } }}
                >
                  <CancelIcon />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        {/* Crop Controls */}
        <Collapse in={isCropping}>
          <Paper sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
            <Typography variant="subtitle2" gutterBottom>
              Crop Settings
            </Typography>
            <Grid container spacing={2}>
              <Grid size={{ xs: 6, sm: 3 }}>
                <TextField
                  fullWidth
                  label="X Position"
                  type="number"
                  size="small"
                  value={cropArea.x}
                  onChange={(e) => handleCropChange('x', parseInt(e.target.value) || 0)}
                  slotProps={{
                    htmlInput: { min: 0, max: imageNaturalSize.width }
                  }}
                />
              </Grid>
              <Grid size={{ xs: 6, sm: 3 }}>
                <TextField
                  fullWidth
                  label="Y Position"
                  type="number"
                  size="small"
                  value={cropArea.y}
                  onChange={(e) => handleCropChange('y', parseInt(e.target.value) || 0)}
                  slotProps={{
                    htmlInput: { min: 0, max: imageNaturalSize.height }
                  }}
                />
              </Grid>
              <Grid size={{ xs: 6, sm: 3 }}>
                <TextField
                  fullWidth
                  label="Width"
                  type="number"
                  size="small"
                  value={cropArea.width}
                  onChange={(e) => handleCropChange('width', parseInt(e.target.value) || 0)}
                  slotProps={{
                    htmlInput: { min: 1, max: imageNaturalSize.width - cropArea.x }
                  }}
                />
              </Grid>
              <Grid size={{ xs: 6, sm: 3 }}>
                <TextField
                  fullWidth
                  label="Height"
                  type="number"
                  size="small"
                  value={cropArea.height}
                  onChange={(e) => handleCropChange('height', parseInt(e.target.value) || 0)}
                  slotProps={{
                    htmlInput: { min: 1, max: imageNaturalSize.height - cropArea.y }
                  }}
                />
              </Grid>
            </Grid>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              Crop area: {cropArea.width} × {cropArea.height} pixels at ({cropArea.x}, {cropArea.y})
              {imageNaturalSize.width > 0 && (
                <> • Original: {imageNaturalSize.width} × {imageNaturalSize.height}</>
              )}
            </Typography>
          </Paper>
        </Collapse>

        {/* Filename Input */}
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12 }}>
            <TextField
              fullWidth
              label="Filename"
              value={filename}
              onChange={(e) => setFilename(e.target.value)}
              placeholder={defaultFilename}
              helperText="Leave empty to use default filename"
              variant="outlined"
              size="small"
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 2, pt: 0, justifyContent: 'space-between' }}>
        <Button
          onClick={onClose}
          startIcon={<CancelIcon />}
          color="inherit"
        >
          Cancel
        </Button>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Save with default filename">
            <Button
              onClick={handleQuickSave}
              startIcon={<QuickSaveIcon />}
              variant="outlined"
              color="primary"
            >
              Quick Save
            </Button>
          </Tooltip>

          <Button
            onClick={handleSave}
            startIcon={<SaveIcon />}
            variant="contained"
            color="primary"
          >
            {isCropping && cropArea.width > 0 && cropArea.height > 0
              ? 'Save Cropped Screenshot'
              : 'Save Screenshot'}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};
