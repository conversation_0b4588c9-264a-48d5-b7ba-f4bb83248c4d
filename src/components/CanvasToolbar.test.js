import { act } from '@testing-library/react';
import CanvasToolbar from './CanvasToolbar';
import { useEditorStore } from '../store/editorStore';

// Mock zustand store
jest.mock('../store/editorStore', () => {
  const originalModule = jest.requireActual('../store/editorStore');
  return {
    ...originalModule,
    useEditorStore: jest.fn()
  };
});

// Mock requestAnimationFrame
beforeEach(() => {
  jest.useFakeTimers();
  window.requestAnimationFrame = (cb) => setTimeout(cb, 16);
});

afterEach(() => {
  jest.useRealTimers();
});

describe('CanvasToolbar', () => {
  let canvas, toolbar;
  const mockSubscribe = jest.fn();
  
  beforeEach(() => {
    // Setup canvas
    canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 600;
    document.body.appendChild(canvas);
    
    // Mock store
    useEditorStore.mockImplementation((selector) => {
      if (selector === ((state) => state.windowState.selectedWindow)) {
        return null;
      }
      return undefined;
    });
    
    // Mock store subscription
    useEditorStore.subscribe = mockSubscribe;
    
    // Create toolbar instance
    toolbar = new CanvasToolbar(canvas);
  });
  
  afterEach(() => {
    if (toolbar) toolbar.destroy();
    document.body.removeChild(canvas);
  });
  
  test('订阅editorStore的状态变化', () => {
    expect(mockSubscribe).toHaveBeenCalled();
  });
  
  test('当窗口位置变化时工具栏同步移动', () => {
    // 初始显示工具栏
    toolbar.show({x: 100, y: 100, width: 200, height: 200});
    
    // 模拟窗口位置更新
    const newWindow = {x: 300, y: 300, width: 400, height: 400};
    const callback = mockSubscribe.mock.calls[0][1];
    callback(newWindow);
    
    // 检查位置是否更新
    expect(toolbar.position.x).toBeGreaterThan(300);
    expect(toolbar.position.y).toBeGreaterThan(300);
  });
  
  test('工具栏位置边界检查', () => {
    // 测试左边界
    toolbar.show({x: 0, y: 0, width: 100, height: 100});
    const callback = mockSubscribe.mock.calls[0][1];
    
    // 模拟窗口在左上角
    callback({x: 0, y: 0, width: 100, height: 100});
    expect(toolbar.position.x).toBeGreaterThanOrEqual(10 * toolbar.dpr);
    
    // 模拟窗口在右下角
    callback({x: 700, y: 500, width: 100, height: 100});
    expect(toolbar.position.x + toolbar.bounds.width).toBeLessThanOrEqual(790 * toolbar.dpr);
  });
  
  test('位置变化时使用动画', () => {
    toolbar.show({x: 100, y: 100, width: 200, height: 200});
    const initialPos = {...toolbar.position};
    
    // 模拟窗口位置更新
    const newWindow = {x: 300, y: 300, width: 400, height: 400};
    const callback = mockSubscribe.mock.calls[0][1];
    callback(newWindow);
    
    // 检查动画是否启动
    expect(toolbar.animations.has('toolbar-pos-x')).toBe(true);
    expect(toolbar.animations.has('toolbar-pos-y')).toBe(true);
    
    // 模拟动画执行
    act(() => {
      jest.advanceTimersByTime(100);
    });
    
    // 检查位置是否变化
    expect(toolbar.position.x).not.toEqual(initialPos.x);
    expect(toolbar.position.y).not.toEqual(initialPos.y);
  });
  
  test('性能优化 - 使用requestAnimationFrame和节流', () => {
    toolbar.show({x: 100, y: 100, width: 200, height: 200});
    const callback = mockSubscribe.mock.calls[0][1];
    
    // 第一次调用
    callback({x: 200, y: 200, width: 300, height: 300});
    const firstUpdate = toolbar.lastUpdateTime;
    
    // 立即第二次调用（应被节流）
    callback({x: 210, y: 210, width: 300, height: 300});
    expect(toolbar.lastUpdateTime).toEqual(firstUpdate);
    
    // 16ms后再次调用（应通过）
    act(() => {
      jest.advanceTimersByTime(17);
    });
    callback({x: 220, y: 220, width: 300, height: 300});
    expect(toolbar.lastUpdateTime).not.toEqual(firstUpdate);
  });
});