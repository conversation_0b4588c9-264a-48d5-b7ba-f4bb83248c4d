import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert
} from '@mui/material';
import {
  Speed as SpeedIcon,
  Close as CloseIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { invoke } from '@tauri-apps/api/core';

interface PerformanceBenchmark {
  window_enumeration_ms: number;
  fullscreen_capture_ms: number;
  window_capture_ms: number;
  region_capture_ms: number;
  total_windows: number;
  memory_usage_mb: number;
}

interface PerformanceMonitorProps {
  open: boolean;
  onClose: () => void;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  open,
  onClose
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [benchmark, setBenchmark] = useState<PerformanceBenchmark | null>(null);
  const [error, setError] = useState<string | null>(null);

  const runBenchmark = async () => {
    try {
      setIsRunning(true);
      setError(null);
      setBenchmark(null);

      console.log('Starting performance benchmark...');
      const result = await invoke<PerformanceBenchmark>('run_performance_benchmark');
      setBenchmark(result);
      console.log('Benchmark completed:', result);
    } catch (err) {
      console.error('Benchmark failed:', err);
      setError(err as string);
    } finally {
      setIsRunning(false);
    }
  };

  const getPerformanceStatus = (value: number, threshold: number) => {
    if (value <= threshold) {
      return { color: 'success' as const, icon: <CheckIcon fontSize="small" /> };
    } else if (value <= threshold * 2) {
      return { color: 'warning' as const, icon: <WarningIcon fontSize="small" /> };
    } else {
      return { color: 'error' as const, icon: <ErrorIcon fontSize="small" /> };
    }
  };

  const formatTime = (ms: number) => {
    return `${ms.toFixed(2)} ms`;
  };

  const formatMemory = (mb: number) => {
    return `${mb.toFixed(2)} MB`;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SpeedIcon />
        性能监控与基准测试
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="body2" color="text.secondary">
            运行性能基准测试以评估截图功能的性能表现。目标是所有操作都在1秒内完成。
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            基准测试失败: {error}
          </Alert>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <Button
            variant="contained"
            onClick={runBenchmark}
            disabled={isRunning}
            startIcon={isRunning ? <CircularProgress size={20} /> : <SpeedIcon />}
            size="large"
          >
            {isRunning ? '运行中...' : '开始性能测试'}
          </Button>
        </Box>

        {benchmark && (
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell><strong>测试项目</strong></TableCell>
                  <TableCell align="right"><strong>耗时</strong></TableCell>
                  <TableCell align="center"><strong>状态</strong></TableCell>
                  <TableCell><strong>说明</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell>窗口枚举</TableCell>
                  <TableCell align="right">{formatTime(benchmark.window_enumeration_ms)}</TableCell>
                  <TableCell align="center">
                    <Chip
                      {...getPerformanceStatus(benchmark.window_enumeration_ms, 50)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>获取所有窗口列表 ({benchmark.total_windows} 个窗口)</TableCell>
                </TableRow>

                <TableRow>
                  <TableCell>全屏截图</TableCell>
                  <TableCell align="right">{formatTime(benchmark.fullscreen_capture_ms)}</TableCell>
                  <TableCell align="center">
                    <Chip
                      {...getPerformanceStatus(benchmark.fullscreen_capture_ms, 500)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>捕获整个屏幕</TableCell>
                </TableRow>

                <TableRow>
                  <TableCell>窗口截图</TableCell>
                  <TableCell align="right">{formatTime(benchmark.window_capture_ms)}</TableCell>
                  <TableCell align="center">
                    <Chip
                      {...getPerformanceStatus(benchmark.window_capture_ms, 300)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>捕获单个窗口</TableCell>
                </TableRow>

                <TableRow>
                  <TableCell>区域截图</TableCell>
                  <TableCell align="right">{formatTime(benchmark.region_capture_ms)}</TableCell>
                  <TableCell align="center">
                    <Chip
                      {...getPerformanceStatus(benchmark.region_capture_ms, 100)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>裁剪指定区域 (200×200)</TableCell>
                </TableRow>

                <TableRow>
                  <TableCell>内存使用</TableCell>
                  <TableCell align="right">{formatMemory(benchmark.memory_usage_mb)}</TableCell>
                  <TableCell align="center">
                    <Chip
                      {...getPerformanceStatus(benchmark.memory_usage_mb, 100)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>估算内存占用</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {benchmark && (
          <Box sx={{ mt: 2 }}>
            <Alert 
              severity={
                benchmark.fullscreen_capture_ms < 1000 && 
                benchmark.window_capture_ms < 1000 && 
                benchmark.region_capture_ms < 1000 
                  ? 'success' 
                  : 'warning'
              }
            >
              {benchmark.fullscreen_capture_ms < 1000 && 
               benchmark.window_capture_ms < 1000 && 
               benchmark.region_capture_ms < 1000
                ? '🎉 性能表现优秀！所有操作都在1秒内完成。'
                : '⚠️ 部分操作超过1秒，可能需要进一步优化。'
              }
            </Alert>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} startIcon={<CloseIcon />}>
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};
