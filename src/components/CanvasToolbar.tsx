import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  ButtonGroup,
  Button,
  Tooltip,
  Paper,
  IconButton,
  Fade,
  useTheme,
  alpha
} from '@mui/material';
import {
  CameraAlt as CaptureIcon,
  Close as CancelIcon,
  ContentCopy as CopyIcon,
  MoreHoriz as MoreI<PERSON>,
  DragIndicator as DragIcon,
  Rectangle as RectangleIcon,
  RadioButtonUnchecked as EllipseIcon,
  ArrowForward as ArrowIcon,
  Brush as PenIcon,
  TextFields as TextIcon,
  BlurOn as MosaicIcon,
  LooksOne as NumberIcon,
  Undo as UndoIcon,
  Download as DownloadIcon,
  Check as ConfirmIcon
} from '@mui/icons-material';
import { invoke } from '@tauri-apps/api/core';
import {
  CanvasToolbarProps,
  ToolbarButton,
  DEFAULT_TOOLBAR_BUTTONS,
  DEFAULT_TOOLBAR_CONFIG,
  ToolbarActionType
} from '../types/toolbar';
import { useDragSystem } from '../hooks/useDragSystem';
import { useZIndexManager } from '../hooks/useZIndexManager';

// 图标映射
const ICON_MAP: Record<string, React.ReactElement> = {
  '📷': <CaptureIcon />,
  '❌': <CancelIcon />,
  '✏️': <PenIcon />,
  '📋': <CopyIcon />,
  '💾': <DownloadIcon />,
  '⋯': <MoreIcon />,
  '⬜': <RectangleIcon />,
  '⭕': <EllipseIcon />,
  '↗️': <ArrowIcon />,
  'T': <TextIcon />,
  '🔲': <MosaicIcon />,
  '①': <NumberIcon />,
  '↶': <UndoIcon />,
  '✓': <ConfirmIcon />
};

export const CanvasToolbar: React.FC<CanvasToolbarProps> = ({
  visible,
  position,
  onAction,
  onPositionChange,
  config = {},
  disabled = false,
  selectedRegion
}) => {
  const theme = useTheme();
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [buttons] = useState<ToolbarButton[]>(DEFAULT_TOOLBAR_BUTTONS);

  const finalConfig = { ...DEFAULT_TOOLBAR_CONFIG, ...config };

  // 增强的拖拽系统
  const {
    isDragging,
    handleDragStart: handleEnhancedDragStart,
    setPosition
  } = useDragSystem(toolbarRef, {
    constraints: {
      minX: 0,
      minY: 0,
      maxX: window.innerWidth - 400, // 工具栏宽度
      maxY: window.innerHeight - 60   // 工具栏高度
    },
    enableTouch: true,
    enableAnimation: true,
    onDragStart: (pos) => {
      console.log('[TOOLBAR] 🎯 Drag started:', pos);
      onAction('dragStart');
    },
    onDragMove: () => {
      // 实时更新位置，但不触发过多的回调
    },
    onDragEnd: (pos) => {
      console.log('[TOOLBAR] 🎯 Drag ended:', pos);
      onAction('dragEnd');
    },
    onPositionChange
  });

  // Z-Index管理
  const {
    zIndex,
    elevateToTop,
    restoreNormalLevel
  } = useZIndexManager({
    defaultLayer: 10002, // Z_INDEX_LAYERS.TOOLBAR
    autoRestore: true,
    restoreDelay: 300
  });

  // 处理按钮点击
  const handleButtonClick = useCallback(async (button: ToolbarButton) => {
    if (disabled || button.disabled) return;

    try {
      // 记录用户操作
      console.log(`[TOOLBAR] Button clicked: ${button.action}`);
      
      // 执行对应的操作
      switch (button.action) {
        case 'confirmCapture':
          if (selectedRegion) {
            await invoke('capture_region_from_overlay', { region: selectedRegion });
          }
          break;
        case 'cancelCapture':
          await invoke('close_all_overlays');
          break;
        case 'copyToClipboard':
          if (selectedRegion) {
            await invoke('copy_selection_to_clipboard', { region: selectedRegion });
          }
          break;
        case 'saveToFile':
          if (selectedRegion) {
            await invoke('save_region_to_file', { region: selectedRegion });
          }
          break;
        default:
          // 其他操作通过回调处理
          onAction(button.action as ToolbarActionType);
          break;
      }
    } catch (error) {
      console.error(`[TOOLBAR] Action failed: ${button.action}`, error);
    }
  }, [disabled, selectedRegion, onAction]);

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!visible || disabled) return;

      const button = buttons.find(btn => {
        if (!btn.shortcut) return false;
        
        const shortcut = btn.shortcut.toLowerCase();
        const key = event.key.toLowerCase();
        
        // 简单的快捷键匹配
        if (shortcut === 'enter' && key === 'enter') return true;
        if (shortcut === 'esc' && key === 'escape') return true;
        if (shortcut.includes('ctrl+') && event.ctrlKey) {
          const shortcutKey = shortcut.replace('ctrl+', '');
          return key === shortcutKey;
        }
        
        return key === shortcut;
      });

      if (button) {
        event.preventDefault();
        handleButtonClick(button);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [visible, disabled, buttons, handleButtonClick]);

  // 管理拖拽时的Z-Index
  useEffect(() => {
    if (isDragging) {
      elevateToTop();
    } else {
      restoreNormalLevel();
    }
  }, [isDragging, elevateToTop, restoreNormalLevel]);

  // 同步位置变化
  useEffect(() => {
    if (position && setPosition) {
      setPosition(position);
    }
  }, [position, setPosition]);

  // 渲染按钮
  const renderButton = (button: ToolbarButton) => {
    const icon = ICON_MAP[button.icon] || <span>{button.icon}</span>;
    const isDisabled = disabled || button.disabled;
    
    const buttonElement = (
      <Button
        key={button.id}
        variant={button.variant === 'primary' ? 'contained' : 'outlined'}
        color={button.variant === 'danger' ? 'error' : 'primary'}
        size={finalConfig.size}
        disabled={isDisabled}
        onClick={() => handleButtonClick(button)}
        startIcon={icon}
        sx={{
          minWidth: finalConfig.showLabels ? 'auto' : 40,
          px: finalConfig.showLabels ? 2 : 1,
          opacity: isDisabled ? 0.5 : 1,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: isDisabled ? 'none' : 'translateY(-1px)',
            boxShadow: isDisabled ? 'none' : theme.shadows[4]
          }
        }}
      >
        {finalConfig.showLabels ? button.label : ''}
      </Button>
    );

    if (button.shortcut) {
      return (
        <Tooltip
          key={button.id}
          title={`${button.label} (${button.shortcut})`}
          placement="top"
        >
          {buttonElement}
        </Tooltip>
      );
    }

    return buttonElement;
  };

  if (!visible) return null;

  return (
    <Fade in={visible} timeout={200}>
      <Paper
        ref={toolbarRef}
        elevation={isDragging ? 16 : 8}
        sx={{
          position: 'fixed',
          left: position.x,
          top: position.y,
          zIndex: zIndex,
          backgroundColor: alpha(theme.palette.background.paper, isDragging ? 0.98 : 0.95),
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.divider, isDragging ? 0.4 : 0.2)}`,
          borderRadius: 2,
          p: 1,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          cursor: isDragging ? 'grabbing' : 'default',
          userSelect: 'none',
          transform: isDragging ? 'scale(1.02)' : 'scale(1)',
          transition: isDragging ? 'none' : 'all 0.2s ease-in-out',
          '&:hover': {
            boxShadow: isDragging ? theme.shadows[20] : theme.shadows[12],
            transform: isDragging ? 'scale(1.02)' : 'scale(1.01)'
          }
        }}
      >
        {/* 拖拽手柄 */}
        {onPositionChange && (
          <IconButton
            size="small"
            onMouseDown={handleEnhancedDragStart}
            onTouchStart={handleEnhancedDragStart}
            sx={{
              cursor: isDragging ? 'grabbing' : 'grab',
              opacity: isDragging ? 1 : 0.6,
              transform: isDragging ? 'scale(1.1)' : 'scale(1)',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                opacity: 1,
                transform: 'scale(1.05)'
              },
              '&:active': {
                cursor: 'grabbing',
                transform: 'scale(1.1)'
              }
            }}
          >
            <DragIcon fontSize="small" />
          </IconButton>
        )}

        {/* 按钮组 */}
        <ButtonGroup
          variant="outlined"
          size={finalConfig.size}
          disabled={disabled}
          sx={{
            '& .MuiButton-root': {
              borderColor: alpha(theme.palette.divider, 0.3)
            }
          }}
        >
          {buttons.map(renderButton)}
        </ButtonGroup>
      </Paper>
    </Fade>
  );
};

export default CanvasToolbar;
