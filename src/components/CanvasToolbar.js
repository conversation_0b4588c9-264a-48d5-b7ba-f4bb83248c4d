/**
 * Canvas快捷操作栏实现
 * 为截图覆盖层提供高性能的工具栏UI
 */
import { useEditorStore } from '../store/editorStore';

class CanvasToolbar {
    constructor(canvas, options = {}) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.dpr = window.devicePixelRatio || 1;
        
        // 配置选项
        this.options = {
            theme: 'auto',
            buttonSize: 36,
            padding: 8,
            borderRadius: 8,
            animationDuration: 200,
            ...options
        };
        
        // 状态管理
        this.isVisible = false;
        this.isExpanded = false;
        this.hoveredButton = null;
        this.selectedButton = null;
        this.position = { x: 0, y: 0 };
        this.bounds = { x: 0, y: 0, width: 0, height: 0 };
        
        // 状态监听
        this.unsubscribeEditorStore = null;
        this.lastUpdateTime = 0;
        this.updateThrottle = 16; // ~60fps
        
        // 按钮配置
        this.buttons = this.createButtons();
        this.extendedButtons = this.createExtendedButtons();
        
        // 动画系统
        this.animations = new Map();
        this.animationFrame = null;
        
        // 主题系统
        this.theme = this.getTheme();
        
        // 事件回调
        this.callbacks = new Map();
        
        this.init();
    }

    // subscribeToEditorStore 方法已移至下方，此处移除重复定义
    
    init() {
        this.bindEvents();
        this.startRenderLoop();
        this.setupThemeWatcher();
        this.subscribeToEditorStore();
    }
    
    createButtons() {
        return [
            {
                id: 'confirm',
                icon: '✓',
                label: '确认截图',
                shortcut: 'Enter',
                color: '#4CAF50',
                action: 'confirmCapture'
            },
            {
                id: 'cancel',
                icon: '✕',
                label: '取消',
                shortcut: 'Esc',
                color: '#F44336',
                action: 'cancelCapture'
            },
            {
                id: 'edit',
                icon: '✏',
                label: '编辑',
                shortcut: 'E',
                action: 'openEditor'
            },
            {
                id: 'copy',
                icon: '📋',
                label: '复制',
                shortcut: 'Ctrl+C',
                action: 'copyToClipboard'
            },
            {
                id: 'save',
                icon: '💾',
                label: '保存',
                shortcut: 'Ctrl+S',
                action: 'saveToFile'
            },
            {
                id: 'more',
                icon: '⋯',
                label: '更多',
                action: 'toggleExpanded'
            }
        ];
    }
    
    createExtendedButtons() {
        return [
            {
                id: 'pin',
                icon: '📌',
                label: '贴图',
                action: 'pinImage'
            },
            {
                id: 'ocr',
                icon: '🔤',
                label: '文字识别',
                action: 'performOCR'
            },
            {
                id: 'share',
                icon: '🔗',
                label: '分享',
                action: 'shareImage'
            }
        ];
    }
    
    getTheme() {
        const themes = {
            light: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderColor: 'rgba(0, 0, 0, 0.1)',
                textColor: '#333333',
                hoverColor: 'rgba(0, 0, 0, 0.05)',
                shadowColor: 'rgba(0, 0, 0, 0.15)',
                accentColor: '#007AFF'
            },
            dark: {
                backgroundColor: 'rgba(45, 45, 45, 0.95)',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                textColor: '#FFFFFF',
                hoverColor: 'rgba(255, 255, 255, 0.1)',
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                accentColor: '#0A84FF'
            }
        };
        
        if (this.options.theme === 'auto') {
            const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            return themes[isDark ? 'dark' : 'light'];
        }
        
        return themes[this.options.theme] || themes.light;
    }
    
    setupThemeWatcher() {
        if (this.options.theme === 'auto') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', () => {
                this.theme = this.getTheme();
                this.requestRender();
            });
        }
    }
    
    bindEvents() {
        this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.canvas.addEventListener('click', this.onClick.bind(this));
        this.canvas.addEventListener('mouseleave', this.onMouseLeave.bind(this));
        document.addEventListener('keydown', this.onKeyDown.bind(this));
    }
    
    onMouseMove(event) {
        if (!this.isVisible) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = (event.clientX - rect.left) * this.dpr;
        const y = (event.clientY - rect.top) * this.dpr;
        
        const hoveredButton = this.getButtonAt(x, y);
        
        if (hoveredButton !== this.hoveredButton) {
            if (this.hoveredButton) {
                this.animateButtonHover(this.hoveredButton, false);
            }
            if (hoveredButton) {
                this.animateButtonHover(hoveredButton, true);
            }
            this.hoveredButton = hoveredButton;
            this.requestRender();
        }
        
        // 更新鼠标样式
        this.canvas.style.cursor = hoveredButton ? 'pointer' : 'default';
    }
    
    onClick(event) {
        if (!this.isVisible || !this.hoveredButton) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        this.executeAction(this.hoveredButton.action, this.hoveredButton);
    }
    
    onMouseLeave() {
        if (this.hoveredButton) {
            this.animateButtonHover(this.hoveredButton, false);
            this.hoveredButton = null;
            this.requestRender();
        }
        this.canvas.style.cursor = 'default';
    }
    
    onKeyDown(event) {
        if (!this.isVisible) return;
        
        // 查找匹配的快捷键
        const button = this.buttons.find(btn => 
            btn.shortcut && this.matchesShortcut(event, btn.shortcut)
        );
        
        if (button) {
            event.preventDefault();
            this.executeAction(button.action, button);
        }
    }
    
    matchesShortcut(event, shortcut) {
        const parts = shortcut.toLowerCase().split('+');
        const key = parts.pop();
        
        const modifiers = {
            ctrl: event.ctrlKey || event.metaKey,
            alt: event.altKey,
            shift: event.shiftKey
        };
        
        // 检查修饰键
        for (const part of parts) {
            if (!modifiers[part]) return false;
        }
        
        // 检查主键
        return event.key.toLowerCase() === key.toLowerCase() ||
               event.code.toLowerCase() === key.toLowerCase();
    }
    
    getButtonAt(x, y) {
        const allButtons = this.isExpanded 
            ? [...this.buttons, ...this.extendedButtons]
            : this.buttons;
            
        return allButtons.find(button => {
            const bounds = button.bounds;
            return bounds && 
                   x >= bounds.x && x <= bounds.x + bounds.width &&
                   y >= bounds.y && y <= bounds.y + bounds.height;
        });
    }
    
    calculateLayout() {
        const buttonSize = this.options.buttonSize * this.dpr;
        const padding = this.options.padding * this.dpr;
        const gap = 4 * this.dpr;
        
        // 计算主按钮布局
        const mainButtonCount = this.buttons.length;
        const mainWidth = mainButtonCount * buttonSize + (mainButtonCount - 1) * gap + padding * 2;
        const mainHeight = buttonSize + padding * 2;
        
        // 计算扩展按钮布局
        let extendedHeight = 0;
        if (this.isExpanded && this.extendedButtons.length > 0) {
            extendedHeight = this.extendedButtons.length * (buttonSize + gap) + padding;
        }
        
        // 更新工具栏边界
        this.bounds = {
            x: this.position.x,
            y: this.position.y,
            width: mainWidth,
            height: mainHeight + extendedHeight
        };
        
        // 计算按钮位置
        this.buttons.forEach((button, index) => {
            button.bounds = {
                x: this.bounds.x + padding + index * (buttonSize + gap),
                y: this.bounds.y + padding,
                width: buttonSize,
                height: buttonSize
            };
        });
        
        // 计算扩展按钮位置
        if (this.isExpanded) {
            this.extendedButtons.forEach((button, index) => {
                button.bounds = {
                    x: this.bounds.x + padding,
                    y: this.bounds.y + mainHeight + padding + index * (buttonSize + gap),
                    width: buttonSize,
                    height: buttonSize
                };
            });
        }
    }
    
    show(selectionRect) {
        this.calculateOptimalPosition(selectionRect);
        this.calculateLayout();
        this.isVisible = true;
        this.animateShow();
    }

    // 此处的重复定义已移除

    /**
     * 同步工具栏位置到选中窗口
     * @param {Object} windowData - 窗口位置数据 {x, y, width, height}
     */
    syncPositionWithWindow(windowData) {
        const now = performance.now();
        if (now - this.lastUpdateTime < this.updateThrottle) return;
        
        this.lastUpdateTime = now;
        
        requestAnimationFrame(() => {
            const canvasRect = this.canvas.getBoundingClientRect();
            const dpr = this.dpr;
            
            // 转换为canvas坐标
            const windowRect = {
                x: (windowData.x - canvasRect.left) * dpr,
                y: (windowData.y - canvasRect.top) * dpr,
                width: windowData.width * dpr,
                height: windowData.height * dpr
            };
            
            // 计算工具栏新位置（右上角偏移）
            const newPosition = {
                x: windowRect.x + windowRect.width - this.bounds.width - 10 * dpr,
                y: windowRect.y + 10 * dpr
            };
            
            // 边界检查
            const margin = 10 * dpr;
            if (newPosition.x < margin) newPosition.x = margin;
            if (newPosition.y < margin) newPosition.y = margin;
            if (newPosition.x + this.bounds.width > canvasRect.width * dpr - margin) {
                newPosition.x = canvasRect.width * dpr - this.bounds.width - margin;
            }
            
            // 添加平滑过渡动画
            this.animatePosition(newPosition);
        });
    }

    /**
     * 动画方式更新工具栏位置
     * @param {Object} targetPosition - 目标位置 {x, y}
     */
    animatePosition(targetPosition) {
        const startX = this.position.x;
        const startY = this.position.y;
        const duration = this.options.animationDuration;
        
        // 为X坐标创建动画
        this.animate('toolbar-pos-x', startX, targetPosition.x, duration, 'easeOutQuad');
        
        // 为Y坐标创建动画
        this.animate('toolbar-pos-y', startY, targetPosition.y, duration, 'easeOutQuad', () => {
            // 动画完成后更新布局
            this.calculateLayout();
        });
    }
    
    hide() {
        this.animateHide(() => {
            this.isVisible = false;
            this.isExpanded = false;
            this.hoveredButton = null;
        });
    }
    
    calculateOptimalPosition(selectionRect) {
        const canvasRect = this.canvas.getBoundingClientRect();
        const toolbarSize = {
            width: (this.buttons.length * this.options.buttonSize + 
                   (this.buttons.length - 1) * 4 + this.options.padding * 2) * this.dpr,
            height: (this.options.buttonSize + this.options.padding * 2) * this.dpr
        };
        
        // 优先在选区下方显示
        let x = selectionRect.x + selectionRect.width / 2 - toolbarSize.width / 2;
        let y = selectionRect.y + selectionRect.height + 10 * this.dpr;
        
        // 边界检查和调整
        const margin = 10 * this.dpr;
        if (x < margin) x = margin;
        if (x + toolbarSize.width > canvasRect.width * this.dpr - margin) {
            x = canvasRect.width * this.dpr - toolbarSize.width - margin;
        }
        if (y + toolbarSize.height > canvasRect.height * this.dpr - margin) {
            y = selectionRect.y - toolbarSize.height - margin;
        }
        
        this.position = { x, y };
    }
    
    executeAction(action, button) {
        // 执行按钮动画
        this.animateButtonClick(button);
        
        // 触发回调
        if (this.callbacks.has(action)) {
            this.callbacks.get(action)(button);
        }
        
        // 内置动作处理
        switch (action) {
            case 'toggleExpanded':
                this.toggleExpanded();
                break;
            case 'cancelCapture':
                this.hide();
                break;
        }
    }
    
    toggleExpanded() {
        this.isExpanded = !this.isExpanded;
        this.calculateLayout();
        this.animateExpansion();
    }
    
    // 动画方法
    animateShow() {
        this.animate('opacity', 0, 1, this.options.animationDuration);
        this.animate('scale', 0.8, 1, this.options.animationDuration, 'easeOutBack');
    }
    
    animateHide(callback) {
        this.animate('opacity', 1, 0, this.options.animationDuration / 2);
        this.animate('scale', 1, 0.9, this.options.animationDuration / 2, 'easeInQuad', callback);
    }
    
    animateButtonHover(button, isHover) {
        const targetScale = isHover ? 1.1 : 1;
        this.animate(`button-${button.id}-scale`, button.scale || 1, targetScale, 100);
    }
    
    animateButtonClick(button) {
        this.animate(`button-${button.id}-scale`, 1, 0.9, 50, 'easeInQuad', () => {
            this.animate(`button-${button.id}-scale`, 0.9, 1, 100, 'easeOutQuad');
        });
    }
    
    animateExpansion() {
        this.animate('expansion', this.isExpanded ? 0 : 1, this.isExpanded ? 1 : 0, 200);
    }
    
    animate(property, from, to, duration, easing = 'easeOutQuad', callback = null) {
        const animation = {
            property,
            from,
            to,
            duration,
            startTime: performance.now(),
            easing: this.getEasingFunction(easing),
            callback
        };
        
        this.animations.set(property, animation);
    }
    
    getEasingFunction(name) {
        const easings = {
            linear: t => t,
            easeInQuad: t => t * t,
            easeOutQuad: t => t * (2 - t),
            easeOutBack: t => {
                const c1 = 1.70158;
                const c3 = c1 + 1;
                return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
            }
        };
        return easings[name] || easings.linear;
    }
    
    updateAnimations(currentTime) {
        let hasActiveAnimations = false;
        
        for (const [key, animation] of this.animations) {
            const elapsed = currentTime - animation.startTime;
            const progress = Math.min(elapsed / animation.duration, 1);
            
            if (progress >= 1) {
                this.applyAnimationValue(animation.property, animation.to);
                this.animations.delete(key);
                if (animation.callback) {
                    animation.callback();
                }
            } else {
                const easedProgress = animation.easing(progress);
                const value = animation.from + (animation.to - animation.from) * easedProgress;
                this.applyAnimationValue(animation.property, value);
                hasActiveAnimations = true;
            }
        }
        
        return hasActiveAnimations;
    }
    
    applyAnimationValue(property, value) {
        if (property === 'opacity') {
            this.opacity = value;
        } else if (property === 'scale') {
            this.scale = value;
        } else if (property.startsWith('button-') && property.endsWith('-scale')) {
            const buttonId = property.replace('button-', '').replace('-scale', '');
            const button = [...this.buttons, ...this.extendedButtons].find(b => b.id === buttonId);
            if (button) button.scale = value;
        } else if (property === 'expansion') {
            this.expansionProgress = value;
        } else if (property === 'toolbar-pos-x') {
            this.position.x = value;
            this.calculateLayout();
        } else if (property === 'toolbar-pos-y') {
            this.position.y = value;
            this.calculateLayout();
        }
    }
    
    startRenderLoop() {
        const render = (currentTime) => {
            const hasAnimations = this.updateAnimations(currentTime);
            
            if (this.isVisible || hasAnimations) {
                this.render();
            }
            
            this.animationFrame = requestAnimationFrame(render);
        };
        
        this.animationFrame = requestAnimationFrame(render);
    }
    
    requestRender() {
        // 标记需要重新渲染
        this.needsRender = true;
    }
    
    render() {
        if (!this.isVisible && !this.animations.size) return;
        
        this.ctx.save();
        
        // 应用全局变换
        if (this.scale !== undefined && this.scale !== 1) {
            const centerX = this.bounds.x + this.bounds.width / 2;
            const centerY = this.bounds.y + this.bounds.height / 2;
            this.ctx.translate(centerX, centerY);
            this.ctx.scale(this.scale, this.scale);
            this.ctx.translate(-centerX, -centerY);
        }
        
        if (this.opacity !== undefined && this.opacity !== 1) {
            this.ctx.globalAlpha = this.opacity;
        }
        
        // 绘制工具栏背景
        this.drawBackground();
        
        // 绘制按钮
        this.buttons.forEach(button => this.drawButton(button));
        
        // 绘制扩展按钮
        if (this.isExpanded || this.expansionProgress > 0) {
            this.drawExtendedButtons();
        }
        
        // 绘制工具提示
        if (this.hoveredButton) {
            this.drawTooltip(this.hoveredButton);
        }
        
        this.ctx.restore();
    }
    
    drawBackground() {
        const { x, y, width, height } = this.bounds;
        
        this.ctx.save();
        
        // 阴影
        this.ctx.shadowColor = this.theme.shadowColor;
        this.ctx.shadowBlur = 10 * this.dpr;
        this.ctx.shadowOffsetY = 2 * this.dpr;
        
        // 背景
        this.ctx.fillStyle = this.theme.backgroundColor;
        this.roundRect(x, y, width, height, this.options.borderRadius * this.dpr);
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    drawButton(button) {
        if (!button.bounds) return;
        
        const { x, y, width, height } = button.bounds;
        const scale = button.scale || 1;
        
        this.ctx.save();
        
        // 应用按钮缩放
        if (scale !== 1) {
            const centerX = x + width / 2;
            const centerY = y + height / 2;
            this.ctx.translate(centerX, centerY);
            this.ctx.scale(scale, scale);
            this.ctx.translate(-centerX, -centerY);
        }
        
        // 绘制悬停背景
        if (this.hoveredButton === button) {
            this.ctx.fillStyle = this.theme.hoverColor;
            this.roundRect(x, y, width, height, 4 * this.dpr);
            this.ctx.fill();
        }
        
        // 绘制图标
        this.drawIcon(button.icon, x + width / 2, y + height / 2, button.color);
        
        this.ctx.restore();
    }
    
    drawExtendedButtons() {
        const progress = this.expansionProgress || (this.isExpanded ? 1 : 0);
        
        this.ctx.save();
        this.ctx.globalAlpha *= progress;
        
        this.extendedButtons.forEach((button, index) => {
            // 添加延迟动画效果
            const delay = index * 0.1;
            const buttonProgress = Math.max(0, Math.min(1, (progress - delay) / (1 - delay)));
            
            if (buttonProgress > 0) {
                this.ctx.save();
                this.ctx.globalAlpha *= buttonProgress;
                
                // 从上方滑入效果
                const offsetY = (1 - buttonProgress) * -20 * this.dpr;
                this.ctx.translate(0, offsetY);
                
                this.drawButton(button);
                this.ctx.restore();
            }
        });
        
        this.ctx.restore();
    }
    
    drawIcon(icon, x, y, color = null) {
        this.ctx.save();
        
        this.ctx.font = `${16 * this.dpr}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillStyle = color || this.theme.textColor;
        
        this.ctx.fillText(icon, x, y);
        
        this.ctx.restore();
    }
    
    drawTooltip(button) {
        if (!button.label) return;
        
        const padding = 8 * this.dpr;
        const fontSize = 12 * this.dpr;
        
        this.ctx.save();
        this.ctx.font = `${fontSize}px Arial`;
        
        const textWidth = this.ctx.measureText(button.label).width;
        const tooltipWidth = textWidth + padding * 2;
        const tooltipHeight = fontSize + padding * 2;
        
        const x = button.bounds.x + button.bounds.width / 2 - tooltipWidth / 2;
        const y = button.bounds.y - tooltipHeight - 8 * this.dpr;
        
        // 背景
        this.ctx.fillStyle = this.theme.backgroundColor;
        this.ctx.shadowColor = this.theme.shadowColor;
        this.ctx.shadowBlur = 5 * this.dpr;
        this.roundRect(x, y, tooltipWidth, tooltipHeight, 4 * this.dpr);
        this.ctx.fill();
        
        // 文字
        this.ctx.shadowBlur = 0;
        this.ctx.fillStyle = this.theme.textColor;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(button.label, x + tooltipWidth / 2, y + tooltipHeight / 2);
        
        this.ctx.restore();
    }
    
    roundRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
    }
    
    // 公共API
    on(action, callback) {
        this.callbacks.set(action, callback);
    }
    
    off(action) {
        this.callbacks.delete(action);
    }
    
    destroy() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        if (this.unsubscribeEditorStore) {
            this.unsubscribeEditorStore();
        }
        this.callbacks.clear();
        this.animations.clear();
    }
}

export default CanvasToolbar;
