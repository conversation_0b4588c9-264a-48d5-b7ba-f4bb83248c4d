import React, { useEffect, useState, useCallback } from 'react';
import { Box, Fade, useTheme, alpha } from '@mui/material';
import { invoke } from '@tauri-apps/api/core';
import CanvasToolbar from './CanvasToolbar';
import { ToolbarPosition, ToolbarActionType } from '../types/toolbar';

interface ScreenshotOverlayToolbarProps {
  visible: boolean;
  selectedRegion?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  onCapture?: () => void;
  onCancel?: () => void;
  onEdit?: () => void;
}

export const ScreenshotOverlayToolbar: React.FC<ScreenshotOverlayToolbarProps> = ({
  visible,
  selectedRegion,
  onCapture,
  onCancel,
  onEdit
}) => {
  const theme = useTheme();
  const [toolbarPosition, setToolbarPosition] = useState<ToolbarPosition>({ x: 0, y: 0 });
  const [isProcessing, setIsProcessing] = useState(false);

  // 计算工具栏位置 - 跟随选择区域
  useEffect(() => {
    if (selectedRegion && visible) {
      // 将工具栏定位在选择区域下方中央
      const toolbarX = selectedRegion.x + selectedRegion.width / 2 - 150; // 假设工具栏宽度约300px
      const toolbarY = selectedRegion.y + selectedRegion.height + 10; // 距离选择区域10px
      
      // 确保工具栏不超出屏幕边界
      const maxX = window.innerWidth - 300;
      const maxY = window.innerHeight - 60;
      
      setToolbarPosition({
        x: Math.max(10, Math.min(toolbarX, maxX)),
        y: Math.max(10, Math.min(toolbarY, maxY))
      });
    }
  }, [selectedRegion, visible]);

  // 处理工具栏操作
  const handleToolbarAction = useCallback(async (action: ToolbarActionType) => {
    if (isProcessing) return;
    
    setIsProcessing(true);
    
    try {
      switch (action) {
        case 'confirmCapture':
          console.log('[OVERLAY_TOOLBAR] Confirming capture');
          if (selectedRegion) {
            await invoke('capture_region_from_overlay', { region: selectedRegion });
            onCapture?.();
          }
          break;
          
        case 'cancelCapture':
          console.log('[OVERLAY_TOOLBAR] Cancelling capture');
          await invoke('close_all_overlays');
          onCancel?.();
          break;
          
        case 'openEditor':
          console.log('[OVERLAY_TOOLBAR] Opening editor');
          onEdit?.();
          break;
          
        case 'copyToClipboard':
          console.log('[OVERLAY_TOOLBAR] Copying to clipboard');
          if (selectedRegion) {
            // 先捕获区域，然后复制
            const result = await invoke('capture_region_new', { 
              area: {
                x: selectedRegion.x,
                y: selectedRegion.y,
                width: selectedRegion.width,
                height: selectedRegion.height
              }
            });
            
            if (result) {
              await invoke('copy_edited_screenshot_to_clipboard', {
                imageDataUrl: `data:image/png;base64,${(result as any).base64}`
              });
              
              // 显示成功提示
              console.log('[OVERLAY_TOOLBAR] Copied to clipboard successfully');
            }
          }
          break;
          
        case 'saveToFile':
          console.log('[OVERLAY_TOOLBAR] Saving to file');
          if (selectedRegion) {
            const result = await invoke('capture_region_new', { 
              area: {
                x: selectedRegion.x,
                y: selectedRegion.y,
                width: selectedRegion.width,
                height: selectedRegion.height
              }
            });
            
            if (result) {
              await invoke('save_edited_screenshot', {
                imageDataUrl: `data:image/png;base64,${(result as any).base64}`,
                finalPath: null
              });
              
              console.log('[OVERLAY_TOOLBAR] Saved to file successfully');
            }
          }
          break;
          
        default:
          console.log(`[OVERLAY_TOOLBAR] Unhandled action: ${action}`);
          break;
      }
    } catch (error) {
      console.error(`[OVERLAY_TOOLBAR] Action failed: ${action}`, error);
    } finally {
      setIsProcessing(false);
    }
  }, [selectedRegion, onCapture, onCancel, onEdit, isProcessing]);

  // 处理工具栏位置变化
  const handlePositionChange = useCallback((newPosition: ToolbarPosition) => {
    setToolbarPosition(newPosition);
  }, []);

  if (!visible || !selectedRegion) {
    return null;
  }

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        pointerEvents: 'none', // 让背景不阻挡鼠标事件
        zIndex: 9998
      }}
    >
      {/* 选择区域信息显示 */}
      <Fade in={visible} timeout={200}>
        <Box
          sx={{
            position: 'absolute',
            top: selectedRegion.y - 30,
            left: selectedRegion.x,
            backgroundColor: alpha(theme.palette.background.paper, 0.9),
            color: theme.palette.text.primary,
            px: 1,
            py: 0.5,
            borderRadius: 1,
            fontSize: '0.75rem',
            fontFamily: 'monospace',
            pointerEvents: 'auto',
            border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
            backdropFilter: 'blur(5px)'
          }}
        >
          {Math.round(selectedRegion.width)} × {Math.round(selectedRegion.height)}
        </Box>
      </Fade>

      {/* 工具栏 */}
      <Box sx={{ pointerEvents: 'auto' }}>
        <CanvasToolbar
          visible={visible}
          position={toolbarPosition}
          onAction={handleToolbarAction}
          onPositionChange={handlePositionChange}
          disabled={isProcessing}
          selectedRegion={selectedRegion}
          config={{
            position: 'floating',
            showLabels: true,
            size: 'small',
            theme: 'auto'
          }}
        />
      </Box>
    </Box>
  );
};

export default ScreenshotOverlayToolbar;
