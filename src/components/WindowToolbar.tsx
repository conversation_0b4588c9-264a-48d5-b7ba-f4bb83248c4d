import React, { useEffect, useState } from 'react';
import { Box, IconButton, Tooltip } from '@mui/material';
import { Crop, TextFields, Brush, Save, Close, Undo } from '@mui/icons-material';

interface WindowToolbarProps {
  windowRect: { x: number; y: number; width: number; height: number };
  onSave: () => void;
  onCancel: () => void;
  onToolSelect: (tool: string) => void;
}

export const WindowToolbar: React.FC<WindowToolbarProps> = ({
  windowRect,
  onSave,
  onCancel,
  onToolSelect
}) => {
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [isVisible, setIsVisible] = useState(false);

  // Calculate toolbar position (avoid window occlusion)
  useEffect(() => {
    const toolbarHeight = 50;
    const toolbarWidth = 300;
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    
    // Default position: below window with 10px margin
    let top = windowRect.y + windowRect.height + 10;
    let left = windowRect.x;
    
    // If below position is offscreen, position above window
    if (top + toolbarHeight > screenHeight) {
      top = windowRect.y - toolbarHeight - 10;
    }
    
    // If left position is offscreen, adjust to stay visible
    if (left + toolbarWidth > screenWidth) {
      left = screenWidth - toolbarWidth - 10;
    } else if (left < 0) {
      left = 10;
    }
    
    setPosition({ top, left });
    setIsVisible(true);
  }, [windowRect]);

  const tools = [
    { id: 'pen', icon: <Brush />, title: 'Pen' },
    { id: 'text', icon: <TextFields />, title: 'Text' },
    { id: 'crop', icon: <Crop />, title: 'Crop' },
    { id: 'undo', icon: <Undo />, title: 'Undo' },
    { id: 'save', icon: <Save />, title: 'Save' },
    { id: 'cancel', icon: <Close />, title: 'Cancel' }
  ];

  return (
    <Box
      sx={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        display: 'flex',
        backgroundColor: 'rgba(50, 50, 50, 0.8)',
        borderRadius: '25px',
        padding: '8px 16px',
        gap: '10px',
        zIndex: 10000,
        opacity: isVisible ? 1 : 0,
        transition: 'opacity 0.3s ease-in-out',
        boxShadow: '0 4px 10px rgba(0,0,0,0.25)',
        backdropFilter: 'blur(10px)'
      }}
    >
      {tools.map((tool) => (
        <Tooltip key={tool.id} title={tool.title} arrow>
          <IconButton
            sx={{ 
              color: 'white',
              '&:hover': { 
                backgroundColor: 'rgba(255,255,255,0.2)' 
              }
            }}
            onClick={() => {
              if (tool.id === 'save') onSave();
              else if (tool.id === 'cancel') onCancel();
              else onToolSelect(tool.id);
            }}
          >
            {tool.icon}
          </IconButton>
        </Tooltip>
      ))}
    </Box>
  );
};