import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
} from '@mui/material';

interface RegionSelectorProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (region: { x: number; y: number; width: number; height: number }) => void;
}

export const RegionSelector: React.FC<RegionSelectorProps> = ({
  open,
  onClose,
  onConfirm,
}) => {
  const [region, setRegion] = useState({
    x: 0,
    y: 0,
    width: 800,
    height: 600,
  });

  const handleInputChange = (field: keyof typeof region) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = parseInt(event.target.value) || 0;
    setRegion(prev => ({ ...prev, [field]: value }));
  };

  const handleConfirm = () => {
    onConfirm(region);
    onClose();
  };

  const handleFullScreen = () => {
    // Get screen dimensions (approximate)
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    setRegion({
      x: 0,
      y: 0,
      width: screenWidth,
      height: screenHeight,
    });
  };

  const handleCenterRegion = () => {
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    setRegion({
      x: Math.floor((screenWidth - 800) / 2),
      y: Math.floor((screenHeight - 600) / 2),
      width: 800,
      height: 600,
    });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Select Screenshot Region</DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Specify the region coordinates and dimensions for the screenshot.
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          <Button 
            variant="outlined" 
            onClick={handleFullScreen}
            sx={{ mr: 1 }}
          >
            Full Screen
          </Button>
          <Button 
            variant="outlined" 
            onClick={handleCenterRegion}
          >
            Center Region
          </Button>
        </Box>

        <Grid container spacing={2}>
          <Grid size={{ xs: 6 }}>
            <TextField
              fullWidth
              label="X Position"
              type="number"
              value={region.x}
              onChange={handleInputChange('x')}
              inputProps={{ min: 0 }}
            />
          </Grid>
          <Grid size={{ xs: 6 }}>
            <TextField
              fullWidth
              label="Y Position"
              type="number"
              value={region.y}
              onChange={handleInputChange('y')}
              inputProps={{ min: 0 }}
            />
          </Grid>
          <Grid size={{ xs: 6 }}>
            <TextField
              fullWidth
              label="Width"
              type="number"
              value={region.width}
              onChange={handleInputChange('width')}
              inputProps={{ min: 1 }}
            />
          </Grid>
          <Grid size={{ xs: 6 }}>
            <TextField
              fullWidth
              label="Height"
              type="number"
              value={region.height}
              onChange={handleInputChange('height')}
              inputProps={{ min: 1 }}
            />
          </Grid>
        </Grid>

        <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
          Preview: {region.width} × {region.height} pixels at ({region.x}, {region.y})
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleConfirm} variant="contained">
          Capture Region
        </Button>
      </DialogActions>
    </Dialog>
  );
};
