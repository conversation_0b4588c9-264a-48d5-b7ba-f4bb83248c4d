/**
 * 碰撞检测系统
 * 
 * 该类负责检测工具栏与其他元素的碰撞，并提供智能的位置调整建议
 */

import { ScreenBounds, ScreenshotBounds, ToolbarPosition, ToolbarSize } from './IntelligentToolbarPositioner';

export interface CollisionInfo {
  withScreenshot: boolean;
  withScreenEdges: boolean;
  withOtherElements: boolean;
  suggestedAdjustment: ToolbarPosition | null;
  collisionDetails: CollisionDetail[];
}

export interface CollisionDetail {
  type: 'screenshot' | 'screen-edge' | 'element';
  severity: 'low' | 'medium' | 'high';
  description: string;
  affectedArea: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface ElementBounds {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  zIndex: number;
  type: string;
}

/**
 * 碰撞检测器
 */
export class CollisionDetector {
  private screenBounds: ScreenBounds;
  private toolbarSize: ToolbarSize;
  private edgeThreshold: number;

  constructor(
    screenBounds: ScreenBounds,
    toolbarSize: ToolbarSize,
    edgeThreshold: number = 50
  ) {
    this.screenBounds = screenBounds;
    this.toolbarSize = toolbarSize;
    this.edgeThreshold = edgeThreshold;
  }

  /**
   * 检测所有类型的碰撞
   */
  detectCollisions(
    toolbarPosition: ToolbarPosition,
    screenshotBounds: ScreenshotBounds,
    otherElements: ElementBounds[] = []
  ): CollisionInfo {
    console.log('[COLLISION] 🔍 Detecting collisions for toolbar position:', toolbarPosition);

    const collisionDetails: CollisionDetail[] = [];
    
    // 检测与截图的碰撞
    const screenshotCollision = this.checkScreenshotCollision(toolbarPosition, screenshotBounds);
    if (screenshotCollision) {
      collisionDetails.push(screenshotCollision);
    }

    // 检测与屏幕边缘的碰撞
    const edgeCollisions = this.checkScreenEdgeCollisions(toolbarPosition);
    collisionDetails.push(...edgeCollisions);

    // 检测与其他元素的碰撞
    const elementCollisions = this.checkElementCollisions(toolbarPosition, otherElements);
    collisionDetails.push(...elementCollisions);

    // 生成调整建议
    const suggestedAdjustment = this.calculateAdjustment(
      toolbarPosition,
      screenshotBounds,
      collisionDetails
    );

    const result: CollisionInfo = {
      withScreenshot: collisionDetails.some(d => d.type === 'screenshot'),
      withScreenEdges: collisionDetails.some(d => d.type === 'screen-edge'),
      withOtherElements: collisionDetails.some(d => d.type === 'element'),
      suggestedAdjustment,
      collisionDetails
    };

    console.log('[COLLISION] 📊 Collision detection result:', {
      screenshot: result.withScreenshot,
      edges: result.withScreenEdges,
      elements: result.withOtherElements,
      detailsCount: collisionDetails.length
    });

    return result;
  }

  /**
   * 检测与截图的碰撞
   */
  private checkScreenshotCollision(
    toolbarPosition: ToolbarPosition,
    screenshotBounds: ScreenshotBounds
  ): CollisionDetail | null {
    if (this.isOverlapping(toolbarPosition, this.toolbarSize, screenshotBounds)) {
      const overlapArea = this.calculateOverlapArea(toolbarPosition, this.toolbarSize, screenshotBounds);
      const overlapPercentage = (overlapArea.width * overlapArea.height) / 
                               (this.toolbarSize.width * this.toolbarSize.height);

      let severity: 'low' | 'medium' | 'high' = 'low';
      if (overlapPercentage > 0.5) severity = 'high';
      else if (overlapPercentage > 0.2) severity = 'medium';

      return {
        type: 'screenshot',
        severity,
        description: `工具栏与截图重叠 ${(overlapPercentage * 100).toFixed(1)}%`,
        affectedArea: overlapArea
      };
    }

    return null;
  }

  /**
   * 检测与屏幕边缘的碰撞
   */
  private checkScreenEdgeCollisions(toolbarPosition: ToolbarPosition): CollisionDetail[] {
    const collisions: CollisionDetail[] = [];
    const toolbarRight = toolbarPosition.x + this.toolbarSize.width;
    const toolbarBottom = toolbarPosition.y + this.toolbarSize.height;

    // 检测左边缘
    if (toolbarPosition.x < 0) {
      collisions.push({
        type: 'screen-edge',
        severity: 'high',
        description: `工具栏超出左边缘 ${Math.abs(toolbarPosition.x)}px`,
        affectedArea: {
          x: toolbarPosition.x,
          y: toolbarPosition.y,
          width: Math.abs(toolbarPosition.x),
          height: this.toolbarSize.height
        }
      });
    } else if (toolbarPosition.x < this.edgeThreshold) {
      collisions.push({
        type: 'screen-edge',
        severity: 'medium',
        description: `工具栏接近左边缘 ${toolbarPosition.x}px`,
        affectedArea: {
          x: 0,
          y: toolbarPosition.y,
          width: this.edgeThreshold,
          height: this.toolbarSize.height
        }
      });
    }

    // 检测右边缘
    if (toolbarRight > this.screenBounds.width) {
      const overflow = toolbarRight - this.screenBounds.width;
      collisions.push({
        type: 'screen-edge',
        severity: 'high',
        description: `工具栏超出右边缘 ${overflow}px`,
        affectedArea: {
          x: this.screenBounds.width,
          y: toolbarPosition.y,
          width: overflow,
          height: this.toolbarSize.height
        }
      });
    } else if (toolbarRight > this.screenBounds.width - this.edgeThreshold) {
      collisions.push({
        type: 'screen-edge',
        severity: 'medium',
        description: `工具栏接近右边缘 ${this.screenBounds.width - toolbarRight}px`,
        affectedArea: {
          x: this.screenBounds.width - this.edgeThreshold,
          y: toolbarPosition.y,
          width: this.edgeThreshold,
          height: this.toolbarSize.height
        }
      });
    }

    // 检测上边缘
    if (toolbarPosition.y < 0) {
      collisions.push({
        type: 'screen-edge',
        severity: 'high',
        description: `工具栏超出上边缘 ${Math.abs(toolbarPosition.y)}px`,
        affectedArea: {
          x: toolbarPosition.x,
          y: toolbarPosition.y,
          width: this.toolbarSize.width,
          height: Math.abs(toolbarPosition.y)
        }
      });
    } else if (toolbarPosition.y < this.edgeThreshold) {
      collisions.push({
        type: 'screen-edge',
        severity: 'medium',
        description: `工具栏接近上边缘 ${toolbarPosition.y}px`,
        affectedArea: {
          x: toolbarPosition.x,
          y: 0,
          width: this.toolbarSize.width,
          height: this.edgeThreshold
        }
      });
    }

    // 检测下边缘
    if (toolbarBottom > this.screenBounds.height) {
      const overflow = toolbarBottom - this.screenBounds.height;
      collisions.push({
        type: 'screen-edge',
        severity: 'high',
        description: `工具栏超出下边缘 ${overflow}px`,
        affectedArea: {
          x: toolbarPosition.x,
          y: this.screenBounds.height,
          width: this.toolbarSize.width,
          height: overflow
        }
      });
    } else if (toolbarBottom > this.screenBounds.height - this.edgeThreshold) {
      collisions.push({
        type: 'screen-edge',
        severity: 'medium',
        description: `工具栏接近下边缘 ${this.screenBounds.height - toolbarBottom}px`,
        affectedArea: {
          x: toolbarPosition.x,
          y: this.screenBounds.height - this.edgeThreshold,
          width: this.toolbarSize.width,
          height: this.edgeThreshold
        }
      });
    }

    return collisions;
  }

  /**
   * 检测与其他元素的碰撞
   */
  private checkElementCollisions(
    toolbarPosition: ToolbarPosition,
    otherElements: ElementBounds[]
  ): CollisionDetail[] {
    const collisions: CollisionDetail[] = [];

    for (const element of otherElements) {
      if (this.isOverlapping(toolbarPosition, this.toolbarSize, element)) {
        const overlapArea = this.calculateOverlapArea(toolbarPosition, this.toolbarSize, element);
        const overlapPercentage = (overlapArea.width * overlapArea.height) / 
                                 (this.toolbarSize.width * this.toolbarSize.height);

        let severity: 'low' | 'medium' | 'high' = 'low';
        if (overlapPercentage > 0.5) severity = 'high';
        else if (overlapPercentage > 0.2) severity = 'medium';

        collisions.push({
          type: 'element',
          severity,
          description: `工具栏与${element.type}元素重叠 ${(overlapPercentage * 100).toFixed(1)}%`,
          affectedArea: overlapArea
        });
      }
    }

    return collisions;
  }

  /**
   * 检查两个矩形是否重叠
   */
  private isOverlapping(
    pos1: ToolbarPosition,
    size1: ToolbarSize,
    bounds2: { x: number; y: number; width: number; height: number }
  ): boolean {
    const rect1Right = pos1.x + size1.width;
    const rect1Bottom = pos1.y + size1.height;
    const rect2Right = bounds2.x + bounds2.width;
    const rect2Bottom = bounds2.y + bounds2.height;

    return !(
      rect1Right <= bounds2.x ||
      pos1.x >= rect2Right ||
      rect1Bottom <= bounds2.y ||
      pos1.y >= rect2Bottom
    );
  }

  /**
   * 计算重叠区域
   */
  private calculateOverlapArea(
    pos1: ToolbarPosition,
    size1: ToolbarSize,
    bounds2: { x: number; y: number; width: number; height: number }
  ): { x: number; y: number; width: number; height: number } {
    const left = Math.max(pos1.x, bounds2.x);
    const top = Math.max(pos1.y, bounds2.y);
    const right = Math.min(pos1.x + size1.width, bounds2.x + bounds2.width);
    const bottom = Math.min(pos1.y + size1.height, bounds2.y + bounds2.height);

    return {
      x: left,
      y: top,
      width: Math.max(0, right - left),
      height: Math.max(0, bottom - top)
    };
  }

  /**
   * 计算位置调整建议
   */
  private calculateAdjustment(
    currentPosition: ToolbarPosition,
    screenshotBounds: ScreenshotBounds,
    collisions: CollisionDetail[]
  ): ToolbarPosition | null {
    if (collisions.length === 0) {
      return null; // 无需调整
    }

    console.log('[COLLISION] 🔧 Calculating position adjustment for', collisions.length, 'collisions');

    // 生成候选调整位置
    const candidates: ToolbarPosition[] = [
      // 向上移动
      { x: currentPosition.x, y: currentPosition.y - 100 },
      // 向下移动
      { x: currentPosition.x, y: currentPosition.y + 100 },
      // 向左移动
      { x: currentPosition.x - 100, y: currentPosition.y },
      // 向右移动
      { x: currentPosition.x + 100, y: currentPosition.y },
      // 对角移动
      { x: currentPosition.x - 50, y: currentPosition.y - 50 },
      { x: currentPosition.x + 50, y: currentPosition.y - 50 },
      { x: currentPosition.x - 50, y: currentPosition.y + 50 },
      { x: currentPosition.x + 50, y: currentPosition.y + 50 }
    ];

    // 找到最佳调整位置（使用简化的碰撞检测避免递归）
    let bestCandidate: ToolbarPosition | null = null;
    let bestScore = -1;

    for (const candidate of candidates) {
      // 检查候选位置是否在屏幕内
      if (!this.isPositionInBounds(candidate)) {
        continue;
      }

      // 使用简化的碰撞检测（避免递归调用detectCollisions）
      const score = this.calculateSimpleCollisionScore(candidate, screenshotBounds);

      if (score > bestScore) {
        bestScore = score;
        bestCandidate = candidate;
      }
    }

    if (bestCandidate) {
      console.log('[COLLISION] ✅ Found adjustment:', bestCandidate, 'score:', bestScore);
    } else {
      console.log('[COLLISION] ❌ No valid adjustment found');
    }

    return bestCandidate;
  }

  /**
   * 简化的碰撞分数计算（避免递归调用）
   */
  private calculateSimpleCollisionScore(
    position: ToolbarPosition,
    screenshotBounds: ScreenshotBounds
  ): number {
    let score = 100; // 基础分数

    // 检查与截图的重叠
    const toolbarRight = position.x + this.toolbarSize.width;
    const toolbarBottom = position.y + this.toolbarSize.height;
    const screenshotRight = screenshotBounds.x + screenshotBounds.width;
    const screenshotBottom = screenshotBounds.y + screenshotBounds.height;

    // 如果与截图重叠，大幅降低分数
    if (!(toolbarRight <= screenshotBounds.x ||
          position.x >= screenshotRight ||
          toolbarBottom <= screenshotBounds.y ||
          position.y >= screenshotBottom)) {
      score -= 50; // 重叠惩罚
    }

    // 检查与屏幕边缘的距离
    const edgeDistance = Math.min(
      position.x,
      position.y,
      this.screenBounds.width - toolbarRight,
      this.screenBounds.height - toolbarBottom
    );

    // 距离边缘太近也会降低分数
    if (edgeDistance < this.edgeThreshold) {
      score -= (this.edgeThreshold - edgeDistance);
    }

    return score;
  }

  /**
   * 检查位置是否在屏幕边界内
   */
  private isPositionInBounds(position: ToolbarPosition): boolean {
    return (
      position.x >= 0 &&
      position.y >= 0 &&
      position.x + this.toolbarSize.width <= this.screenBounds.width &&
      position.y + this.toolbarSize.height <= this.screenBounds.height
    );
  }



  /**
   * 更新屏幕边界
   */
  updateScreenBounds(newBounds: ScreenBounds): void {
    this.screenBounds = newBounds;
  }

  /**
   * 更新工具栏尺寸
   */
  updateToolbarSize(newSize: ToolbarSize): void {
    this.toolbarSize = newSize;
  }
}
