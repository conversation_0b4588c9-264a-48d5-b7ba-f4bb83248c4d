/**
 * 窗口间通信协议
 * 
 * 定义工具栏窗口与预览窗口之间的双向通信机制
 * 基于Tauri事件系统实现实时交互
 */

import { emit, listen, UnlistenFn } from '@tauri-apps/api/event';

// 事件类型定义
export interface ToolbarEvent {
  type: 'tool-selected' | 'action-undo' | 'action-redo' | 'action-save' | 'action-copy' | 'action-close' | 'toolbar-connected' | 'toolbar-disconnected';
  data: any;
  timestamp: number;
  toolbarId: string;
}

export interface PreviewEvent {
  type: 'connection-ack' | 'tool-feedback' | 'annotation-completed' | 'layout-change-request' | 'position-update';
  data: any;
  timestamp: number;
  windowId: string;
}

export interface AnnotationData {
  id: string;
  type: 'text' | 'arrow' | 'rectangle' | 'circle' | 'brush';
  coordinates: {
    x: number;
    y: number;
    width?: number;
    height?: number;
    endX?: number;
    endY?: number;
  };
  style: {
    color: string;
    strokeWidth: number;
    fill?: string;
    fontSize?: number;
  };
  content?: string; // 用于文字标注
}

export interface ToolStatus {
  [toolName: string]: {
    enabled: boolean;
    active: boolean;
    count?: number; // 使用次数
  };
}

/**
 * 工具栏通信管理器
 * 负责工具栏窗口的事件发送和接收
 */
export class ToolbarCommunicator {
  private toolbarId: string;
  private previewWindowId: string | null = null;
  private isConnected: boolean = false;
  private eventListeners: Map<string, UnlistenFn> = new Map();

  constructor(toolbarId: string) {
    this.toolbarId = toolbarId;
  }

  /**
   * 初始化通信连接
   */
  async initialize(): Promise<void> {
    console.log('[TOOLBAR-COMM] 🔧 Initializing toolbar communicator');

    // 监听来自预览窗口的事件
    const unlistenPreview = await listen<PreviewEvent>('preview-event', (event) => {
      this.handlePreviewEvent(event.payload);
    });
    this.eventListeners.set('preview-event', unlistenPreview);

    // 发送连接请求
    await this.sendToolbarEvent('toolbar-connected', {
      toolbarId: this.toolbarId,
      capabilities: ['text', 'arrow', 'rectangle', 'circle', 'brush', 'undo', 'redo', 'save', 'copy'],
      timestamp: Date.now()
    });

    console.log('[TOOLBAR-COMM] ✅ Toolbar communicator initialized');
  }

  /**
   * 发送工具选择事件
   */
  async selectTool(toolName: string): Promise<void> {
    await this.sendToolbarEvent('tool-selected', {
      tool: toolName,
      timestamp: Date.now()
    });
  }

  /**
   * 发送操作事件
   */
  async executeAction(action: string): Promise<void> {
    await this.sendToolbarEvent(`action-${action}`, {
      action: action,
      timestamp: Date.now()
    });
  }

  /**
   * 发送工具栏事件到预览窗口
   */
  private async sendToolbarEvent(type: string, data: any): Promise<void> {
    const event: ToolbarEvent = {
      type: type as any,
      data: data,
      timestamp: Date.now(),
      toolbarId: this.toolbarId
    };

    try {
      await emit('toolbar-event', event);
      console.log('[TOOLBAR-COMM] 📤 Event sent:', type, data);
    } catch (error) {
      console.error('[TOOLBAR-COMM] ❌ Failed to send event:', error);
    }
  }

  /**
   * 处理来自预览窗口的事件
   */
  private handlePreviewEvent(event: PreviewEvent): void {
    console.log('[TOOLBAR-COMM] 📨 Received preview event:', event);

    switch (event.type) {
      case 'connection-ack':
        this.isConnected = true;
        this.previewWindowId = event.data.windowId;
        this.onConnectionEstablished(event.data);
        break;
      case 'tool-feedback':
        this.onToolFeedback(event.data);
        break;
      case 'annotation-completed':
        this.onAnnotationCompleted(event.data);
        break;
      case 'layout-change-request':
        this.onLayoutChangeRequest(event.data);
        break;
    }
  }

  /**
   * 连接建立回调
   */
  private onConnectionEstablished(data: any): void {
    console.log('[TOOLBAR-COMM] ✅ Connection established with preview window:', data.windowId);
    // 可以在这里更新UI状态
  }

  /**
   * 工具反馈回调
   */
  private onToolFeedback(data: ToolStatus): void {
    console.log('[TOOLBAR-COMM] 🔧 Tool feedback received:', data);
    // 更新工具状态UI
  }

  /**
   * 标注完成回调
   */
  private onAnnotationCompleted(data: AnnotationData): void {
    console.log('[TOOLBAR-COMM] ✅ Annotation completed:', data);
    // 可以在这里更新统计信息
  }

  /**
   * 布局改变请求回调
   */
  private onLayoutChangeRequest(data: { layout: string }): void {
    console.log('[TOOLBAR-COMM] 🔄 Layout change requested:', data.layout);
    // 触发布局改变
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await this.sendToolbarEvent('toolbar-disconnected', {
        toolbarId: this.toolbarId
      });
    }

    // 清理事件监听器
    for (const [eventName, unlisten] of this.eventListeners) {
      unlisten();
      console.log('[TOOLBAR-COMM] 🗑️ Removed listener for:', eventName);
    }
    this.eventListeners.clear();

    this.isConnected = false;
    this.previewWindowId = null;
    console.log('[TOOLBAR-COMM] 🔌 Disconnected');
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): { isConnected: boolean; previewWindowId: string | null } {
    return {
      isConnected: this.isConnected,
      previewWindowId: this.previewWindowId
    };
  }
}

/**
 * 预览窗口通信管理器
 * 负责预览窗口的事件发送和接收
 */
export class PreviewCommunicator {
  private windowId: string;
  private connectedToolbars: Set<string> = new Set();
  private eventListeners: Map<string, UnlistenFn> = new Map();
  private annotationHistory: AnnotationData[] = [];

  constructor(windowId: string) {
    this.windowId = windowId;
  }

  /**
   * 初始化通信连接
   */
  async initialize(): Promise<void> {
    console.log('[PREVIEW-COMM] 🔧 Initializing preview communicator');

    // 监听来自工具栏的事件
    const unlistenToolbar = await listen<ToolbarEvent>('toolbar-event', (event) => {
      this.handleToolbarEvent(event.payload);
    });
    this.eventListeners.set('toolbar-event', unlistenToolbar);

    console.log('[PREVIEW-COMM] ✅ Preview communicator initialized');
  }

  /**
   * 处理来自工具栏的事件
   */
  private async handleToolbarEvent(event: ToolbarEvent): Promise<void> {
    console.log('[PREVIEW-COMM] 📨 Received toolbar event:', event);

    switch (event.type) {
      case 'toolbar-connected':
        await this.handleToolbarConnection(event);
        break;
      case 'toolbar-disconnected':
        this.handleToolbarDisconnection(event);
        break;
      case 'tool-selected':
        this.handleToolSelection(event.data.tool);
        break;
      case 'action-undo':
        this.handleUndo();
        break;
      case 'action-redo':
        this.handleRedo();
        break;
      case 'action-save':
        this.handleSave();
        break;
      case 'action-copy':
        this.handleCopy();
        break;
      case 'action-close':
        this.handleClose();
        break;
    }
  }

  /**
   * 处理工具栏连接
   */
  private async handleToolbarConnection(event: ToolbarEvent): Promise<void> {
    this.connectedToolbars.add(event.toolbarId);
    
    // 发送连接确认
    await this.sendPreviewEvent('connection-ack', {
      windowId: this.windowId,
      capabilities: ['annotation', 'undo', 'redo', 'save', 'copy'],
      timestamp: Date.now()
    });

    console.log('[PREVIEW-COMM] ✅ Toolbar connected:', event.toolbarId);
  }

  /**
   * 处理工具栏断开连接
   */
  private handleToolbarDisconnection(event: ToolbarEvent): void {
    this.connectedToolbars.delete(event.toolbarId);
    console.log('[PREVIEW-COMM] 🔌 Toolbar disconnected:', event.toolbarId);
  }

  /**
   * 处理工具选择
   */
  private handleToolSelection(toolName: string): void {
    console.log('[PREVIEW-COMM] 🔧 Tool selected:', toolName);
    // 在这里切换预览窗口的标注工具
    // 这个方法需要与具体的标注系统集成
  }

  /**
   * 处理撤销操作
   */
  private handleUndo(): void {
    console.log('[PREVIEW-COMM] ↶ Undo requested');
    // 实现撤销逻辑
  }

  /**
   * 处理重做操作
   */
  private handleRedo(): void {
    console.log('[PREVIEW-COMM] ↷ Redo requested');
    // 实现重做逻辑
  }

  /**
   * 处理保存操作
   */
  private handleSave(): void {
    console.log('[PREVIEW-COMM] 💾 Save requested');
    // 实现保存逻辑
  }

  /**
   * 处理复制操作
   */
  private handleCopy(): void {
    console.log('[PREVIEW-COMM] 📋 Copy requested');
    // 实现复制逻辑
  }

  /**
   * 处理关闭操作
   */
  private handleClose(): void {
    console.log('[PREVIEW-COMM] ✕ Close requested');
    // 实现关闭逻辑
  }

  /**
   * 发送预览事件到工具栏
   */
  private async sendPreviewEvent(type: string, data: any): Promise<void> {
    const event: PreviewEvent = {
      type: type as any,
      data: data,
      timestamp: Date.now(),
      windowId: this.windowId
    };

    try {
      await emit('preview-event', event);
      console.log('[PREVIEW-COMM] 📤 Event sent:', type, data);
    } catch (error) {
      console.error('[PREVIEW-COMM] ❌ Failed to send event:', error);
    }
  }

  /**
   * 通知标注完成
   */
  async notifyAnnotationCompleted(annotation: AnnotationData): Promise<void> {
    this.annotationHistory.push(annotation);
    await this.sendPreviewEvent('annotation-completed', annotation);
  }

  /**
   * 发送工具反馈
   */
  async sendToolFeedback(toolStatus: ToolStatus): Promise<void> {
    await this.sendPreviewEvent('tool-feedback', toolStatus);
  }

  /**
   * 请求布局改变
   */
  async requestLayoutChange(layout: 'vertical' | 'horizontal'): Promise<void> {
    await this.sendPreviewEvent('layout-change-request', { layout });
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    // 清理事件监听器
    for (const [eventName, unlisten] of this.eventListeners) {
      unlisten();
      console.log('[PREVIEW-COMM] 🗑️ Removed listener for:', eventName);
    }
    this.eventListeners.clear();

    this.connectedToolbars.clear();
    console.log('[PREVIEW-COMM] 🔌 Disconnected');
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): { connectedToolbars: string[]; annotationCount: number } {
    return {
      connectedToolbars: Array.from(this.connectedToolbars),
      annotationCount: this.annotationHistory.length
    };
  }
}
