/**
 * 独立工具栏智能定位算法
 * 
 * 负责计算工具栏相对于预览窗口的最佳显示位置
 * 确保工具栏始终在屏幕可见区域内，不会超出屏幕边界
 */

export interface WindowBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ScreenBounds {
  width: number;
  height: number;
}

export interface ToolbarSize {
  width: number;
  height: number;
}

export interface PositionResult {
  x: number;
  y: number;
  layout: 'vertical' | 'horizontal';
  position: 'right' | 'left' | 'top' | 'bottom';
  score: number;
  reason: string;
}

export interface PositionStrategy {
  name: string;
  position: PositionResult;
  score: number;
  reason: string;
}

export class ToolbarPositioning {
  private screenBounds: ScreenBounds;
  private toolbarSize: ToolbarSize;
  private margin: number = 20; // 与窗口和屏幕边缘的最小距离
  private windowSeparation: number = 20; // 工具栏与预览窗口之间的最小距离

  constructor(screenBounds: ScreenBounds, toolbarSize: ToolbarSize) {
    this.screenBounds = screenBounds;
    this.toolbarSize = toolbarSize;
  }

  /**
   * 计算工具栏的最佳位置
   * @param previewBounds 预览窗口的边界信息
   * @returns 最佳工具栏位置
   */
  calculateOptimalPosition(previewBounds: WindowBounds): PositionResult {
    console.log('[POSITIONING] 🎯 Calculating optimal toolbar position');
    console.log('[POSITIONING] Preview bounds:', previewBounds);
    console.log('[POSITIONING] Screen bounds:', this.screenBounds);
    console.log('[POSITIONING] Toolbar size:', this.toolbarSize);

    // 生成所有可能的位置策略
    const strategies = this.generatePositionStrategies(previewBounds);
    
    // 按评分排序，选择最佳策略
    const bestStrategy = strategies.reduce((best, current) => 
      current.score > best.score ? current : best
    );

    console.log('[POSITIONING] ✅ Best position selected:', bestStrategy);
    return bestStrategy.position;
  }

  /**
   * 生成所有可能的位置策略
   */
  private generatePositionStrategies(previewBounds: WindowBounds): PositionStrategy[] {
    const strategies: PositionStrategy[] = [];

    // 策略1: 预览窗口右侧（垂直布局）
    const rightPosition = this.calculateRightPosition(previewBounds);
    if (this.isPositionValid(rightPosition)) {
      strategies.push({
        name: 'right-vertical',
        position: rightPosition,
        score: this.calculatePositionScore(rightPosition, previewBounds, 'right'),
        reason: '预览窗口右侧 - 最佳用户体验'
      });
    }

    // 策略2: 预览窗口左侧（垂直布局）
    const leftPosition = this.calculateLeftPosition(previewBounds);
    if (this.isPositionValid(leftPosition)) {
      strategies.push({
        name: 'left-vertical',
        position: leftPosition,
        score: this.calculatePositionScore(leftPosition, previewBounds, 'left'),
        reason: '预览窗口左侧 - 避免遮挡'
      });
    }

    // 策略3: 预览窗口上方（水平布局）
    const topPosition = this.calculateTopPosition(previewBounds);
    if (this.isPositionValid(topPosition)) {
      strategies.push({
        name: 'top-horizontal',
        position: topPosition,
        score: this.calculatePositionScore(topPosition, previewBounds, 'top'),
        reason: '预览窗口上方 - 优先显示位置'
      });
    }

    // 策略4: 预览窗口下方（水平布局）
    const bottomPosition = this.calculateBottomPosition(previewBounds);
    if (this.isPositionValid(bottomPosition)) {
      strategies.push({
        name: 'bottom-horizontal',
        position: bottomPosition,
        score: this.calculatePositionScore(bottomPosition, previewBounds, 'bottom'),
        reason: '预览窗口下方 - 备选位置'
      });
    }

    // 策略5: 屏幕顶部居中（后备方案）
    const screenTopPosition = this.calculateScreenTopPosition();
    if (this.isPositionValid(screenTopPosition)) {
      strategies.push({
        name: 'screen-top',
        position: screenTopPosition,
        score: this.calculatePositionScore(screenTopPosition, previewBounds, 'fallback'),
        reason: '屏幕顶部 - 后备位置'
      });
    }

    // 如果没有有效策略，使用强制位置
    if (strategies.length === 0) {
      const forcedPosition = this.calculateForcedPosition();
      strategies.push({
        name: 'forced',
        position: forcedPosition,
        score: 10,
        reason: '强制位置 - 确保可见'
      });
    }

    return strategies;
  }

  /**
   * 计算右侧位置（垂直布局）
   */
  private calculateRightPosition(previewBounds: WindowBounds): PositionResult {
    return {
      x: previewBounds.x + previewBounds.width + this.windowSeparation,
      y: previewBounds.y + (previewBounds.height - this.toolbarSize.height) / 2,
      layout: 'vertical',
      position: 'right',
      score: 0,
      reason: ''
    };
  }

  /**
   * 计算左侧位置（垂直布局）
   */
  private calculateLeftPosition(previewBounds: WindowBounds): PositionResult {
    return {
      x: previewBounds.x - this.toolbarSize.width - this.windowSeparation,
      y: previewBounds.y + (previewBounds.height - this.toolbarSize.height) / 2,
      layout: 'vertical',
      position: 'left',
      score: 0,
      reason: ''
    };
  }

  /**
   * 计算上方位置（水平布局）
   */
  private calculateTopPosition(previewBounds: WindowBounds): PositionResult {
    return {
      x: previewBounds.x + (previewBounds.width - this.toolbarSize.width) / 2,
      y: previewBounds.y - this.toolbarSize.height - this.windowSeparation,
      layout: 'horizontal',
      position: 'top',
      score: 0,
      reason: ''
    };
  }

  /**
   * 计算下方位置（水平布局）
   */
  private calculateBottomPosition(previewBounds: WindowBounds): PositionResult {
    return {
      x: previewBounds.x + (previewBounds.width - this.toolbarSize.width) / 2,
      y: previewBounds.y + previewBounds.height + this.windowSeparation,
      layout: 'horizontal',
      position: 'bottom',
      score: 0,
      reason: ''
    };
  }

  /**
   * 计算屏幕顶部位置（后备方案）
   */
  private calculateScreenTopPosition(): PositionResult {
    return {
      x: (this.screenBounds.width - this.toolbarSize.width) / 2,
      y: this.margin,
      layout: 'horizontal',
      position: 'top',
      score: 0,
      reason: ''
    };
  }

  /**
   * 计算强制位置（确保可见）
   */
  private calculateForcedPosition(): PositionResult {
    return {
      x: this.margin,
      y: this.margin,
      layout: 'vertical',
      position: 'left',
      score: 10,
      reason: '强制位置 - 确保可见'
    };
  }

  /**
   * 检查位置是否有效（不超出屏幕边界）
   */
  private isPositionValid(position: PositionResult): boolean {
    const rightEdge = position.x + this.toolbarSize.width;
    const bottomEdge = position.y + this.toolbarSize.height;

    return (
      position.x >= 0 &&
      position.y >= 0 &&
      rightEdge <= this.screenBounds.width &&
      bottomEdge <= this.screenBounds.height
    );
  }

  /**
   * 计算位置质量评分
   */
  private calculatePositionScore(
    position: PositionResult,
    previewBounds: WindowBounds,
    strategy: string
  ): number {
    let score = 0;

    // 基础分数根据策略类型
    switch (strategy) {
      case 'right':
        score = 90; // 右侧是最佳位置
        break;
      case 'left':
        score = 80; // 左侧次之
        break;
      case 'top':
        score = 85; // 上方优先于下方
        break;
      case 'bottom':
        score = 70; // 下方位置
        break;
      case 'fallback':
        score = 50; // 后备位置
        break;
      default:
        score = 30;
    }

    // 距离屏幕边缘的惩罚
    const edgeDistance = Math.min(
      position.x,
      position.y,
      this.screenBounds.width - (position.x + this.toolbarSize.width),
      this.screenBounds.height - (position.y + this.toolbarSize.height)
    );

    if (edgeDistance < this.margin) {
      score -= (this.margin - edgeDistance) * 2; // 每像素扣2分
    }

    // 与预览窗口的距离奖励
    const distanceToPreview = this.calculateDistanceToPreview(position, previewBounds);
    if (distanceToPreview >= this.margin && distanceToPreview <= this.margin * 2) {
      score += 10; // 适当距离奖励
    }

    // 确保分数在合理范围内
    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算工具栏到预览窗口的距离
   */
  private calculateDistanceToPreview(position: PositionResult, previewBounds: WindowBounds): number {
    const toolbarCenterX = position.x + this.toolbarSize.width / 2;
    const toolbarCenterY = position.y + this.toolbarSize.height / 2;
    const previewCenterX = previewBounds.x + previewBounds.width / 2;
    const previewCenterY = previewBounds.y + previewBounds.height / 2;

    return Math.sqrt(
      Math.pow(toolbarCenterX - previewCenterX, 2) +
      Math.pow(toolbarCenterY - previewCenterY, 2)
    );
  }

  /**
   * 更新屏幕边界信息
   */
  updateScreenBounds(screenBounds: ScreenBounds): void {
    this.screenBounds = screenBounds;
  }

  /**
   * 更新工具栏尺寸信息
   */
  updateToolbarSize(toolbarSize: ToolbarSize): void {
    this.toolbarSize = toolbarSize;
  }

  /**
   * 设置边距
   */
  setMargin(margin: number): void {
    this.margin = margin;
  }

  /**
   * 设置窗口分离距离
   */
  setWindowSeparation(separation: number): void {
    this.windowSeparation = separation;
  }

  /**
   * 验证拖拽位置是否有效
   * @param position 拖拽后的位置
   * @param previewBounds 预览窗口边界
   * @returns 是否为有效位置
   */
  validateDragPosition(position: { x: number; y: number }, previewBounds?: WindowBounds): boolean {
    // 检查屏幕边界
    const rightEdge = position.x + this.toolbarSize.width;
    const bottomEdge = position.y + this.toolbarSize.height;

    const withinScreen = (
      position.x >= 0 &&
      position.y >= 0 &&
      rightEdge <= this.screenBounds.width &&
      bottomEdge <= this.screenBounds.height
    );

    if (!withinScreen) {
      return false;
    }

    // 如果提供了预览窗口边界，检查是否与预览窗口重叠
    if (previewBounds) {
      return !this.isOverlappingWithWindow(position, previewBounds);
    }

    return true;
  }

  /**
   * 调整拖拽位置以确保在屏幕边界内
   * @param position 原始位置
   * @returns 调整后的位置
   */
  constrainToScreenBounds(position: { x: number; y: number }): { x: number; y: number } {
    const maxX = this.screenBounds.width - this.toolbarSize.width;
    const maxY = this.screenBounds.height - this.toolbarSize.height;

    return {
      x: Math.max(0, Math.min(position.x, maxX)),
      y: Math.max(0, Math.min(position.y, maxY))
    };
  }

  /**
   * 检查工具栏是否与预览窗口重叠
   * @param toolbarPos 工具栏位置
   * @param previewBounds 预览窗口边界
   * @returns 是否重叠
   */
  private isOverlappingWithWindow(
    toolbarPos: { x: number; y: number },
    previewBounds: WindowBounds
  ): boolean {
    const toolbarRight = toolbarPos.x + this.toolbarSize.width;
    const toolbarBottom = toolbarPos.y + this.toolbarSize.height;
    const previewRight = previewBounds.x + previewBounds.width;
    const previewBottom = previewBounds.y + previewBounds.height;

    // 检查是否有重叠
    const noOverlap = (
      toolbarRight <= previewBounds.x ||
      toolbarPos.x >= previewRight ||
      toolbarBottom <= previewBounds.y ||
      toolbarPos.y >= previewBottom
    );

    return !noOverlap;
  }

  /**
   * 计算避免与预览窗口重叠的最近位置
   * @param position 当前位置
   * @param previewBounds 预览窗口边界
   * @returns 调整后的位置
   */
  adjustPositionToAvoidWindow(
    position: { x: number; y: number },
    previewBounds: WindowBounds
  ): { x: number; y: number } {
    if (!this.isOverlappingWithWindow(position, previewBounds)) {
      return position;
    }

    // 计算四个方向的可能位置
    const positions = [
      // 右侧
      {
        x: previewBounds.x + previewBounds.width + this.windowSeparation,
        y: position.y
      },
      // 左侧
      {
        x: previewBounds.x - this.toolbarSize.width - this.windowSeparation,
        y: position.y
      },
      // 上方
      {
        x: position.x,
        y: previewBounds.y - this.toolbarSize.height - this.windowSeparation
      },
      // 下方
      {
        x: position.x,
        y: previewBounds.y + previewBounds.height + this.windowSeparation
      }
    ];

    // 找到最近的有效位置
    let bestPosition = position;
    let minDistance = Infinity;

    for (const pos of positions) {
      if (this.validateDragPosition(pos)) {
        const distance = Math.sqrt(
          Math.pow(pos.x - position.x, 2) + Math.pow(pos.y - position.y, 2)
        );
        if (distance < minDistance) {
          minDistance = distance;
          bestPosition = pos;
        }
      }
    }

    return this.constrainToScreenBounds(bestPosition);
  }
}
