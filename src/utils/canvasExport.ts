import { Stage } from 'konva/lib/Stage';
import { CropArea, Shape } from '../types/editor';
import { invoke } from '@tauri-apps/api/core';

/**
 * Export the canvas content as a data URL
 */
export const exportCanvasAsDataURL = (
  stage: Stage,
  cropArea: CropArea | null = null,
  format: 'image/png' | 'image/jpeg' = 'image/png',
  quality: number = 1.0
): string => {
  if (!stage) {
    throw new Error('Stage is required for export');
  }

  let exportOptions: any = {
    mimeType: format,
    quality: quality
  };

  // If crop area is specified, export only that region
  if (cropArea) {
    exportOptions = {
      ...exportOptions,
      x: cropArea.x,
      y: cropArea.y,
      width: cropArea.width,
      height: cropArea.height
    };
  }

  return stage.toDataURL(exportOptions);
};

/**
 * Export canvas with applied crop area
 */
export const exportCroppedCanvas = (
  stage: Stage,
  cropArea: CropArea,
  format: 'image/png' | 'image/jpeg' = 'image/png'
): string => {
  return exportCanvasAsDataURL(stage, cropArea, format);
};

/**
 * Export full canvas content
 */
export const exportFullCanvas = (
  stage: Stage,
  format: 'image/png' | 'image/jpeg' = 'image/png'
): string => {
  return exportCanvasAsDataURL(stage, null, format);
};

/**
 * Get canvas dimensions
 */
export const getCanvasDimensions = (stage: Stage) => {
  return {
    width: stage.width(),
    height: stage.height()
  };
};

/**
 * Validate export parameters
 */
export const validateExportParams = (
  stage: Stage | null,
  cropArea: CropArea | null = null
): { isValid: boolean; error?: string } => {
  if (!stage) {
    return { isValid: false, error: 'Stage is required' };
  }

  if (cropArea) {
    const stageWidth = stage.width();
    const stageHeight = stage.height();

    if (cropArea.x < 0 || cropArea.y < 0) {
      return { isValid: false, error: 'Crop area coordinates must be positive' };
    }

    if (cropArea.x + cropArea.width > stageWidth || cropArea.y + cropArea.height > stageHeight) {
      return { isValid: false, error: 'Crop area exceeds canvas bounds' };
    }

    if (cropArea.width <= 0 || cropArea.height <= 0) {
      return { isValid: false, error: 'Crop area must have positive dimensions' };
    }
  }

  return { isValid: true };
};

/**
 * Export canvas and save to file using Tauri
 */
export const saveCanvasToFile = async (
  stage: Stage,
  filename: string,
  cropArea: CropArea | null = null,
  format: 'png' | 'jpeg' | 'webp' = 'png',
  quality: number = 1.0
): Promise<string> => {
  try {
    console.log('[CANVAS-EXPORT] 💾 Starting canvas export:', { filename, format, quality });

    // 验证参数
    const validation = validateExportParams(stage, cropArea);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // 导出为DataURL
    const mimeType = `image/${format}` as 'image/png' | 'image/jpeg';
    const dataURL = exportCanvasAsDataURL(stage, cropArea, mimeType, quality);

    // 移除data URL前缀，获取base64数据
    const base64Data = dataURL.split(',')[1];

    // 调用Tauri后端保存文件
    const savedPath = await invoke('save_canvas_image', {
      imageData: base64Data,
      filename: filename,
      format: format
    });

    console.log('[CANVAS-EXPORT] ✅ Canvas saved successfully:', savedPath);
    return savedPath as string;

  } catch (error) {
    console.error('[CANVAS-EXPORT] ❌ Failed to save canvas:', error);
    throw error;
  }
};

/**
 * Export canvas to clipboard
 */
export const copyCanvasToClipboard = async (
  stage: Stage,
  cropArea: CropArea | null = null
): Promise<void> => {
  try {
    console.log('[CANVAS-EXPORT] 📋 Copying canvas to clipboard');

    const validation = validateExportParams(stage, cropArea);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    const dataURL = exportCanvasAsDataURL(stage, cropArea, 'image/png');
    const base64Data = dataURL.split(',')[1];

    await invoke('copy_image_to_clipboard', {
      imageData: base64Data
    });

    console.log('[CANVAS-EXPORT] ✅ Canvas copied to clipboard');

  } catch (error) {
    console.error('[CANVAS-EXPORT] ❌ Failed to copy canvas:', error);
    throw error;
  }
};

/**
 * Export annotation metadata as JSON
 */
export const exportAnnotationMetadata = (
  shapes: Shape[],
  cropArea: CropArea | null,
  canvasDimensions: { width: number; height: number }
) => {
  const metadata = {
    version: '1.0',
    timestamp: Date.now(),
    canvasDimensions,
    cropArea,
    shapes: shapes.map(shape => ({
      ...shape,
      // 确保所有必要的属性都被包含
      id: shape.id,
      type: shape.type,
      x: shape.x,
      y: shape.y,
      width: shape.width,
      height: shape.height,
      points: shape.points,
      text: shape.text,
      number: shape.number,
      style: { ...shape.style }
    }))
  };

  return metadata;
};

/**
 * Save annotation project (image + metadata)
 */
export const saveAnnotationProject = async (
  stage: Stage,
  shapes: Shape[],
  cropArea: CropArea | null,
  projectName: string
): Promise<{ imagePath: string; metadataPath: string }> => {
  try {
    console.log('[CANVAS-EXPORT] 💾 Saving annotation project:', projectName);

    // 保存图片
    const imagePath = await saveCanvasToFile(stage, `${projectName}.png`, cropArea);

    // 准备元数据
    const canvasDimensions = getCanvasDimensions(stage);
    const metadata = exportAnnotationMetadata(shapes, cropArea, canvasDimensions);

    // 保存元数据
    const metadataPath = await invoke('save_annotation_metadata', {
      metadata: JSON.stringify(metadata, null, 2),
      filename: `${projectName}.json`
    });

    console.log('[CANVAS-EXPORT] ✅ Annotation project saved:', { imagePath, metadataPath });

    return {
      imagePath: imagePath as string,
      metadataPath: metadataPath as string
    };

  } catch (error) {
    console.error('[CANVAS-EXPORT] ❌ Failed to save annotation project:', error);
    throw error;
  }
};
