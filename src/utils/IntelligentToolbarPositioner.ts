/**
 * 智能工具栏定位系统
 * 
 * 该类负责计算工具栏的最佳位置，确保：
 * 1. 工具栏不会遮挡截图内容
 * 2. 工具栏始终在屏幕可见区域内
 * 3. 提供智能的碰撞检测和位置调整
 */

export interface ScreenBounds {
  width: number;
  height: number;
}

export interface ScreenshotBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ToolbarPosition {
  x: number;
  y: number;
}

export interface ToolbarSize {
  width: number;
  height: number;
}

export interface PositionStrategy {
  name: string;
  position: ToolbarPosition;
  score: number; // 位置质量评分 (0-100)
  reason: string;
}

/**
 * 智能工具栏定位器
 */
export class IntelligentToolbarPositioner {
  private screenBounds: ScreenBounds;
  private toolbarSize: ToolbarSize;
  private margin: number;

  constructor(
    screenBounds: ScreenBounds,
    toolbarSize: ToolbarSize = { width: 400, height: 60 },
    margin: number = 20
  ) {
    this.screenBounds = screenBounds;
    this.toolbarSize = toolbarSize;
    this.margin = margin;
  }

  /**
   * 计算工具栏的最佳位置
   * @param screenshotBounds 截图区域边界
   * @returns 最佳工具栏位置
   */
  calculateOptimalPosition(screenshotBounds: ScreenshotBounds): ToolbarPosition {
    console.log('[POSITIONING] 🎯 Calculating optimal toolbar position');
    console.log('[POSITIONING] Screenshot bounds:', screenshotBounds);
    console.log('[POSITIONING] Screen bounds:', this.screenBounds);
    console.log('[POSITIONING] Toolbar size:', this.toolbarSize);

    // 生成所有可能的位置策略
    const strategies = this.generatePositionStrategies(screenshotBounds);
    
    // 按评分排序，选择最佳策略
    const bestStrategy = strategies.reduce((best, current) => 
      current.score > best.score ? current : best
    );

    console.log('[POSITIONING] ✅ Selected strategy:', bestStrategy.name);
    console.log('[POSITIONING] Position:', bestStrategy.position);
    console.log('[POSITIONING] Score:', bestStrategy.score);
    console.log('[POSITIONING] Reason:', bestStrategy.reason);

    return bestStrategy.position;
  }

  /**
   * 生成所有可能的位置策略
   */
  private generatePositionStrategies(screenshotBounds: ScreenshotBounds): PositionStrategy[] {
    const strategies: PositionStrategy[] = [];

    // 策略1: 截图下方居中
    const belowCenter = this.calculateBelowPosition(screenshotBounds);
    if (this.isPositionValid(belowCenter)) {
      strategies.push({
        name: 'below-center',
        position: belowCenter,
        score: this.calculatePositionScore(belowCenter, screenshotBounds, 'below'),
        reason: '截图下方居中 - 最佳用户体验'
      });
    }

    // 策略2: 截图上方居中
    const aboveCenter = this.calculateAbovePosition(screenshotBounds);
    if (this.isPositionValid(aboveCenter)) {
      strategies.push({
        name: 'above-center',
        position: aboveCenter,
        score: this.calculatePositionScore(aboveCenter, screenshotBounds, 'above'),
        reason: '截图上方居中 - 避免遮挡'
      });
    }

    // 策略3: 截图右侧
    const rightSide = this.calculateRightPosition(screenshotBounds);
    if (this.isPositionValid(rightSide)) {
      strategies.push({
        name: 'right-side',
        position: rightSide,
        score: this.calculatePositionScore(rightSide, screenshotBounds, 'right'),
        reason: '截图右侧 - 侧边定位'
      });
    }

    // 策略4: 截图左侧
    const leftSide = this.calculateLeftPosition(screenshotBounds);
    if (this.isPositionValid(leftSide)) {
      strategies.push({
        name: 'left-side',
        position: leftSide,
        score: this.calculatePositionScore(leftSide, screenshotBounds, 'left'),
        reason: '截图左侧 - 侧边定位'
      });
    }

    // 策略5: 屏幕底部居中（后备方案）
    const screenBottom = this.calculateScreenBottomPosition();
    if (this.isPositionValid(screenBottom)) {
      strategies.push({
        name: 'screen-bottom',
        position: screenBottom,
        score: this.calculatePositionScore(screenBottom, screenshotBounds, 'fallback'),
        reason: '屏幕底部 - 后备位置'
      });
    }

    // 如果没有有效策略，使用强制位置
    if (strategies.length === 0) {
      const forcedPosition = this.calculateForcedPosition();
      strategies.push({
        name: 'forced',
        position: forcedPosition,
        score: 10,
        reason: '强制位置 - 确保可见'
      });
    }

    return strategies;
  }

  /**
   * 计算截图下方位置
   */
  private calculateBelowPosition(screenshotBounds: ScreenshotBounds): ToolbarPosition {
    return {
      x: screenshotBounds.x + (screenshotBounds.width / 2) - (this.toolbarSize.width / 2),
      y: screenshotBounds.y + screenshotBounds.height + this.margin
    };
  }

  /**
   * 计算截图上方位置
   */
  private calculateAbovePosition(screenshotBounds: ScreenshotBounds): ToolbarPosition {
    return {
      x: screenshotBounds.x + (screenshotBounds.width / 2) - (this.toolbarSize.width / 2),
      y: screenshotBounds.y - this.toolbarSize.height - this.margin
    };
  }

  /**
   * 计算截图右侧位置
   */
  private calculateRightPosition(screenshotBounds: ScreenshotBounds): ToolbarPosition {
    return {
      x: screenshotBounds.x + screenshotBounds.width + this.margin,
      y: screenshotBounds.y + (screenshotBounds.height / 2) - (this.toolbarSize.height / 2)
    };
  }

  /**
   * 计算截图左侧位置
   */
  private calculateLeftPosition(screenshotBounds: ScreenshotBounds): ToolbarPosition {
    return {
      x: screenshotBounds.x - this.toolbarSize.width - this.margin,
      y: screenshotBounds.y + (screenshotBounds.height / 2) - (this.toolbarSize.height / 2)
    };
  }

  /**
   * 计算屏幕底部位置
   */
  private calculateScreenBottomPosition(): ToolbarPosition {
    return {
      x: (this.screenBounds.width / 2) - (this.toolbarSize.width / 2),
      y: this.screenBounds.height - this.toolbarSize.height - this.margin
    };
  }

  /**
   * 计算强制位置（确保工具栏可见）
   */
  private calculateForcedPosition(): ToolbarPosition {
    return {
      x: Math.max(this.margin, Math.min(
        (this.screenBounds.width / 2) - (this.toolbarSize.width / 2),
        this.screenBounds.width - this.toolbarSize.width - this.margin
      )),
      y: Math.max(this.margin, Math.min(
        this.screenBounds.height - this.toolbarSize.height - this.margin,
        this.screenBounds.height - this.toolbarSize.height - this.margin
      ))
    };
  }

  /**
   * 检查位置是否有效（在屏幕边界内）
   */
  isPositionValid(position: ToolbarPosition): boolean {
    return (
      position.x >= 0 &&
      position.y >= 0 &&
      position.x + this.toolbarSize.width <= this.screenBounds.width &&
      position.y + this.toolbarSize.height <= this.screenBounds.height
    );
  }

  /**
   * 计算位置质量评分
   */
  private calculatePositionScore(
    position: ToolbarPosition,
    screenshotBounds: ScreenshotBounds,
    strategy: string
  ): number {
    let score = 0;

    // 基础分数根据策略类型
    switch (strategy) {
      case 'below':
        score = 90; // 下方是最佳位置
        break;
      case 'above':
        score = 80; // 上方次之
        break;
      case 'right':
      case 'left':
        score = 70; // 侧边位置
        break;
      case 'fallback':
        score = 50; // 后备位置
        break;
      default:
        score = 30;
    }

    // 距离屏幕边缘的惩罚
    const edgeDistance = Math.min(
      position.x,
      position.y,
      this.screenBounds.width - (position.x + this.toolbarSize.width),
      this.screenBounds.height - (position.y + this.toolbarSize.height)
    );

    if (edgeDistance < this.margin) {
      score -= (this.margin - edgeDistance) * 2; // 每像素扣2分
    }

    // 与截图重叠的惩罚
    if (this.isOverlappingWithScreenshot(position, screenshotBounds)) {
      score -= 30; // 重叠严重扣分
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 检查工具栏是否与截图重叠
   */
  private isOverlappingWithScreenshot(
    toolbarPosition: ToolbarPosition,
    screenshotBounds: ScreenshotBounds
  ): boolean {
    const toolbarRight = toolbarPosition.x + this.toolbarSize.width;
    const toolbarBottom = toolbarPosition.y + this.toolbarSize.height;
    const screenshotRight = screenshotBounds.x + screenshotBounds.width;
    const screenshotBottom = screenshotBounds.y + screenshotBounds.height;

    return !(
      toolbarRight < screenshotBounds.x ||
      toolbarPosition.x > screenshotRight ||
      toolbarBottom < screenshotBounds.y ||
      toolbarPosition.y > screenshotBottom
    );
  }

  /**
   * 更新屏幕边界
   */
  updateScreenBounds(newBounds: ScreenBounds): void {
    this.screenBounds = newBounds;
    console.log('[POSITIONING] 📐 Screen bounds updated:', newBounds);
  }

  /**
   * 更新工具栏尺寸
   */
  updateToolbarSize(newSize: ToolbarSize): void {
    this.toolbarSize = newSize;
    console.log('[POSITIONING] 📏 Toolbar size updated:', newSize);
  }
}
