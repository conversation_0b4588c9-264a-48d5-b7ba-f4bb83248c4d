/**
 * 撤销重做管理器
 * 
 * 负责管理标注操作的撤销和重做功能
 * 支持跨窗口的状态同步
 */

export interface AnnotationAction {
  id: string;
  type: 'add' | 'remove' | 'modify';
  annotationType: 'text' | 'arrow' | 'rectangle' | 'circle' | 'brush';
  timestamp: number;
  data: any; // 标注的具体数据
  previousData?: any; // 修改操作的前一个状态
}

export interface UndoRedoState {
  canUndo: boolean;
  canRedo: boolean;
  historyLength: number;
  redoStackLength: number;
  currentPosition: number;
}

export class UndoRedoManager {
  private history: AnnotationAction[] = [];
  private redoStack: AnnotationAction[] = [];
  private maxHistorySize: number = 100;
  private onStateChange?: (state: UndoRedoState) => void;

  constructor(maxHistorySize: number = 100) {
    this.maxHistorySize = maxHistorySize;
  }

  /**
   * 设置状态变化回调
   */
  setOnStateChange(callback: (state: UndoRedoState) => void): void {
    this.onStateChange = callback;
    this.notifyStateChange();
  }

  /**
   * 添加一个新的标注操作到历史记录
   */
  addAction(action: AnnotationAction): void {
    console.log('[UNDO-REDO] 📝 Adding action to history:', action);

    // 添加到历史记录
    this.history.push(action);

    // 清空重做栈（因为有新操作）
    this.redoStack = [];

    // 限制历史记录大小
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }

    this.notifyStateChange();
    console.log('[UNDO-REDO] ✅ Action added, history length:', this.history.length);
  }

  /**
   * 执行撤销操作
   */
  undo(): AnnotationAction | null {
    if (!this.canUndo()) {
      console.log('[UNDO-REDO] ⚠️ Cannot undo - no actions in history');
      return null;
    }

    const action = this.history.pop()!;
    this.redoStack.push(action);

    console.log('[UNDO-REDO] ↶ Undo action:', action);
    this.notifyStateChange();

    return action;
  }

  /**
   * 执行重做操作
   */
  redo(): AnnotationAction | null {
    if (!this.canRedo()) {
      console.log('[UNDO-REDO] ⚠️ Cannot redo - no actions in redo stack');
      return null;
    }

    const action = this.redoStack.pop()!;
    this.history.push(action);

    console.log('[UNDO-REDO] ↷ Redo action:', action);
    this.notifyStateChange();

    return action;
  }

  /**
   * 检查是否可以撤销
   */
  canUndo(): boolean {
    return this.history.length > 0;
  }

  /**
   * 检查是否可以重做
   */
  canRedo(): boolean {
    return this.redoStack.length > 0;
  }

  /**
   * 获取当前状态
   */
  getState(): UndoRedoState {
    return {
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      historyLength: this.history.length,
      redoStackLength: this.redoStack.length,
      currentPosition: this.history.length
    };
  }

  /**
   * 清空所有历史记录
   */
  clear(): void {
    console.log('[UNDO-REDO] 🗑️ Clearing all history');
    this.history = [];
    this.redoStack = [];
    this.notifyStateChange();
  }

  /**
   * 获取历史记录的副本
   */
  getHistory(): AnnotationAction[] {
    return [...this.history];
  }

  /**
   * 获取重做栈的副本
   */
  getRedoStack(): AnnotationAction[] {
    return [...this.redoStack];
  }

  /**
   * 从状态恢复历史记录（用于跨窗口同步）
   */
  restoreFromState(history: AnnotationAction[], redoStack: AnnotationAction[]): void {
    console.log('[UNDO-REDO] 🔄 Restoring from state');
    this.history = [...history];
    this.redoStack = [...redoStack];
    this.notifyStateChange();
  }

  /**
   * 获取最后一个操作
   */
  getLastAction(): AnnotationAction | null {
    return this.history.length > 0 ? this.history[this.history.length - 1] : null;
  }

  /**
   * 查找特定ID的操作
   */
  findActionById(id: string): AnnotationAction | null {
    return this.history.find(action => action.id === id) || 
           this.redoStack.find(action => action.id === id) || 
           null;
  }

  /**
   * 移除特定ID的操作（用于清理）
   */
  removeActionById(id: string): boolean {
    const historyIndex = this.history.findIndex(action => action.id === id);
    if (historyIndex !== -1) {
      this.history.splice(historyIndex, 1);
      this.notifyStateChange();
      return true;
    }

    const redoIndex = this.redoStack.findIndex(action => action.id === id);
    if (redoIndex !== -1) {
      this.redoStack.splice(redoIndex, 1);
      this.notifyStateChange();
      return true;
    }

    return false;
  }

  /**
   * 获取统计信息
   */
  getStatistics(): {
    totalActions: number;
    actionsByType: Record<string, number>;
    actionsByAnnotationType: Record<string, number>;
  } {
    const allActions = [...this.history, ...this.redoStack];
    
    const actionsByType: Record<string, number> = {};
    const actionsByAnnotationType: Record<string, number> = {};

    allActions.forEach(action => {
      actionsByType[action.type] = (actionsByType[action.type] || 0) + 1;
      actionsByAnnotationType[action.annotationType] = (actionsByAnnotationType[action.annotationType] || 0) + 1;
    });

    return {
      totalActions: allActions.length,
      actionsByType,
      actionsByAnnotationType
    };
  }

  /**
   * 通知状态变化
   */
  private notifyStateChange(): void {
    if (this.onStateChange) {
      this.onStateChange(this.getState());
    }
  }
}

/**
 * 跨窗口撤销重做同步器
 * 
 * 负责在工具栏窗口和预览窗口之间同步撤销重做状态
 */
export class CrossWindowUndoRedoSync {
  private undoRedoManager: UndoRedoManager;
  private onSyncRequired?: (state: UndoRedoState, history: AnnotationAction[], redoStack: AnnotationAction[]) => void;

  constructor(undoRedoManager: UndoRedoManager, _windowId: string) {
    this.undoRedoManager = undoRedoManager;

    // 监听状态变化并触发同步
    this.undoRedoManager.setOnStateChange((state) => {
      this.triggerSync(state);
    });
  }

  /**
   * 设置同步回调
   */
  setOnSyncRequired(callback: (state: UndoRedoState, history: AnnotationAction[], redoStack: AnnotationAction[]) => void): void {
    this.onSyncRequired = callback;
  }

  /**
   * 触发同步
   */
  private triggerSync(state: UndoRedoState): void {
    if (this.onSyncRequired) {
      const history = this.undoRedoManager.getHistory();
      const redoStack = this.undoRedoManager.getRedoStack();
      this.onSyncRequired(state, history, redoStack);
    }
  }

  /**
   * 接收来自其他窗口的同步数据
   */
  receiveSync(history: AnnotationAction[], redoStack: AnnotationAction[]): void {
    console.log('[SYNC] 📨 Receiving sync data from other window');
    this.undoRedoManager.restoreFromState(history, redoStack);
  }

  /**
   * 发送同步数据到其他窗口
   */
  sendSync(): { state: UndoRedoState; history: AnnotationAction[]; redoStack: AnnotationAction[] } {
    console.log('[SYNC] 📤 Sending sync data to other window');
    return {
      state: this.undoRedoManager.getState(),
      history: this.undoRedoManager.getHistory(),
      redoStack: this.undoRedoManager.getRedoStack()
    };
  }
}

/**
 * 创建标注操作的工厂函数
 */
export class AnnotationActionFactory {
  /**
   * 创建添加标注的操作
   */
  static createAddAction(annotationType: 'text' | 'arrow' | 'rectangle' | 'circle' | 'brush', data: any): AnnotationAction {
    return {
      id: `add_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'add',
      annotationType,
      timestamp: Date.now(),
      data
    };
  }

  /**
   * 创建移除标注的操作
   */
  static createRemoveAction(annotationType: 'text' | 'arrow' | 'rectangle' | 'circle' | 'brush', data: any): AnnotationAction {
    return {
      id: `remove_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'remove',
      annotationType,
      timestamp: Date.now(),
      data
    };
  }

  /**
   * 创建修改标注的操作
   */
  static createModifyAction(
    annotationType: 'text' | 'arrow' | 'rectangle' | 'circle' | 'brush', 
    newData: any, 
    previousData: any
  ): AnnotationAction {
    return {
      id: `modify_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'modify',
      annotationType,
      timestamp: Date.now(),
      data: newData,
      previousData
    };
  }
}
