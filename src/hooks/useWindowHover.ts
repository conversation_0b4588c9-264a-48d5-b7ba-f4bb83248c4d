import { useState, useCallback, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

export interface WindowInfo {
  id: number; // Changed to number to match <PERSON>ust's u64 (JavaScript numbers can represent u64 up to 2^53)
  title: string;
  x: number;
  y: number;
  width: number;
  height: number;
  is_visible: boolean;
}

export interface WindowHoverState {
  isHoverMode: boolean;
  selectedWindow: WindowInfo | null;
  hoveredWindow: WindowInfo | null;
  mousePosition: { x: number; y: number };
}

export const useWindowHover = () => {
  const [state, setState] = useState<WindowHoverState>({
    isHoverMode: false,
    selectedWindow: null,
    hoveredWindow: null,
    mousePosition: { x: 0, y: 0 },
  });

  const startHoverMode = useCallback(() => {
    setState(prev => ({ ...prev, isHoverMode: true }));
  }, []);

  const stopHoverMode = useCallback(() => {
    setState({
      isHoverMode: false,
      selectedWindow: null,
      hoveredWindow: null,
      mousePosition: { x: 0, y: 0 },
    });
  }, []);

  const selectWindow = useCallback((window: WindowInfo) => {
    setState(prev => ({ ...prev, selectedWindow: window }));
  }, []);

  const cancelSelection = useCallback(() => {
    setState(prev => ({ ...prev, selectedWindow: null }));
  }, []);

  const updateMousePosition = useCallback(async (x: number, y: number) => {
    setState(prev => ({ ...prev, mousePosition: { x, y } }));

    if (state.isHoverMode) {
      try {
        const windowInfo = await invoke<WindowInfo | null>('modules::window::detect_window_under_mouse', { x, y });
        setState(prev => ({ ...prev, hoveredWindow: windowInfo }));
      } catch (error) {
        console.error('Failed to get window under cursor:', error);
        setState(prev => ({ ...prev, hoveredWindow: null }));
      }
    }
  }, [state.isHoverMode]);

  const captureHoveredWindow = useCallback(async (savePath?: string) => {
    if (!state.hoveredWindow) {
      throw new Error('No window is currently hovered');
    }

    try {
      const result = await invoke('capture_window_by_id', {
        windowId: state.hoveredWindow.id,
        savePath: savePath || null,
      });

      stopHoverMode();
      return result;
    } catch (error) {
      console.error('Failed to capture hovered window:', error);
      throw error;
    }
  }, [state.hoveredWindow, stopHoverMode]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopHoverMode();
    };
  }, [stopHoverMode]);

  return {
    ...state,
    startHoverMode,
    stopHoverMode,
    selectWindow,
    cancelSelection,
    updateMousePosition,
    captureHoveredWindow,
  };
};
