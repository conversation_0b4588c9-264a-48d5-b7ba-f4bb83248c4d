/**
 * Z-Index管理系统Hook
 * 
 * 管理应用中不同层级的Z-Index，确保工具栏始终在正确的层级显示
 */

import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * 应用层级定义
 * 数值越大，层级越高
 */
export const Z_INDEX_LAYERS = {
  // 基础层级
  BACKGROUND: 0,
  CONTENT: 100,
  
  // 覆盖层级
  OVERLAY_BACKGROUND: 9999,
  SCREENSHOT_DISPLAY: 10000,
  ANNOTATION_LAYER: 10001,
  
  // 工具栏层级
  TOOLBAR: 10002,
  TOOLBAR_DRAGGING: 10003,
  TOOLBAR_MENU: 10004,
  
  // 模态层级
  MODAL_BACKDROP: 10005,
  MODAL_CONTENT: 10006,
  
  // 最高层级
  TOOLTIP: 10007,
  NOTIFICATION: 10008,
  DEBUG_OVERLAY: 10009
} as const;

export type ZIndexLayer = typeof Z_INDEX_LAYERS[keyof typeof Z_INDEX_LAYERS];

export interface ZIndexState {
  currentLayer: ZIndexLayer;
  isElevated: boolean;
  previousLayer?: ZIndexLayer;
}

export interface ZIndexOptions {
  defaultLayer?: ZIndexLayer;
  autoRestore?: boolean;
  restoreDelay?: number;
}

/**
 * Z-Index管理Hook
 */
export const useZIndexManager = (options: ZIndexOptions = {}) => {
  const {
    defaultLayer = Z_INDEX_LAYERS.TOOLBAR,
    autoRestore = true,
    restoreDelay = 200
  } = options;

  const [zIndexState, setZIndexState] = useState<ZIndexState>({
    currentLayer: defaultLayer,
    isElevated: false
  });

  const restoreTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * 提升到最高层级
   */
  const elevateToTop = useCallback((targetLayer?: ZIndexLayer) => {
    const newLayer = targetLayer || Z_INDEX_LAYERS.TOOLBAR_DRAGGING;
    
    setZIndexState(prev => ({
      currentLayer: newLayer,
      isElevated: true,
      previousLayer: prev.currentLayer
    }));
    
    console.log('[Z_INDEX] 🔝 Elevated to layer:', newLayer);
    
    // 清除之前的恢复定时器
    if (restoreTimeoutRef.current) {
      clearTimeout(restoreTimeoutRef.current);
      restoreTimeoutRef.current = null;
    }
  }, []);

  /**
   * 恢复到正常层级
   */
  const restoreNormalLevel = useCallback((delay?: number) => {
    const actualDelay = delay !== undefined ? delay : restoreDelay;
    
    const restore = () => {
      setZIndexState(prev => ({
        currentLayer: prev.previousLayer || defaultLayer,
        isElevated: false,
        previousLayer: undefined
      }));
      
      console.log('[Z_INDEX] 📉 Restored to normal level');
    };
    
    if (actualDelay > 0) {
      restoreTimeoutRef.current = setTimeout(restore, actualDelay);
    } else {
      restore();
    }
  }, [defaultLayer, restoreDelay]);

  /**
   * 设置特定层级
   */
  const setLayer = useCallback((layer: ZIndexLayer) => {
    setZIndexState(prev => ({
      currentLayer: layer,
      isElevated: layer > defaultLayer,
      previousLayer: prev.currentLayer
    }));
    
    console.log('[Z_INDEX] 🎯 Set to layer:', layer);
  }, [defaultLayer]);

  /**
   * 临时提升层级
   */
  const temporaryElevate = useCallback((
    targetLayer: ZIndexLayer,
    duration: number = 1000
  ) => {
    elevateToTop(targetLayer);
    
    setTimeout(() => {
      restoreNormalLevel(0);
    }, duration);
    
    console.log('[Z_INDEX] ⏱️ Temporary elevation for', duration, 'ms');
  }, [elevateToTop, restoreNormalLevel]);

  /**
   * 获取相对层级
   */
  const getRelativeLayer = useCallback((offset: number): ZIndexLayer => {
    return (zIndexState.currentLayer + offset) as ZIndexLayer;
  }, [zIndexState.currentLayer]);

  /**
   * 检查是否在指定层级之上
   */
  const isAboveLayer = useCallback((compareLayer: ZIndexLayer): boolean => {
    return zIndexState.currentLayer > compareLayer;
  }, [zIndexState.currentLayer]);

  /**
   * 检查是否在指定层级之下
   */
  const isBelowLayer = useCallback((compareLayer: ZIndexLayer): boolean => {
    return zIndexState.currentLayer < compareLayer;
  }, [zIndexState.currentLayer]);

  /**
   * 获取层级名称（用于调试）
   */
  const getLayerName = useCallback((layer?: ZIndexLayer): string => {
    const targetLayer = layer || zIndexState.currentLayer;
    
    for (const [name, value] of Object.entries(Z_INDEX_LAYERS)) {
      if (value === targetLayer) {
        return name;
      }
    }
    
    return `CUSTOM_${targetLayer}`;
  }, [zIndexState.currentLayer]);

  /**
   * 重置到默认层级
   */
  const reset = useCallback(() => {
    if (restoreTimeoutRef.current) {
      clearTimeout(restoreTimeoutRef.current);
      restoreTimeoutRef.current = null;
    }
    
    setZIndexState({
      currentLayer: defaultLayer,
      isElevated: false
    });
    
    console.log('[Z_INDEX] 🔄 Reset to default layer:', defaultLayer);
  }, [defaultLayer]);

  // 自动恢复逻辑
  useEffect(() => {
    if (autoRestore && zIndexState.isElevated && !restoreTimeoutRef.current) {
      // 如果启用自动恢复且当前处于提升状态，设置恢复定时器
      restoreTimeoutRef.current = setTimeout(() => {
        restoreNormalLevel(0);
      }, restoreDelay * 2); // 双倍延迟作为安全边际
    }
  }, [autoRestore, zIndexState.isElevated, restoreNormalLevel, restoreDelay]);

  // 清理效果
  useEffect(() => {
    return () => {
      if (restoreTimeoutRef.current) {
        clearTimeout(restoreTimeoutRef.current);
      }
    };
  }, []);

  // 调试信息
  useEffect(() => {
    console.log('[Z_INDEX] 📊 State changed:', {
      layer: zIndexState.currentLayer,
      layerName: getLayerName(),
      isElevated: zIndexState.isElevated,
      previousLayer: zIndexState.previousLayer
    });
  }, [zIndexState, getLayerName]);

  return {
    // 当前状态
    zIndex: zIndexState.currentLayer,
    isElevated: zIndexState.isElevated,
    layerName: getLayerName(),
    
    // 层级控制
    elevateToTop,
    restoreNormalLevel,
    setLayer,
    temporaryElevate,
    reset,
    
    // 查询方法
    getRelativeLayer,
    isAboveLayer,
    isBelowLayer,
    getLayerName,
    
    // 常量
    layers: Z_INDEX_LAYERS
  };
};

/**
 * 全局Z-Index管理器
 * 用于协调多个组件的层级
 */
class GlobalZIndexManager {
  private static instance: GlobalZIndexManager;
  private activeElements: Map<string, ZIndexLayer> = new Map();
  
  static getInstance(): GlobalZIndexManager {
    if (!GlobalZIndexManager.instance) {
      GlobalZIndexManager.instance = new GlobalZIndexManager();
    }
    return GlobalZIndexManager.instance;
  }
  
  /**
   * 注册元素
   */
  register(id: string, layer: ZIndexLayer): void {
    this.activeElements.set(id, layer);
    console.log('[GLOBAL_Z_INDEX] 📝 Registered element:', id, 'at layer:', layer);
  }
  
  /**
   * 注销元素
   */
  unregister(id: string): void {
    this.activeElements.delete(id);
    console.log('[GLOBAL_Z_INDEX] 🗑️ Unregistered element:', id);
  }
  
  /**
   * 获取最高层级
   */
  getTopLayer(): ZIndexLayer {
    if (this.activeElements.size === 0) {
      return Z_INDEX_LAYERS.TOOLBAR;
    }
    
    return Math.max(...this.activeElements.values()) as ZIndexLayer;
  }
  
  /**
   * 获取下一个可用层级
   */
  getNextAvailableLayer(): ZIndexLayer {
    return (this.getTopLayer() + 1) as ZIndexLayer;
  }
  
  /**
   * 获取所有活动元素
   */
  getActiveElements(): Map<string, ZIndexLayer> {
    return new Map(this.activeElements);
  }
}

export const globalZIndexManager = GlobalZIndexManager.getInstance();
