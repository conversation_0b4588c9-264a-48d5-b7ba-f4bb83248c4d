import { useEffect } from 'react';
import { useEditorStore } from '../store/editorStore';


interface KeyboardShortcutsConfig {
  onSave?: () => void;
  onCopy?: () => void;
  onCancel?: () => void;
  onDelete?: () => void;
}

export const useKeyboardShortcuts = (config: KeyboardShortcutsConfig = {}) => {
  const {
    currentTool,
    setCurrentTool,
    undo,
    redo,
    selectedShapeId,
    deleteShape
  } = useEditorStore();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 防止在输入框中触发快捷键
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return;
      }

      const { key, ctrlKey, metaKey, shiftKey, altKey } = event;
      const isModifierPressed = ctrlKey || metaKey;

      console.log('[SHORTCUTS] Key pressed:', { key, ctrlKey, metaKey, shiftKey, altKey });

      // 阻止默认行为的快捷键
      const shouldPreventDefault = [
        'z', 'y', 's', 'c', 'v', 'a', 'd',
        'Escape', 'Delete', 'Backspace'
      ].includes(key.toLowerCase()) && (isModifierPressed || ['Escape', 'Delete', 'Backspace'].includes(key));

      if (shouldPreventDefault) {
        event.preventDefault();
      }

      // 处理快捷键
      if (isModifierPressed) {
        switch (key.toLowerCase()) {
          case 'z':
            if (shiftKey) {
              redo();
              console.log('[SHORTCUTS] ↷ Redo executed');
            } else {
              undo();
              console.log('[SHORTCUTS] ↶ Undo executed');
            }
            break;

          case 'y':
            redo();
            console.log('[SHORTCUTS] ↷ Redo executed');
            break;

          case 's':
            config.onSave?.();
            console.log('[SHORTCUTS] 💾 Save triggered');
            break;

          case 'c':
            config.onCopy?.();
            console.log('[SHORTCUTS] 📋 Copy triggered');
            break;

          case 'a':
            // 全选功能 - 暂时不实现
            console.log('[SHORTCUTS] 🎯 Select all (not implemented)');
            break;

          case 'd':
            // 取消选择
            if (selectedShapeId) {
              const { setSelectedShapeId } = useEditorStore.getState();
              setSelectedShapeId(null);
              console.log('[SHORTCUTS] ❌ Deselect shape');
            }
            break;
        }
      } else {
        // 非修饰键快捷键
        switch (key) {
          case 'Escape':
            if (selectedShapeId) {
              const { setSelectedShapeId } = useEditorStore.getState();
              setSelectedShapeId(null);
              console.log('[SHORTCUTS] ❌ Escape - deselect shape');
            } else {
              config.onCancel?.();
              console.log('[SHORTCUTS] ❌ Escape - cancel operation');
            }
            break;

          case 'Delete':
          case 'Backspace':
            if (selectedShapeId) {
              deleteShape(selectedShapeId);
              console.log('[SHORTCUTS] 🗑️ Delete shape:', selectedShapeId);
            } else {
              config.onDelete?.();
            }
            break;

          // 工具快捷键
          case 'v':
          case 'V':
            setCurrentTool('select');
            console.log('[SHORTCUTS] 🎯 Select tool');
            break;

          case 'r':
          case 'R':
            setCurrentTool('rectangle');
            console.log('[SHORTCUTS] ⬜ Rectangle tool');
            break;

          case 'o':
          case 'O':
            setCurrentTool('ellipse');
            console.log('[SHORTCUTS] ⭕ Ellipse tool');
            break;

          case 'l':
          case 'L':
            setCurrentTool('line');
            console.log('[SHORTCUTS] ➖ Line tool');
            break;

          case 'a':
          case 'A':
            if (!isModifierPressed) {
              setCurrentTool('arrow');
              console.log('[SHORTCUTS] ↗️ Arrow tool');
            }
            break;

          case 't':
          case 'T':
            setCurrentTool('text');
            console.log('[SHORTCUTS] 📝 Text tool');
            break;

          case 'p':
          case 'P':
            setCurrentTool('pen');
            console.log('[SHORTCUTS] ✏️ Pen tool');
            break;

          case 'e':
          case 'E':
            setCurrentTool('eraser');
            console.log('[SHORTCUTS] 🧽 Eraser tool');
            break;

          case 'c':
          case 'C':
            if (!isModifierPressed) {
              setCurrentTool('crop');
              console.log('[SHORTCUTS] ✂️ Crop tool');
            }
            break;

          case 'm':
          case 'M':
            setCurrentTool('mosaic');
            console.log('[SHORTCUTS] 🔲 Mosaic tool');
            break;

          case 'n':
          case 'N':
            setCurrentTool('number');
            console.log('[SHORTCUTS] ① Number tool');
            break;
        }
      }
    };

    // 添加事件监听器
    document.addEventListener('keydown', handleKeyDown);

    console.log('[SHORTCUTS] ⌨️ Keyboard shortcuts initialized');

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      console.log('[SHORTCUTS] 🧹 Keyboard shortcuts cleaned up');
    };
  }, [
    currentTool,
    setCurrentTool,
    undo,
    redo,
    selectedShapeId,
    deleteShape,
    config.onSave,
    config.onCopy,
    config.onCancel,
    config.onDelete
  ]);

  // 返回当前工具和快捷键映射
  return {
    currentTool,
    shortcuts: {
      'Ctrl+Z / Cmd+Z': '撤销',
      'Ctrl+Shift+Z / Cmd+Shift+Z': '重做',
      'Ctrl+Y / Cmd+Y': '重做',
      'Ctrl+S / Cmd+S': '保存',
      'Ctrl+C / Cmd+C': '复制',
      'Escape': '取消选择/退出',
      'Delete / Backspace': '删除选中元素',
      'V': '选择工具',
      'R': '矩形工具',
      'O': '椭圆工具',
      'L': '直线工具',
      'A': '箭头工具',
      'T': '文字工具',
      'P': '画笔工具',
      'E': '橡皮擦工具',
      'C': '裁剪工具',
      'M': '马赛克工具',
      'N': '序号工具'
    }
  };
};
