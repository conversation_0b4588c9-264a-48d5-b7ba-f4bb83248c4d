import { useState, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';

export interface RegionSelectionArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface RegionSelectionState {
  isSelecting: boolean;
  backgroundImage: string | null;
  selectedArea: RegionSelectionArea | null;
}

export const useRegionSelection = () => {
  const [state, setState] = useState<RegionSelectionState>({
    isSelecting: false,
    backgroundImage: null,
    selectedArea: null,
  });

  const startRegionSelection = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isSelecting: true }));
      
      // Get background screenshot for selection overlay
      const backgroundImage = await invoke<string>('start_region_selection');
      
      setState(prev => ({
        ...prev,
        backgroundImage,
        selectedArea: null,
      }));
    } catch (error) {
      console.error('Failed to start region selection:', error);
      setState(prev => ({ ...prev, isSelecting: false }));
      throw error;
    }
  }, []);

  const completeRegionSelection = useCallback(async (area: RegionSelectionArea, savePath?: string) => {
    try {
      const result = await invoke('complete_region_selection', {
        area,
        savePath: savePath || null,
      });
      
      setState({
        isSelecting: false,
        backgroundImage: null,
        selectedArea: null,
      });
      
      return result;
    } catch (error) {
      console.error('Failed to complete region selection:', error);
      throw error;
    }
  }, []);

  const cancelRegionSelection = useCallback(() => {
    setState({
      isSelecting: false,
      backgroundImage: null,
      selectedArea: null,
    });
  }, []);

  const setSelectedArea = useCallback((area: RegionSelectionArea | null) => {
    setState(prev => ({ ...prev, selectedArea: area }));
  }, []);

  return {
    ...state,
    startRegionSelection,
    completeRegionSelection,
    cancelRegionSelection,
    setSelectedArea,
  };
};
