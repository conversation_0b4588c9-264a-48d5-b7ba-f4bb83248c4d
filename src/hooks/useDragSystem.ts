/**
 * 增强的拖拽系统Hook
 * 
 * 提供完整的拖拽功能，包括：
 * - 鼠标和触摸支持
 * - 边界约束
 * - 平滑动画
 * - 碰撞检测
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { ToolbarPosition } from '../types/toolbar';

export interface DragState {
  isDragging: boolean;
  startPosition: { x: number; y: number };
  offset: { x: number; y: number };
  constrainToBounds: boolean;
  dragDistance: number;
}

export interface DragConstraints {
  minX?: number;
  minY?: number;
  maxX?: number;
  maxY?: number;
  snapToGrid?: boolean;
  gridSize?: number;
}

export interface DragOptions {
  constraints?: DragConstraints;
  enableTouch?: boolean;
  enableAnimation?: boolean;
  onDragStart?: (position: ToolbarPosition) => void;
  onDragMove?: (position: ToolbarPosition) => void;
  onDragEnd?: (position: ToolbarPosition) => void;
  onPositionChange?: (position: ToolbarPosition) => void;
}

/**
 * 增强的拖拽系统Hook
 */
export const useDragSystem = (
  elementRef: React.RefObject<HTMLElement>,
  options: DragOptions = {}
) => {
  const {
    constraints = {},
    enableTouch = true,
    enableAnimation = true,
    onDragStart,
    onDragMove,
    onDragEnd,
    onPositionChange
  } = options;

  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    startPosition: { x: 0, y: 0 },
    offset: { x: 0, y: 0 },
    constrainToBounds: true,
    dragDistance: 0
  });

  // 动画相关
  const animationFrameRef = useRef<number | null>(null);
  const lastPositionRef = useRef<ToolbarPosition>({ x: 0, y: 0 });

  /**
   * 应用约束条件到位置
   */
  const applyConstraints = useCallback((position: ToolbarPosition): ToolbarPosition => {
    let { x, y } = position;
    const element = elementRef.current;
    
    if (!element) return position;
    
    const rect = element.getBoundingClientRect();
    
    // 应用自定义约束
    if (constraints.minX !== undefined) x = Math.max(x, constraints.minX);
    if (constraints.minY !== undefined) y = Math.max(y, constraints.minY);
    if (constraints.maxX !== undefined) x = Math.min(x, constraints.maxX);
    if (constraints.maxY !== undefined) y = Math.min(y, constraints.maxY);
    
    // 默认屏幕边界约束
    if (dragState.constrainToBounds) {
      x = Math.max(0, Math.min(x, window.innerWidth - rect.width));
      y = Math.max(0, Math.min(y, window.innerHeight - rect.height));
    }
    
    // 网格对齐
    if (constraints.snapToGrid && constraints.gridSize) {
      x = Math.round(x / constraints.gridSize) * constraints.gridSize;
      y = Math.round(y / constraints.gridSize) * constraints.gridSize;
    }
    
    return { x, y };
  }, [constraints, dragState.constrainToBounds, elementRef]);

  /**
   * 获取事件坐标（支持鼠标和触摸）
   */
  const getEventCoordinates = useCallback((event: MouseEvent | TouchEvent) => {
    if ('touches' in event && event.touches.length > 0) {
      return {
        clientX: event.touches[0].clientX,
        clientY: event.touches[0].clientY
      };
    } else if ('clientX' in event) {
      return {
        clientX: event.clientX,
        clientY: event.clientY
      };
    }
    return { clientX: 0, clientY: 0 };
  }, []);

  /**
   * 开始拖拽
   */
  const handleDragStart = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    event.preventDefault();
    
    const element = elementRef.current;
    if (!element) return;
    
    const rect = element.getBoundingClientRect();
    const { clientX, clientY } = getEventCoordinates(event.nativeEvent);
    
    const newDragState: DragState = {
      isDragging: true,
      startPosition: { x: clientX, y: clientY },
      offset: {
        x: clientX - rect.left,
        y: clientY - rect.top
      },
      constrainToBounds: true,
      dragDistance: 0
    };
    
    setDragState(newDragState);
    
    // 记录初始位置
    lastPositionRef.current = { x: rect.left, y: rect.top };
    
    console.log('[DRAG] 🎯 Drag started at:', { x: clientX, y: clientY });
    console.log('[DRAG] 🎯 Element offset:', newDragState.offset);
    
    // 添加全局事件监听器
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
    
    if (enableTouch) {
      document.addEventListener('touchmove', handleDragMove, { passive: false });
      document.addEventListener('touchend', handleDragEnd);
    }
    
    // 触发回调
    onDragStart?.(lastPositionRef.current);
  }, [elementRef, getEventCoordinates, enableTouch, onDragStart]);

  /**
   * 拖拽移动
   */
  const handleDragMove = useCallback((event: MouseEvent | TouchEvent) => {
    if (!dragState.isDragging) return;
    
    event.preventDefault();
    
    const { clientX, clientY } = getEventCoordinates(event);
    
    // 计算新位置
    let newX = clientX - dragState.offset.x;
    let newY = clientY - dragState.offset.y;
    
    // 应用约束
    const constrainedPosition = applyConstraints({ x: newX, y: newY });
    
    // 计算拖拽距离
    const distance = Math.sqrt(
      Math.pow(clientX - dragState.startPosition.x, 2) +
      Math.pow(clientY - dragState.startPosition.y, 2)
    );
    
    // 更新拖拽状态
    setDragState(prev => ({
      ...prev,
      dragDistance: distance
    }));
    
    // 平滑动画更新
    if (enableAnimation && animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    const updatePosition = () => {
      lastPositionRef.current = constrainedPosition;
      onDragMove?.(constrainedPosition);
      onPositionChange?.(constrainedPosition);
    };
    
    if (enableAnimation) {
      animationFrameRef.current = requestAnimationFrame(updatePosition);
    } else {
      updatePosition();
    }
    
    // 调试日志（节流）
    if (distance % 20 < 1) { // 每20像素记录一次
      console.log('[DRAG] 🎯 Dragging to:', constrainedPosition, 'distance:', distance.toFixed(1));
    }
  }, [dragState, getEventCoordinates, applyConstraints, enableAnimation, onDragMove, onPositionChange]);

  /**
   * 结束拖拽
   */
  const handleDragEnd = useCallback((_event: MouseEvent | TouchEvent) => {
    if (!dragState.isDragging) return;
    
    console.log('[DRAG] 🎯 Drag ended, total distance:', dragState.dragDistance.toFixed(1));
    
    // 清理动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
    
    // 移除全局事件监听器
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
    document.removeEventListener('touchmove', handleDragMove);
    document.removeEventListener('touchend', handleDragEnd);
    
    // 重置拖拽状态
    setDragState(prev => ({
      ...prev,
      isDragging: false,
      dragDistance: 0
    }));
    
    // 触发结束回调
    onDragEnd?.(lastPositionRef.current);
  }, [dragState.isDragging, dragState.dragDistance, handleDragMove, onDragEnd]);

  /**
   * 程序化设置位置
   */
  const setPosition = useCallback((position: ToolbarPosition) => {
    const constrainedPosition = applyConstraints(position);
    lastPositionRef.current = constrainedPosition;
    onPositionChange?.(constrainedPosition);
    
    console.log('[DRAG] 🎯 Position set programmatically:', constrainedPosition);
  }, [applyConstraints, onPositionChange]);

  /**
   * 切换边界约束
   */
  const toggleBoundaryConstraints = useCallback((enabled: boolean) => {
    setDragState(prev => ({
      ...prev,
      constrainToBounds: enabled
    }));
    
    console.log('[DRAG] 🎯 Boundary constraints:', enabled ? 'enabled' : 'disabled');
  }, []);

  /**
   * 获取当前位置
   */
  const getCurrentPosition = useCallback((): ToolbarPosition => {
    return lastPositionRef.current;
  }, []);

  // 清理效果
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      // 清理事件监听器
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
      document.removeEventListener('touchmove', handleDragMove);
      document.removeEventListener('touchend', handleDragEnd);
    };
  }, [handleDragMove, handleDragEnd]);

  return {
    // 状态
    isDragging: dragState.isDragging,
    dragDistance: dragState.dragDistance,
    
    // 事件处理器
    handleDragStart,
    
    // 工具函数
    setPosition,
    getCurrentPosition,
    toggleBoundaryConstraints,
    
    // 约束信息
    isConstrained: dragState.constrainToBounds
  };
};
