import React, { useState, useEffect } from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import ScreenshotEditor from "./screenshot-editor";


// 立即输出初始化日志
console.log('[MAIN] 🎯 main.tsx loaded');
console.log('[MAIN] 🎯 Tauri available:', !!window.__TAURI__);
console.log('[MAIN] 🎯 Current URL:', window.location.href);
console.log('[MAIN] 🎯 Current hash:', window.location.hash);

// 路由组件，支持动态路由切换
const Router: React.FC = () => {
  const [currentRoute, setCurrentRoute] = useState(window.location.hash);

  useEffect(() => {
    console.log(`[ROUTER] 🎯 Router component mounted, initial hash: ${window.location.hash}`);

    const handleHashChange = () => {
      console.log(`[ROUTER] 🔄 Hash changed to: ${window.location.hash}`);
      setCurrentRoute(window.location.hash);
    };

    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  useEffect(() => {
    console.log(`[ROUTER] 🎯 Current route state: ${currentRoute}`);
  }, [currentRoute]);

  if (currentRoute === '#/screenshot-editor') {
    console.log('[ROUTER] 🎯 Rendering ScreenshotEditor component');
    return <ScreenshotEditor />;
  } else {
    console.log('[ROUTER] 🎯 Rendering App component');
    return <App />;
  }
};

// 标记React应用已加载
(window as any).__MECAP_REACT_LOADED__ = true;
console.log('[MAIN] 🎯 React app is being rendered');

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <Router />
  </React.StrictMode>,
);

console.log('[MAIN] 🎯 React app render completed');
