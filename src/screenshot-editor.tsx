import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { theme } from './theme';
import ScreenshotResultOverlay from './components/ScreenshotResultOverlay';
import { listen } from '@tauri-apps/api/event';
import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow';
import { invoke } from '@tauri-apps/api/core';
import { info, error, debug } from '@tauri-apps/plugin-log';
import { convertFileSrc } from '@tauri-apps/api/core';

interface ScreenshotData {
  x: number;
  y: number;
  width: number;
  height: number;
  base64?: string; // For backward compatibility
  filePath?: string; // For file-based approach
  timestamp?: number;
}

function ScreenshotEditor() {
  const [screenshotData, setScreenshotData] = React.useState<ScreenshotData | null>(null);
  const [visible, setVisible] = React.useState(false);

  // 立即输出组件加载日志
  React.useEffect(() => {
    console.log('[SCREENSHOT-EDITOR] 🎯 ScreenshotEditor component mounted');
    console.log(`[SCREENSHOT-EDITOR] 🎯 Current URL: ${window.location.href}`);
    console.log(`[SCREENSHOT-EDITOR] 🎯 Current hash: ${window.location.hash}`);
  }, []);

  React.useEffect(() => {
    info('[SCREENSHOT-EDITOR] 🚀 Screenshot editor window initialized');
    info(`[SCREENSHOT-EDITOR] 🔧 Tauri available: ${!!window.__TAURI__}`);
    info(`[SCREENSHOT-EDITOR] 🔧 Event API available: ${!!window.__TAURI__?.event}`);

    // 监听截图数据事件 - 支持新的文件基础方法和旧的base64方法
    const setupListener = async () => {
      try {
        info('[SCREENSHOT-EDITOR] 🔧 Setting up file-based screenshot event listener...');

        // 监听新的文件基础事件
        const unlistenLoadScreenshot = await listen<{filePath: string, width: number, height: number, x: number, y: number, timestamp: number}>('load-screenshot', async (event) => {
          info('[SCREENSHOT-EDITOR] 📸 File-based screenshot data received!');
          info(`[SCREENSHOT-EDITOR] 📸 Event payload: ${JSON.stringify(event.payload)}`);

          try {
            // 将文件路径转换为可访问的URL
            const imageUrl = convertFileSrc(event.payload.filePath);
            info(`[SCREENSHOT-EDITOR] 📸 Converted file path to URL: ${imageUrl}`);

            // 创建ScreenshotData对象
            const screenshotData: ScreenshotData = {
              x: event.payload.x,
              y: event.payload.y,
              width: event.payload.width,
              height: event.payload.height,
              filePath: event.payload.filePath,
              base64: imageUrl, // 使用转换后的URL作为图像源
              timestamp: event.payload.timestamp
            };

            console.log('[SCREENSHOT-EDITOR] 📸 Created screenshot data object:', {
              x: screenshotData.x,
              y: screenshotData.y,
              width: screenshotData.width,
              height: screenshotData.height,
              filePath: screenshotData.filePath,
              imageUrl: imageUrl,
              hasBase64: !!screenshotData.base64,
              base64Value: screenshotData.base64
            });

            setScreenshotData(screenshotData);
            setVisible(true);

            console.log('[SCREENSHOT-EDITOR] 📸 State updated - screenshotData set and visible=true');

            info('[SCREENSHOT-EDITOR] ✅ File-based screenshot data loaded successfully');
          } catch (err) {
            error(`[SCREENSHOT-EDITOR] ❌ Failed to process file-based screenshot: ${err}`);
          }
        });

        // 保持对旧事件的兼容性
        const unlistenScreenshotData = await listen<ScreenshotData>('screenshot-data', (event) => {
          info('[SCREENSHOT-EDITOR] 📸 Legacy screenshot data received!');
          info(`[SCREENSHOT-EDITOR] 📸 Event payload: ${JSON.stringify(event.payload)}`);

          setScreenshotData(event.payload);
          setVisible(true);

          info('[SCREENSHOT-EDITOR] ✅ Legacy screenshot data state updated');
        });

        info('[SCREENSHOT-EDITOR] ✅ Event listeners setup completed');

        return () => {
          unlistenLoadScreenshot();
          unlistenScreenshotData();
        };
      } catch (err) {
        error(`[SCREENSHOT-EDITOR] ❌ Failed to setup event listener: ${err}`);
        return null;
      }
    };

    const unlistenPromise = setupListener();

    // 监听ESC键退出
    const handleKeyDown = (event: KeyboardEvent) => {
      debug(`[SCREENSHOT-EDITOR] ⌨️ Key pressed: ${event.key}, Code: ${event.code}`);
      if (event.key === 'Escape') {
        info('[SCREENSHOT-EDITOR] 🚪 ESC key detected, closing editor');
        event.preventDefault();
        event.stopPropagation();
        handleCancel();
      }
    };

    info('[SCREENSHOT-EDITOR] 🔧 Adding keydown event listener');
    document.addEventListener('keydown', handleKeyDown);

    // 确保窗口获得焦点
    window.focus();
    info('[SCREENSHOT-EDITOR] 🔧 Window focus requested');

    return () => {
      unlistenPromise.then((fn) => {
        if (fn) fn();
      });
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const handleComplete = async (savedPath?: string) => {
    info(`[SCREENSHOT-EDITOR] ✅ Screenshot editing completed: ${savedPath}`);
    await closeEditor();
  };

  const handleCancel = async () => {
    info('[SCREENSHOT-EDITOR] ❌ Screenshot editing cancelled');
    await closeEditor();
  };

  const closeEditor = async () => {
    try {
      info('[SCREENSHOT-EDITOR] 🔧 Starting editor close process...');

      // 先显示主窗口
      info('[SCREENSHOT-EDITOR] 🔧 Showing main window...');
      await invoke('show_main_window');
      info('[SCREENSHOT-EDITOR] ✅ Main window shown');

      // 然后关闭当前编辑器窗口
      info('[SCREENSHOT-EDITOR] 🔧 Closing editor window...');
      const currentWindow = getCurrentWebviewWindow();
      await currentWindow.close();

      info('[SCREENSHOT-EDITOR] ✅ Editor window closed and main window restored');
    } catch (err) {
      error(`[SCREENSHOT-EDITOR] ❌ Failed to close editor: ${err}`);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          width: '100vw',
          height: '100vh',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {screenshotData && screenshotData.base64 ? (
          <>
            {console.log('[SCREENSHOT-EDITOR] 🔧 Rendering ScreenshotResultOverlay with data:', {
              hasBase64: !!screenshotData.base64,
              base64Preview: screenshotData.base64.substring(0, 50) + '...',
              width: screenshotData.width,
              height: screenshotData.height,
              x: screenshotData.x,
              y: screenshotData.y,
              visible: visible
            })}
            <ScreenshotResultOverlay
              visible={visible}
              screenshotData={{
                base64: screenshotData.base64,
                width: screenshotData.width,
                height: screenshotData.height,
                x: screenshotData.x,
                y: screenshotData.y
              }}
              onComplete={handleComplete}
              onCancel={handleCancel}
            />
          </>
        ) : (
          <>
            {console.log('[SCREENSHOT-EDITOR] ❌ Not rendering overlay - missing data:', {
              hasScreenshotData: !!screenshotData,
              hasBase64: !!screenshotData?.base64,
              visible: visible
            })}
            <div style={{ color: 'white', padding: '20px' }}>
              Waiting for screenshot data...
            </div>
          </>
        )}
      </Box>
    </ThemeProvider>
  );
}

export default ScreenshotEditor;
