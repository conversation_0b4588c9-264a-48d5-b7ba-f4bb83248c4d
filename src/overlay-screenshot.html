<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mecap Screenshot Overlay</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.3);
            cursor: crosshair;
            overflow: hidden;
            user-select: none;
            -webkit-user-select: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .window-highlight {
            position: absolute;
            border: 2px solid #00ff00;
            background: rgba(0, 255, 0, 0.1);
            pointer-events: none;
            z-index: 999;
            transition: all 0.1s ease;
        }

        .selection-area {
            position: absolute;
            border: 2px solid #007AFF;
            background: rgba(0, 122, 255, 0.1);
            pointer-events: none;
            z-index: 998;
        }

        .toolbar {
            position: absolute;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            display: none;
            z-index: 1001;
            backdrop-filter: blur(10px);
        }

        .toolbar-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            margin: 0 2px;
            border: none;
            border-radius: 6px;
            background: transparent;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.2s ease;
        }

        .toolbar-button:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .toolbar-button.primary {
            background: #007AFF;
            color: white;
        }

        .toolbar-button.primary:hover {
            background: #0056CC;
        }

        .toolbar-button.danger {
            background: #FF3B30;
            color: white;
        }

        .toolbar-button.danger:hover {
            background: #D70015;
        }

        .coordinates-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: 'Monaco', 'Menlo', monospace;
            z-index: 1002;
            display: none;
        }

        .help-text {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            text-align: center;
            z-index: 1002;
        }

        .monitor-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1002;
            display: none;
        }

        /* 深色主题适配 */
        @media (prefers-color-scheme: dark) {
            .toolbar {
                background: rgba(45, 45, 45, 0.95);
                color: white;
            }
            
            .toolbar-button:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    
    <div class="coordinates-info" id="coordinates">
        Position: (0, 0) | Size: 0×0
    </div>
    
    <div class="monitor-info" id="monitor-info">
        Monitor: 1 | Scale: 1.0x
    </div>
    
    <div class="toolbar" id="toolbar">
        <button class="toolbar-button primary" id="confirm-btn" title="确认截图 (Enter)">✓</button>
        <button class="toolbar-button danger" id="cancel-btn" title="取消 (Esc)">✕</button>
        <button class="toolbar-button" id="edit-btn" title="编辑 (E)">✏</button>
        <button class="toolbar-button" id="copy-btn" title="复制 (Ctrl+C)">📋</button>
        <button class="toolbar-button" id="save-btn" title="保存 (Ctrl+S)">💾</button>
        <button class="toolbar-button" id="more-btn" title="更多选项">⋯</button>
    </div>
    
    <div class="help-text" id="help-text">
        拖拽选择区域，点击窗口快速截图，或按 ESC 取消
    </div>

    <script type="module">
        import { invoke } from '/src/lib/tauri.js';
        
        class ScreenshotOverlay {
            constructor() {
                this.canvas = document.getElementById('canvas');
                this.ctx = this.canvas.getContext('2d');
                this.toolbar = document.getElementById('toolbar');
                this.coordinatesInfo = document.getElementById('coordinates');
                this.monitorInfo = document.getElementById('monitor-info');
                this.helpText = document.getElementById('help-text');
                
                this.isSelecting = false;
                this.startPoint = null;
                this.currentSelection = null;
                this.currentWindow = null;
                this.monitorData = null;
                
                this.setupCanvas();
                this.bindEvents();
                this.setupToolbar();
                this.loadMonitorInfo();
            }
            
            setupCanvas() {
                const dpr = window.devicePixelRatio || 1;
                this.canvas.width = window.innerWidth * dpr;
                this.canvas.height = window.innerHeight * dpr;
                this.canvas.style.width = window.innerWidth + 'px';
                this.canvas.style.height = window.innerHeight + 'px';
                this.ctx.scale(dpr, dpr);
            }
            
            async loadMonitorInfo() {
                try {
                    // 监听来自后端的显示器信息
                    const { listen } = await import('/src/lib/tauri.js');
                    await listen('monitor-info', (event) => {
                        this.monitorData = event.payload;
                        this.updateMonitorInfo();
                    });
                } catch (error) {
                    console.error('Failed to load monitor info:', error);
                }
            }
            
            updateMonitorInfo() {
                if (this.monitorData) {
                    this.monitorInfo.textContent = 
                        `Monitor: ${this.monitorData.index + 1} | Scale: ${this.monitorData.scale_factor.toFixed(1)}x`;
                    this.monitorInfo.style.display = 'block';
                }
            }
            
            bindEvents() {
                // 鼠标事件
                this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
                this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
                this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
                this.canvas.addEventListener('mouseleave', this.onMouseLeave.bind(this));
                
                // 键盘事件
                document.addEventListener('keydown', this.onKeyDown.bind(this));
                
                // 窗口事件
                window.addEventListener('resize', this.onResize.bind(this));
            }
            
            setupToolbar() {
                document.getElementById('confirm-btn').addEventListener('click', () => this.confirmCapture());
                document.getElementById('cancel-btn').addEventListener('click', () => this.cancelCapture());
                document.getElementById('edit-btn').addEventListener('click', () => this.openEditor());
                document.getElementById('copy-btn').addEventListener('click', () => this.copyToClipboard());
                document.getElementById('save-btn').addEventListener('click', () => this.saveToFile());
                document.getElementById('more-btn').addEventListener('click', () => this.showMoreOptions());
            }
            
            async onMouseMove(event) {
                const x = event.clientX;
                const y = event.clientY;
                
                this.updateCoordinates(x, y);
                
                if (this.isSelecting) {
                    this.updateSelection(event);
                } else {
                    // 检测窗口
                    await this.detectAndHighlightWindow(x, y);
                }
            }
            
            updateCoordinates(x, y) {
                const size = this.currentSelection ? 
                    `${Math.abs(this.currentSelection.width)}×${Math.abs(this.currentSelection.height)}` : '0×0';
                this.coordinatesInfo.textContent = `Position: (${x}, ${y}) | Size: ${size}`;
                this.coordinatesInfo.style.display = 'block';
            }
            
            async detectAndHighlightWindow(x, y) {
                try {
                    const windowInfo = await invoke('detect_window_under_mouse', { x, y });
                    if (windowInfo && windowInfo !== this.currentWindow) {
                        this.currentWindow = windowInfo;
                        this.highlightWindow(windowInfo);
                    }
                } catch (error) {
                    // 窗口检测失败是正常的，不显示错误
                    this.clearHighlight();
                }
            }
            
            highlightWindow(windowInfo) {
                this.clearCanvas();
                
                // 绘制窗口高亮
                this.ctx.strokeStyle = '#00ff00';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(windowInfo.x, windowInfo.y, windowInfo.width, windowInfo.height);
                
                this.ctx.fillStyle = 'rgba(0, 255, 0, 0.1)';
                this.ctx.fillRect(windowInfo.x, windowInfo.y, windowInfo.width, windowInfo.height);
            }
            
            onMouseDown(event) {
                if (event.button === 0) { // 左键
                    this.isSelecting = true;
                    this.startPoint = { x: event.clientX, y: event.clientY };
                    this.clearCanvas();
                    this.hideToolbar();
                }
            }
            
            onMouseUp(event) {
                if (this.isSelecting) {
                    this.isSelecting = false;
                    const endPoint = { x: event.clientX, y: event.clientY };
                    
                    if (this.startPoint) {
                        this.completeSelection(this.startPoint, endPoint);
                    }
                } else if (this.currentWindow) {
                    // 点击窗口进行窗口截图
                    this.captureWindow(this.currentWindow);
                }
            }
            
            updateSelection(event) {
                if (!this.startPoint) return;
                
                const currentPoint = { x: event.clientX, y: event.clientY };
                const selection = this.calculateSelection(this.startPoint, currentPoint);
                
                this.currentSelection = selection;
                this.drawSelection(selection);
            }
            
            calculateSelection(start, end) {
                return {
                    x: Math.min(start.x, end.x),
                    y: Math.min(start.y, end.y),
                    width: Math.abs(end.x - start.x),
                    height: Math.abs(end.y - start.y)
                };
            }
            
            drawSelection(selection) {
                this.clearCanvas();
                
                // 绘制选择区域
                this.ctx.strokeStyle = '#007AFF';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(selection.x, selection.y, selection.width, selection.height);
                
                this.ctx.fillStyle = 'rgba(0, 122, 255, 0.1)';
                this.ctx.fillRect(selection.x, selection.y, selection.width, selection.height);
            }
            
            completeSelection(start, end) {
                const selection = this.calculateSelection(start, end);
                
                if (selection.width > 10 && selection.height > 10) {
                    this.currentSelection = selection;
                    this.showToolbar(selection);
                } else {
                    this.clearCanvas();
                }
            }
            
            showToolbar(selection) {
                const toolbarWidth = this.toolbar.offsetWidth;
                const toolbarHeight = this.toolbar.offsetHeight;
                
                // 计算工具栏位置
                let x = selection.x + selection.width / 2 - toolbarWidth / 2;
                let y = selection.y + selection.height + 10;
                
                // 边界检查
                if (x < 10) x = 10;
                if (x + toolbarWidth > window.innerWidth - 10) {
                    x = window.innerWidth - toolbarWidth - 10;
                }
                if (y + toolbarHeight > window.innerHeight - 10) {
                    y = selection.y - toolbarHeight - 10;
                }
                
                this.toolbar.style.left = x + 'px';
                this.toolbar.style.top = y + 'px';
                this.toolbar.style.display = 'block';
                
                this.helpText.style.display = 'none';
            }
            
            hideToolbar() {
                this.toolbar.style.display = 'none';
                this.helpText.style.display = 'block';
            }
            
            clearCanvas() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            }
            
            clearHighlight() {
                this.currentWindow = null;
                this.clearCanvas();
            }
            
            async confirmCapture() {
                if (this.currentSelection) {
                    await this.captureRegion(this.currentSelection);
                }
            }
            
            async captureRegion(selection) {
                try {
                    // 转换为全局坐标
                    const region = {
                        x: selection.x + (this.monitorData ? this.monitorData.x : 0),
                        y: selection.y + (this.monitorData ? this.monitorData.y : 0),
                        width: selection.width,
                        height: selection.height,
                        monitor_index: this.monitorData ? this.monitorData.index : 0
                    };

                    const result = await invoke('capture_region_from_overlay', { region });
                    console.log('Capture successful:', result);
                    // 🔧 CRITICAL FIX: 不在前端关闭覆盖层，让后端统一处理清理和预览窗口创建
                    // await this.closeOverlay(); // 移除这行，避免与后端清理逻辑冲突
                } catch (error) {
                    console.error('Capture failed:', error);
                    this.showError('截图失败: ' + error);
                }
            }
            
            async captureWindow(windowInfo) {
                try {
                    const region = {
                        x: windowInfo.x,
                        y: windowInfo.y,
                        width: windowInfo.width,
                        height: windowInfo.height,
                        monitor_index: this.monitorData ? this.monitorData.index : 0
                    };

                    const result = await invoke('capture_region_from_overlay', { region });
                    console.log('Window capture successful:', result);
                    // 🔧 CRITICAL FIX: 不在前端关闭覆盖层，让后端统一处理清理和预览窗口创建
                    // await this.closeOverlay(); // 移除这行，避免与后端清理逻辑冲突
                } catch (error) {
                    console.error('Window capture failed:', error);
                    this.showError('窗口截图失败: ' + error);
                }
            }
            
            async cancelCapture() {
                await this.closeOverlay();
            }
            
            async closeOverlay() {
                try {
                    await invoke('close_hybrid_screenshot');
                } catch (error) {
                    console.error('Failed to close overlay:', error);
                }
            }
            
            async openEditor() {
                // TODO: 实现编辑器功能
                console.log('Open editor');
            }
            
            async copyToClipboard() {
                // TODO: 实现复制到剪贴板
                console.log('Copy to clipboard');
            }
            
            async saveToFile() {
                // TODO: 实现保存文件
                console.log('Save to file');
            }
            
            showMoreOptions() {
                // TODO: 实现更多选项
                console.log('Show more options');
            }
            
            showError(message) {
                // TODO: 实现错误提示
                console.error(message);
            }
            
            onKeyDown(event) {
                switch (event.key) {
                    case 'Escape':
                        this.cancelCapture();
                        break;
                    case 'Enter':
                        if (this.currentSelection) {
                            this.confirmCapture();
                        }
                        break;
                    case 'e':
                    case 'E':
                        if (this.currentSelection) {
                            this.openEditor();
                        }
                        break;
                }
                
                // Ctrl+C
                if (event.ctrlKey && event.key === 'c') {
                    this.copyToClipboard();
                }
                
                // Ctrl+S
                if (event.ctrlKey && event.key === 's') {
                    event.preventDefault();
                    this.saveToFile();
                }
            }
            
            onMouseLeave() {
                this.clearHighlight();
            }
            
            onResize() {
                this.setupCanvas();
            }
        }
        
        // 初始化覆盖层
        document.addEventListener('DOMContentLoaded', () => {
            new ScreenshotOverlay();
        });
    </script>
</body>
</html>
