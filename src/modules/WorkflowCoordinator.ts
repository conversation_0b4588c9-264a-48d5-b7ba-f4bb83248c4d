import { EditorMode } from '../types/editor';
import { workflowEventBus } from './WorkflowEventBus.ts';
import { showWarning } from '../utils/notification.ts';
import { useEditorStore } from '../store/editorStore';
import { captureScreenshot, cancelScreenshot } from '../components/ScreenshotCapture';

// 状态超时时间（毫秒）
const STATE_TIMEOUT = 10000;

class WorkflowCoordinator {
  private stateTimer: ReturnType<typeof setTimeout> | null = null;
  
  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // 窗口选择事件
    workflowEventBus.on('window-selected', this.handleWindowSelected);
    
    // 区域选择事件
    workflowEventBus.on('region-selected', this.handleRegionSelected);
    
    // 工具栏事件
    workflowEventBus.on('toolbar-save', this.handleSave);
    workflowEventBus.on('toolbar-cancel', this.handleCancel);
    
    // 键盘事件
    document.addEventListener('keydown', this.handleKeyDown);
  }

  private resetTimer() {
    if (this.stateTimer) {
      clearTimeout(this.stateTimer);
    }
    this.stateTimer = setTimeout(() => {
      this.handleTimeout();
    }, STATE_TIMEOUT);
  }

  // 处理窗口选择事件
  private handleWindowSelected = () => {
    const { mode, setMode } = useEditorStore.getState();
    if (mode === EditorMode.WindowDetection) {
      setMode(EditorMode.Editing);
      this.resetTimer();
    } else {
      showWarning('无效状态转换：仅能在窗口检测模式切换到编辑模式');
    }
  };

  // 处理区域选择事件
  private handleRegionSelected = () => {
    const { mode, setMode } = useEditorStore.getState();
    if (mode === EditorMode.RegionSelection) {
      setMode(EditorMode.Editing);
      this.resetTimer();
    }
  };

  // 处理保存事件
  private handleSave = () => {
    const { mode } = useEditorStore.getState();
    if (mode === EditorMode.Editing) {
      captureScreenshot();
      this.resetTimer();
    }
  };

  // 处理取消事件
  private handleCancel = () => {
    const { mode, setMode } = useEditorStore.getState();
    if (mode === EditorMode.Editing) {
      cancelScreenshot();
      setMode(EditorMode.WindowDetection);
    }
  };

  // 处理键盘事件
  private handleKeyDown = (e: KeyboardEvent) => {
    const { mode, setMode } = useEditorStore.getState();
    if (e.code === 'Space' && mode === EditorMode.Editing) {
      setMode(EditorMode.RegionSelection);
      this.resetTimer();
      e.preventDefault();
    }
  };

  // 处理超时
  private handleTimeout = () => {
    const { setMode } = useEditorStore.getState();
    showWarning('操作超时，已重置流程');
    setMode(EditorMode.WindowDetection);
  };
}

export const workflowCoordinator = new WorkflowCoordinator();