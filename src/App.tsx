
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Container, AppBar, Toolbar, Typography, Box, IconButton, Tooltip } from '@mui/material';
import { Speed as SpeedIcon } from '@mui/icons-material';
import { theme } from './theme';
import { ScreenshotCapture } from './components/ScreenshotCapture';
import { RecentScreenshots } from './components/RecentScreenshots';
import { PerformanceMonitor } from './components/PerformanceMonitor';
// ScreenshotResultOverlay已移至独立的screenshot editor窗口
import { listen } from '@tauri-apps/api/event';
import { info } from '@tauri-apps/plugin-log';
import { useScreenshotStore } from './store/screenshotStore';

import { useState, useEffect } from 'react';

// WindowSelectedData接口已移至screenshot editor窗口

function App() {
  const [performanceMonitorOpen, setPerformanceMonitorOpen] = useState(false);
  const { addScreenshot } = useScreenshotStore();
  // 截图编辑状态已移至独立的screenshot editor窗口

  useEffect(() => {
    info('[APP] 🎯 App component mounted');
  }, []);

  // 监听截图保存事件，用于集成Recent Screenshots功能
  useEffect(() => {
    info('[APP] 🔧 Setting up screenshot-saved event listener...');

    const unlistenScreenshotSaved = listen<{
      path: string;
      width: number;
      height: number;
      size_bytes: number;
      timestamp: number;
      name: string;
    }>('screenshot-saved', (event) => {
      info('[APP] 📸 Screenshot-saved event received!');
      info(`[APP] 📸 Event data: path=${event.payload.path}, name=${event.payload.name}`);

      try {
        // 添加截图到store，这将自动更新Recent Screenshots显示
        addScreenshot({
          path: event.payload.path,
          width: event.payload.width,
          height: event.payload.height,
          tags: [],
          name: event.payload.name
        });

        info('[APP] ✅ Successfully added screenshot to store from event');
      } catch (error) {
        console.error('[APP] ❌ Failed to add screenshot to store:', error);
      }
    });

    return () => {
      unlistenScreenshotSaved.then(fn => fn());
    };
  }, [addScreenshot]);

  // 注意：移除了自动创建编辑器的window-selected事件监听
  // 现在截图完成后只显示预览窗口，用户双击预览窗口才会打开编辑器

  // 截图完成和取消处理已移至独立的screenshot editor窗口

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static" elevation={1}>
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Mecap - Screenshot Tool
            </Typography>
            <Tooltip title="性能监控">
              <IconButton
                color="inherit"
                onClick={() => setPerformanceMonitorOpen(true)}
              >
                <SpeedIcon />
              </IconButton>
            </Tooltip>
          </Toolbar>
        </AppBar>

        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            <ScreenshotCapture />
            <RecentScreenshots />
          </Box>
        </Container>

        {/* 性能监控对话框 */}
        <PerformanceMonitor
          open={performanceMonitorOpen}
          onClose={() => setPerformanceMonitorOpen(false)}
        />

        {/* 截图编辑功能已移至独立的screenshot editor窗口 */}
      </Box>
    </ThemeProvider>
  );
}

export default App;
